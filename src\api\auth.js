import http from '@/utils/http'

// 登录接口
export const login = (data) => {
  return http.post('/sys/login/meetingLogin', data)
}

// 发送邮箱验证码
export const sendEmailCode = (email) => {
  return http.get('/sys/login/sendEmailCode?email='+email)
}

// 注册接口
export const register = (data) => {
  return http.post('/meeting/user/userRegister', data)
}

// 用户登出
export function logout(data) {
  return http.post('/sys/login/meetingLogout',data)
}

// 获取用户信息
export function getUserInfo() {
  return http.get('/meeting/user/info')
}

// 发送重置密码验证码
export function sendResetEmailCode(email) {
  return http.get('/meeting/user/sendResetEmailCode?email='+email)
}

// 重置密码
export function resetPassword(data) {
  return http.post('/meeting/user/resetPassword', data)
}

// 更新个人资料
export const updateProfile = (data) => {
  return http.post('/meeting/user/updateMeetingUser', data)
}

// 修改密码
export const changePassword = (data) => {
  return http.post('/meeting/user/updatePassword', data)
}
export function sendEmailVerification(email) {
  return http.get('/meeting/user/sendEmailCode?email='+email)
}
// 密码过期接口
export function checkPasswordExpire(account) {
  return http.get('/meeting/user/passwordExpire?account='+account  )
}