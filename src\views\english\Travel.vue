<template>
  <div class="travel-page">
    <!-- 页面头部 -->
    <!-- <section class="page-header">
      <div class="container">
      <h1 class="animate__animated animate__fadeInDown">旅游服务</h1>
      <p class="animate__animated animate__fadeInUp">探索中国之美，体验文化之旅</p>
      </div>  
    </section> -->
    <section class="page-header">
      <!-- <h1 class="animate__animated animate__fadeInDown">travel&Partner Program</h1> -->
      <!-- <p class="animate__animated animate__fadeInUp">Explore the beauty of China and experience a cultural journey</p> -->
    </section>
    <div class="conference-overview-container">
    <!-- 主要内容区域 -->
    <section class="travel-section">
      <div class="travel-container">
    

        <!-- 热门景点 -->
        <div class="attractions-section animate__animated animate__fadeIn">
          <!-- <h2 class="animate__animated animate__fadeInLeft">热门景点</h2> -->
          <el-row :gutter="30">
            <el-col :span="24" v-for="(spot, index) in hotSpots" :key="spot.id" 
              :class="`animate__animated animate__fadeInUp`" 
              :style="{ animationDelay: `${index * 0.2}s` }">
              <el-card class="spot-card" :body-style="{ padding: '0px' }">
                <div class="spot-content">
                  <div class="spot-image-wrapper">
                    <img :src="spot.image" class="spot-image" />
                  </div>
                  <div class="spot-info">
                    <h3>{{ spot.name }}</h3>
                    <p class="spot-description">{{ spot.description }}</p>
                    <div class="spot-footer">
                      <el-button type="primary" size="small" @click="handleBook(spot.detail)">View Details</el-button>
                      <el-button type="primary" size="small" @click="handleBook(spot.paypal)">Book Now</el-button>
                    </div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <!-- 特色行程 -->
        <!-- <div class="tours-section" v-intersection-observer="handleToursIntersection">
          <h2 :class="{ 'animate__animated animate__fadeInLeft': toursVisible }">特色行程</h2>
          <el-timeline>
            <el-timeline-item
              v-for="(tour, index) in featuredTours"
              :key="tour.id"
              :timestamp="tour.duration"
              placement="top"
              :class="{ 'animate__animated animate__fadeInRight': toursVisible }"
              :style="{ animationDelay: `${index * 0.3}s` }"
            >
              <el-card>
                <h3>{{ tour.name }}</h3>
                <p>{{ tour.description }}</p>
                <div class="tour-tags">
                  <el-tag 
                    v-for="(tag, tagIndex) in tour.tags" 
                    :key="tag" 
                    size="small"
                    :class="{ 'animate__animated animate__fadeIn': toursVisible }"
                    :style="{ animationDelay: `${(index * 0.3 + tagIndex * 0.1)}s` }"
                  >
                    {{ tag }}
                  </el-tag>
                </div>
                <div class="tour-footer">
                  <span class="price">¥{{ tour.price }}起</span>
                  <el-button 
                    type="primary" 
                    size="small" 
                    @click="handleBookTour(tour)"
                    :class="{ 'animate__animated animate__pulse animate__infinite': toursVisible }"
                  >
                    查看详情
                  </el-button>
                </div>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </div> -->
      </div>
    </section>

    <!-- 预订弹窗 -->
    <el-dialog
      v-model="bookingDialogVisible"
      :title="selectedSpot ? '景点预订' : '行程预订'"
      width="500px"
      class="animate__animated animate__zoomIn"
    >
      <el-form :model="bookingForm" label-width="100px">
        <el-form-item label="出行日期">
          <el-date-picker
            v-model="bookingForm.date"
            type="date"
            placeholder="选择日期"
          />
        </el-form-item>
        <el-form-item label="人数">
          <el-input-number v-model="bookingForm.people" :min="1" :max="10" />
        </el-form-item>
        <el-form-item label="联系人">
          <el-input v-model="bookingForm.contact" />
        </el-form-item>
        <el-form-item label="联系电话">
          <el-input v-model="bookingForm.phone" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="bookingDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitBooking">确认预订</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import travel1 from '@/assets/images/trvale1.jpg'
import travel2 from '@/assets/images/trvale2.jpg'
import travel3 from '@/assets/images/trvale3.jpg'
import travel4 from '@/assets/images/trvale4.jpg'
import 'animate.css'

export default {
  name: 'TravelPage',
  directives: {
    intersectionObserver: {
      mounted(el, binding) {
        const observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            // 每次进入视口时都触发动画
            if (entry.isIntersecting) {
              binding.value(true)
            } else {
              // 离开视口时重置状态，为下次进入做准备
              binding.value(false)
            }
          })
        }, {
          threshold: 0.1, // 当元素10%进入视口时触发
          rootMargin: '0px' // 可以调整触发时机
        })
        observer.observe(el)
      },
      // 组件更新时重新观察
      updated(el, binding) {
        const observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              binding.value(true)
            } else {
              binding.value(false)
            }
          })
        }, {
          threshold: 0.1,
          rootMargin: '0px'
        })
        observer.observe(el)
      }
    }
  },
  setup() {
    // 搜索表单
    const searchForm = reactive({
      destination: '',
      date: [],
      people: 1
    })

    // 热门景点数据
    const hotSpots = ref([
      {
        id: 1,
        name: 'Jiangnan Water Charm:Zhujiajiao Ancient Grace Tour',
        description: 'Zhujiajiao Ancient Town, a poetic scroll of the Jiangnan water town. With a history spanning 1,700 years, Kezhi Garden is a perfect blend of Chinese and Western styles, exuding an enduring ancient charm. Take a boat ride on the waterways to quietly enjoy the tranquil moments of Jiangnan. The Silk Exhibition Hall is a testament to the brilliant craftsmanship of Chinese civilization.',
        image: travel1,
        detail:'https://www.ourtour.com/zl/product?channelId=1&productId=3500059397',
        paypal:'https://www.paypal.com/ncp/payment/UZW5W5Z4TNMY4',
        price: 100
      },
      {
        id: 2,
        name: 'Shanghai Classic Splendor Tour',
        description: 'Stroll along the Bund, where the foreign-style architecture by the Huangpu River tells the stories of a century of changes. Step into the Yu Garden, and you will be captivated by the exquisite and elegant charm of the classical Jiangnan gardens. At the Silk Exhibition Hall, you can appreciate the great wisdom and superb skills of ancient Chinese working people. Finally, enjoy a Shanghai specialty lunch to savor the unique flavor and cultural depth of this city.',
        image: travel2,
        detail:'https://www.ourtour.com/zl/product?channelId=1&productId=3500059423',
        paypal:'https://www.paypal.com/ncp/payment/C2JQSKUDZYZGY',
        price: 60
      },
      {
        id: 3,
        name: 'Hangzhou & Suzhou: A Journey Through Jiangnan Charm',
        description: `The lingering chimes of Lingyin Temple embody a thousand years of Buddhist serenity; West Lake, with its rippling waters and hazy hills, creates a scenic blend of nature and culture. The Humble Administrator's Garden features a different view at every turn, epitomizing the grace of Jiangnan water towns. Shantang Street, exuding a vintage charm, brims with the essence of daily life and historical depth.`,
        image: travel3,
        detail:'https://www.ourtour.com/zl/product?channelId=1&productId=3500059427',
        paypal:'https://www.paypal.com/ncp/payment/J4WYJ6B8R7YS8',
        price: 80
      },
      {
        id: 4,
        name: `Xi'an & Beijing: Essence of Chinese Civilization Tour`,
        description: `Embark on a cultural adventure from ancient Xi'an to modern Beijing. In Xi'an, explore the majestic city walls, marvel at the Terracotta Army, and experience the solemnity of the Great Wild Goose Pagoda. In Beijing, witness the grandeur of Tiananmen Square, the splendor of the Forbidden City, and the beauty of the Summer Palace and Great Wall. This journey is a dialogue between soul and history, showcasing the timeless beauty of China.`,
        image: travel4,
        detail:'https://www.ourtour.com/zl/product?channelId=1&productId=3500059424',
        paypal:'https://www.paypal.com/ncp/payment/RE87VDBQ9ZU8E',
        price: 90
      }

    ])

    // 特色行程数据
    const featuredTours = ref([
      {
        id: 1,
        name: '江南水乡三日游',
        description: '游览周庄、朱家角、乌镇等江南水乡古镇，体验江南水乡文化',
        duration: '3天2晚',
        price: 1999,
        tags: ['文化之旅', '古镇体验', '江南风情']
      },
      {
        id: 2,
        name: '崇明生态之旅',
        description: '探索崇明岛生态保护区，观鸟、赏花、亲近自然',
        duration: '2天1晚',
        price: 1299,
        tags: ['生态旅游', '自然探索', '观鸟摄影']
      },
      {
        id: 3,
        name: '苏州园林文化游',
        description: '游览苏州著名园林，感受江南园林艺术的精髓',
        duration: '2天1晚',
        price: 1599,
        tags: ['文化体验', '园林艺术', '历史探索']
      }
    ])

    // 预订相关
    const bookingDialogVisible = ref(false)
    const selectedSpot = ref(null)
    const bookingForm = reactive({
      date: '',
      people: 1,
      contact: '',
      phone: ''
    })

    // 添加特色行程可见性状态
    const toursVisible = ref(false)

    // 处理特色行程部分的交叉观察
    const handleToursIntersection = (isVisible) => {
      toursVisible.value = isVisible
    }

    // 搜索处理
    const handleSearch = () => {
      // 实现搜索逻辑
      console.log('搜索条件：', searchForm)
    }

    // 景点预订
    const handleBook = (url) => {
      // selectedSpot.value = spot
      // bookingDialogVisible.value = true
    
        window.open(url,'_blank')
    
    }

    // 行程预订
    const handleBookTour = (tour) => {
      selectedSpot.value = null
      bookingDialogVisible.value = true
    }

    // 提交预订
    const submitBooking = () => {
      // 实现预订逻辑
      ElMessage.success('预订成功！')
      bookingDialogVisible.value = false
    }

    return {
      searchForm,
      hotSpots,
      featuredTours,
      bookingDialogVisible,
      selectedSpot,
      bookingForm,
      handleSearch,
      handleBook,
      handleBookTour,
      submitBooking,
      toursVisible,
      handleToursIntersection
    }
  }
}
</script>

<style scoped>
@import url('@/assets/css/travel.css');
</style>
