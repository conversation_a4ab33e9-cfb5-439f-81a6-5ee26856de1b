import { defineConfig } from 'vite'
import { resolve } from 'path'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  base: '/cec-meeting-web',
  plugins: [vue()],
  // publicPath: '/cec-meeting-web',
  server: {
    host: '0.0.0.0',  
    proxy: {
      '/oa/cec-api': {
        target: 'https://scrm.sino-bridge.com:8098/oa/cec-api', 
        // target: 'http://svy5ir.natappfree.cc', 
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/oa\/cec-api/, '')
      }
    }
  },
  assetsInclude: ['**/*.doc', '**/*.docx'],
  resolve: {
    alias: {
      '@': resolve(__dirname, './src')
    }
  },
  define: {
    'import.meta.env.PROD': JSON.stringify(process.env.NODE_ENV === 'production')
  }
})
