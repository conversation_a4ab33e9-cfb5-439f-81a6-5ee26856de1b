<template>
  <header class="main-header" :class="{ 'scrolled': isScrolled }">
    <div class="container">
      <div class="logo">
        <!-- <router-link to="/chinese/home"> -->
          <img src="../../assets/images/logo-big.png" alt="ICMCI 2025" class="logo-big" @click="goCechandler"/>
        <!-- </router-link> -->
        <router-link :to="getLocalePath('/home')">
          <img src="../../assets/images/logo.png" alt="ICMCI 2025" style="transform: scale(1.2);margin-top: 5px;"/>
        </router-link>
      
      </div>
      
      <nav class="main-nav">
        <ul>
          <li v-for="item in navItems" :key="item.path">
            <template v-if="item.children">
              <div class="nav-item-with-dropdown">
                <router-link :to="item.path" :class="{ 'active': isActive(item.path) }">
                  {{ item.name }}
                </router-link>
                <div class="submenu">
                  <a 
                    v-for="child in item.children" 
                    :key="child.anchor"
                    href="javascript:void(0)"
                    @click="scrollToSection(child.anchor)"
                  >
                    {{ child.name }}
                  </a>
                </div>
              </div>
            </template>
            <template v-else>
              <router-link :to="item.path" :class="{ 'active': isActive(item.path) }">
                {{ item.name }}
              </router-link>
            </template>
          </li>
          <!-- <li class="lang-switch">
            <a href="javascript:void(0)" @click="switchLanguage">
              {{ currentLanguage === 'chinese' ? 'English' : '中文' }}
            </a>
          </li> -->
        </ul>
      </nav>
      
      <div class="user-actions">
        <template v-if="isLoggedIn">
          <el-dropdown>
            <span class="user-info">
              {{ userInfo.account }}
              <el-icon><arrow-down /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item>
                  <router-link :to="getLocalePath('/dashboard')" class="dropdown-link"> {{ currentLanguage === 'chinese' ? '个人中心' : 'Personal Center' }}</router-link>
                </el-dropdown-item>
                <!-- <el-dropdown-item>
                   <span class="dropdown-link" @click="switchLanguage">中文</span>
                </el-dropdown-item> -->
                <el-dropdown-item>
                  <span class="dropdown-link" @click="handleLogout">{{ currentLanguage === 'chinese' ? '退出登录' : 'Log out' }}</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
        <template v-else>
          <!-- <router-link to="/login" class="btn btn-outline">登录</router-link>
          <router-link to="/register" class="btn btn-primary">注册</router-link> -->
          <button class="btn btn-primary" @click="$router.push(getLocalePath('/login'))">
           {{ currentLanguage === 'chinese' ? '登录' : 'Log in' }}
          </button>
          <button class="btn btn-outline" @click="$router.push(getLocalePath('/register'))">
            {{ currentLanguage === 'chinese' ? '注册' : 'Register' }}
          </button>
        </template>
      </div>
      
      <div class="mobile-menu-toggle" @click="toggleMobileMenu">
        <span></span>
        <span></span>
        <span></span>
      </div>
    </div>
    
    <div class="mobile-menu" :class="{ 'active': isMobileMenuOpen }">
      <nav class="mobile-nav">
        <ul>
          <li v-for="item in navItems" :key="item.path">
            <router-link :to="item.path" :class="{ 'active': isActive(item.path) }" @click="closeMobileMenu">
              {{ item.name }}
            </router-link>
          </li>
        </ul>
      </nav>
      
      <div class="mobile-user-actions">
        <template v-if="isLoggedIn">
          <router-link to="/profile" class="btn btn-outline" @click="closeMobileMenu">个人中心</router-link>
          <button class="btn btn-outline" @click="handleLogout">退出登录</button>
        </template>
        <template v-else>
          <router-link to="/login" class="btn btn-primary" @click="closeMobileMenu">登录</router-link>
          <router-link to="/register" class="btn btn-outline" @click="closeMobileMenu">注册</router-link>
        </template>
      </div>
    </div>
    <!-- <div style="color:#000">通知</div> -->
  </header>
  
</template>

<script>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ArrowDown } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import { useLanguage } from '@/hooks/useLanguage'
export default {
  name: 'AppHeader',
  components: {
    ArrowDown
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const userStore = useUserStore()
    const isScrolled = ref(false)
    const isMobileMenuOpen = ref(false)
    const { currentLanguage, getLocalePath } = useLanguage()
    // console.log(currentLanguage.value)
    const isLoggedIn = computed(() => userStore.isLoggedIn)
    const userInfo = computed(() => userStore.userInfo)
    
    const navItems = computed(() => {
      const items = {
        chinese: [
          { path: '/chinese/home', name: '首页' },
          { 
            path: '/chinese/about', 
            name: '关于会议',
            children: [
              { name: '会议简介', anchor: 'intro' },
              { name: '会议主题', anchor: 'theme' },
              { name: '会议亮点', anchor: 'highlights' },
              { name: '重要嘉宾', anchor: 'speakers' },
              { name: '分论坛', anchor: 'forum' },
              { name: '合作伙伴', anchor: 'partners' },
              { name: '媒体支持', anchor: 'media' },
              { name: '住宿', anchor: 'hotel' }
            ]
          },
          { path: '/chinese/agenda', name: '会议日程' },
          { path: '/chinese/participants', name: '重要嘉宾' },
          { path: '/chinese/registration', name: '报名参会' },
          { path: '/chinese/Travel', name: '旅游' },
          // { path: '/chinese/Visits', name: '参观考察' },
          { path: '/chinese/AskQuestion', name: '常见问题' }
        ],
        english: [
          { path: '/english/home', name: 'Home' },
          { 
            path: '/english/about', 
            name: 'About',
            children: [
              { name: 'Introduction', anchor: 'intro' },
              { name: 'Theme', anchor: 'theme' },
              { name: 'Highlights', anchor: 'highlights' },
              { name: 'VIPs', anchor: 'speakers' },
              //{ name: 'Forum', anchor: 'forum' },
              { name: 'Sponser', anchor: 'partners' },
             // { name: 'Media', anchor: 'media' },
              { name: 'Accommodation', anchor: 'hotel' }
            ]
          },
          { path: '/english/agenda', name: 'Agenda' },
          { path: '/english/participants', name: 'VIPs' },
          { path: '/english/registration', name: 'Register' },
          { path: '/english/Travel', name: 'Partner Program' },
          // { path: '/english/Visits', name: 'Visits' },
          { path: '/english/AskQuestion', name: 'FAQ' }
        ]
      }
      return items[currentLanguage.value]
    })
    
    const isActive = (path) => {
      return route.path.startsWith(path)
    }
    const goCechandler = ()=>{
      window.open('https://www.cec1979.org.cn/englishVersion/','_blank')
    }
    const toggleMobileMenu = () => {
      isMobileMenuOpen.value = !isMobileMenuOpen.value
      document.body.classList.toggle('menu-open')
    }
    
    const closeMobileMenu = () => {
      isMobileMenuOpen.value = false
      document.body.classList.remove('menu-open')
    }
    
    const handleLogout = async () => {
      await userStore.logoutUser()
      closeMobileMenu()
      // sessionStorage.removeItem("registrationFormData");
      router.push(getLocalePath('/home'))
      sessionStorage.clear()
      localStorage.clear()
    }
    
    const scrollToSection = (anchor) => {
      if (!route.path.includes('/about')) {
        router.push({
          path: getLocalePath('/about'),
          query: { anchor }
        })
      } else {
        setTimeout(() => {
          const element = document.getElementById(anchor)
          if (element) {
            element.scrollIntoView({ behavior: 'smooth' })
          }
        }, 100)
      }
    }
    
    const switchLanguage = () => {
      const newLang = currentLanguage.value === 'chinese' ? 'english' : 'chinese'
      const currentPath = route.path
      const newPath = currentPath.replace(
        `/${currentLanguage.value}/`,
        `/${newLang}/`
      )
      router.push(newPath)
    }
    
    onMounted(() => {
      const handleScroll = () => {
        isScrolled.value = window.scrollY > 50
      }
      
      window.addEventListener('scroll', handleScroll)
      
      return () => {
        window.removeEventListener('scroll', handleScroll)
      }
    })
    
    return {
      isScrolled,
      isMobileMenuOpen,
      isLoggedIn,
      userInfo,
      navItems,
      isActive,
      toggleMobileMenu,
      closeMobileMenu,
      handleLogout,
      scrollToSection,
      currentLanguage,
      switchLanguage,
      getLocalePath,
      goCechandler
    }
  }
}
</script>

<style scoped>
/* 样式已在全局CSS中定义 */
.user-info {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  color: var(--text-color);
}

.user-info:hover {
  color: var(--primary-color);
}

.el-dropdown-menu {
  padding: 0;
}

.el-dropdown-menu a {
  display: block;
  padding: 8px 20px;
  color: var(--text-color);
  text-decoration: none;
}

.el-dropdown-menu a:hover {
  background-color: var(--bg-light);
  color: var(--primary-color);
}

.dropdown-link {
  display: block;
  padding: 8px 20px;
  color: var(--text-color);
  text-decoration: none;
  cursor: pointer;
  width: 100%;
  text-align: left;
  background: none;
  border: none;
}

.dropdown-link:hover {
  background-color: var(--bg-light);
  color: var(--primary-color);
}

.el-dropdown-menu {
  padding: 0;
  min-width: 120px;
}

.el-dropdown-menu .el-dropdown-item {
  padding: 0;
}

.mobile-user-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 20px;
}

.mobile-user-actions .btn {
  width: 100%;
  text-align: center;
}

.nav-item-with-dropdown {
  position: relative;
}

.nav-item-with-dropdown::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  height: 20px;
  background: transparent;
}

.submenu {
  display: none;
  position: absolute;
  top: calc(100% + 20px);
  left: 50%;
  transform: translateX(-50%);
  background-color: #fff;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 8px 0;
  min-width: 160px;
  z-index: 100;
  opacity: 0;
  transition: all 0.3s ease;
}

.nav-item-with-dropdown:hover .submenu {
  display: block;
  opacity: 1;
}

.submenu:hover {
  display: block;
  opacity: 1;
}

.submenu a {
  display: block;
  padding: 12px 24px;
  color: var(--text-color);
  text-decoration: none;
  white-space: nowrap;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  position: relative;
}

.submenu a:hover {
  background-color: var(--primary-color);
  color: #fff;
  padding-left: 28px;
}

.submenu::before {
  content: '';
  position: absolute;
  top: -6px;
  left: 50%;
  transform: translateX(-50%);
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid #fff;
}

@media (max-width: 768px) {
  .submenu {
    position: static;
    box-shadow: none;
    padding: 0;
    margin: 0;
    background: transparent;
    transform: none;
    display: none;
  }

  .submenu::before {
    display: none;
  }

  .submenu a {
    padding: 10px 20px;
    color: var(--text-color);
    background: transparent;
  }

  .submenu a:hover {
    background: transparent;
    color: var(--primary-color);
    padding-left: 20px;
  }

  .mobile-nav .nav-item-with-dropdown:hover .submenu {
    display: none;
  }

  .mobile-nav .nav-item-with-dropdown.active .submenu {
    display: block;
  }
}

/* 添加语言切换按钮样式 */
/* .lang-switch {
  margin-left: 20px;
}

.lang-switch a {
  padding: 8px 16px;
  border-radius: 4px;
  background-color: var(--primary-color);
  color: #fff;
  text-decoration: none;
  font-size: 14px;
  transition: all 0.3s ease;
}

.lang-switch a:hover {
  background-color: var(--primary-color-dark);
} */
 .logo{
  display: flex;
 }
.logo-big{
  cursor: pointer;
  margin-right: 12px;
  transform: scale(1.1);
}
.logo-big:hover{
 
}
@media (max-width: 768px) {
  .lang-switch {
    margin: 10px 0;
  }
  
  .lang-switch a {
    display: block;
    text-align: center;
  }
}
</style>
