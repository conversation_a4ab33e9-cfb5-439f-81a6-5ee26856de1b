<template>
  <div class="profile-page">
    <!-- 页面头部 -->

    <!-- 主要内容区域 -->
    <section class="profile-section">
      <div class="">
        <div class="profile-container">
          <!-- 左侧标签页 -->

          <!-- 右侧内容区域 -->
          <div class="profile-content">
            <!-- 个人资料表单 -->
            <el-table stripe max-height="600px" v-loading="tableDataLoad" :data="tableData"
              style="width: 100%; margin-top: 20px; overflow: hidden" row-key="chId" border>
              <el-table-column label="姓名" show-overflow-tooltip width="180">
                <template #default="scope">
                  {{ scope.row.firstName }} &nbsp; {{ scope.row.lastName }}
                </template>
              </el-table-column>
              <el-table-column prop="sex" label="性别" width="60">
                <template #default="scope">
                  {{ scope.row.sex === "sex_001" ? "男" : "女" }}
                </template>
              </el-table-column>
              <el-table-column prop="countryValue" label="国籍" show-overflow-tooltip />
              <!-- <el-table-column prop="organization" label="组织"  />   -->
              <!-- <el-table-column prop="position" label="职位"  />   -->
              <!-- <el-table-column prop="telephone" label="电话号码"  />   -->
              <el-table-column prop="telAddr" label="通讯地址" show-overflow-tooltip />
              <el-table-column prop="email" label="邮箱" show-overflow-tooltip />
              <el-table-column prop="statusValue" label="报名状态" show-overflow-tooltip />
              <el-table-column prop="cancelStatusValue" label="报名异常状态" show-overflow-tooltip />
              <el-table-column label="操作" width="170" fixed="right">
                <template #default="scope">
                  <el-button type="primary" v-if="scope.row.isParent" link
                    :disabled="scope.row.status != 'meeting_status_001' || !!scope.row.cancelStatusValue " @click="processPayment(scope.row)">
                    缴费
                  </el-button>
                  <el-button type="primary" v-if="scope.row.isParent"
                    :disabled="scope.row.status != 'meeting_status_001'" link @click="handleEdit(scope.row)">
                    编辑
                  </el-button>

                  <!-- <el-button
                    type="info"
                    :disabled="scope.row.status != 'meeting_status_001'"
                    v-if="!scope.row.isParent"
                    link
                    @click="handlePeerDelete(scope.row)"
                  >
                    删除
                  </el-button> -->
                  <!-- 父元素 -->
                  <el-dropdown v-if="scope.row.isParent">
                    <span class="el-dropdown-link">
                      更多
                      <el-icon class="el-icon--right">
                        <arrow-down />
                      </el-icon>
                    </span>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item @click="handleDetail(scope.row)">
                          详情
                        </el-dropdown-item>
                        <el-dropdown-item :disabled="scope.row.status != 'meeting_status_001'"
                          @click="handleDelete(scope.row)">删除</el-dropdown-item>
                        <!-- <el-dropdown-item :disabled="scope.row.status != 'meeting_status_001'"
                          @click="handleEditMeeting(scope.row)">修改参会</el-dropdown-item> -->
                        <el-dropdown-item :disabled="!!scope.row.cancelStatus || !scope.row.sendFlag"
                          @click="handleCancel(scope.row)">取消报名</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </template>
              </el-table-column>
            </el-table>



            <!-- 详情弹框 -->
            <el-dialog v-model="detailDialogVisible" title="报名详情" width="70%" class="detail-dialog">
              <div class="detail-content">
                <div class="detail-section" v-if="detailData.status != 'meeting_status_001'">
                   <h3>转账凭证</h3>
                  <el-descriptions :column="2" border>
                    <el-descriptions-item label="持卡人姓名">
                      {{ detailData.cardholdName }}
                    </el-descriptions-item>
                    <el-descriptions-item label="转账账号">
                      {{ detailData.remitAccount }}
                    </el-descriptions-item>
                    <el-descriptions-item label="订单号码">
                      {{ detailData.orderNum }}
                    </el-descriptions-item>
                    <el-descriptions-item label="参会费用">
                      {{ detailData.orderUnit =='1'?'￥':'EUR' }} {{ detailData.orderAmtPayable }}
                    </el-descriptions-item>
                    </el-descriptions>
                </div>
                <div class="detail-section">

                </div>
                <div class="detail-section" v-if="detailData.cancelStatus">
                  <h3>退款进展</h3>
                  <el-descriptions :column="2" border>
                    <el-descriptions-item label="退款进展">
                      {{
                        detailData.cancelStatus === "cancel_status_001"
                          ? "已取消"
                          : detailData.cancelStatus === "cancel_status_002"
                            ? " 退款中"
                            : detailData.cancelStatus === "cancel_status_003"
                              ? "已退款"
                              : ""
                      }}
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
                <!-- 报名人信息 -->
                <div class="detail-section">
                   <h3>报名人信息</h3>
                  <el-collapse v-model="detailActiveCompanions">
                    <el-collapse-item name="0">
                      <template #title>
                      <div class="companion-header">
                        <span>报名人： {{ detailData.firstName }} &nbsp;{{
                          detailData.lastName
                        }}</span>
                      </div>
                    </template>
                  <el-descriptions :column="2" border>
                    <el-descriptions-item label="姓名">
                          {{ detailData.firstName }} &nbsp;{{ detailData.lastName }}
                    </el-descriptions-item>
                    <el-descriptions-item label="性别">
                          {{ detailData.sex === "sex_001" ? "男" : "女" }}
                    </el-descriptions-item>
                    <el-descriptions-item label="国籍">
                          {{ detailData.countryValue }}
                    </el-descriptions-item>
                    <el-descriptions-item label="组织">
                      {{ detailData.organization }}
                    </el-descriptions-item>
                    <el-descriptions-item label="职位">
                      {{ detailData.position }}
                    </el-descriptions-item>
                    <el-descriptions-item label="电话号码">
                      {{ detailData.telephone }}
                    </el-descriptions-item>
                    <el-descriptions-item label="通讯地址">
                      {{ detailData.telAddr }}
                    </el-descriptions-item>
                    <el-descriptions-item label="邮箱">
                      {{ detailData.email }}
                    </el-descriptions-item>
                    <el-descriptions-item label="护照号">
                      {{ detailData.passportNum }}
                    </el-descriptions-item>
                        <el-descriptions-item>
                          <template #label>
                            是否需要签证邀请函(只涵盖会议期间和路上往返共五天)
                            <!-- <el-tooltip content="只涵盖会议期间和路上往返共五天" placement="top">
                              <el-icon style="margin-left: 2px; cursor: pointer;">
                                <QuestionFilled style="padding-top:2px;" />
                              </el-icon>
                            </el-tooltip> -->
                          </template>
                          {{
                            detailData.invitePassportFlag === "invite_passport_flag_001"
                              ? "是"
                              : "否"
                          }}
                    </el-descriptions-item>
                        <el-descriptions-item label="是否第一次来华">
                          {{
                            detailData.firstToCn === "first_to_cn_001" ? "是" : "否"
                          }}
                        </el-descriptions-item>
                        <el-descriptions-item label="参会费用">
                          {{ detailData.orderUnit =='1'?'￥':'EUR' }} {{
                            (() => {
                              const types = detailData.meetingType.split(',');
                              const total = types.map(type => {
                                const item = participationList.find(item => item.value === type);
                                return item ? +item.price : 0;
                              }).filter(Boolean).reduce((a, b) => a + b, 0);
                              return total  + '.00'
                              
                            })()
                          }}
                        </el-descriptions-item>
                        <el-descriptions-item label="参会类型" :span="2">
                          {{
                            detailData.meetingType.split(',').map(type => {
                              const item = participationList.find(item => item.value === type);
                              return item ? item.label : '';
                            }).filter(Boolean).join('、')
                          }}
                        </el-descriptions-item>
                        <el-descriptions-item label="组织介绍" :span="2">
                          {{ detailData?.orgIntroduce }}
                        </el-descriptions-item>
                        <el-descriptions-item label="个人简历" :span="2">
                          {{ detailData?.personalIntroduce }}
                        </el-descriptions-item>
                        <el-descriptions-item label="护照照片" :span="2">

                          <el-image style="margin:0 12px;" v-for="(itemUrl, index) in detailData.passportImageList" :src="getImage(itemUrl)
                            "></el-image>
                        </el-descriptions-item>

                  </el-descriptions>

                  <!-- 签证信息 -->
                      <div v-if="
                        detailData.invitePassportFlag ===
                        'invite_passport_flag_001'
                      " class="visa-info">
                    <h3>签证信息</h3>
                    <el-descriptions :column="2" border>


                      <el-descriptions-item label="出生日期">
                        {{ detailData.visaInfo?.visaBirth }}
                      </el-descriptions-item>
                      <el-descriptions-item label="申请签证地点">
                        {{ detailData.visaInfo?.visaApplyPassportSite }}
                      </el-descriptions-item>
                      <el-descriptions-item label="入境日期">
                        {{ detailData.visaInfo?.visaEnterDate }}
                      </el-descriptions-item>
                          <el-descriptions-item label="离境日期">
                            {{ detailData.visaInfo?.visaLeaveDate }}
                      </el-descriptions-item>
                          <el-descriptions-item label="紧急联系人">
                            {{ detailData.visaInfo?.visaUrgentContacter }}
                      </el-descriptions-item>
                          <el-descriptions-item label="邀请函接收邮箱">
                            {{ detailData.visaInfo?.visaEmail }}
                      </el-descriptions-item>
                          <el-descriptions-item label="访问地点">
                            {{ detailData.visaInfo?.visaVisitSite }}
                          </el-descriptions-item>

                    </el-descriptions>
                  
                  </div>
              </el-collapse-item>
            </el-collapse>
                </div>

                <!-- 同行人信息 -->
                <div class="detail-section" v-if="detailData.children && detailData.children.length > 0">
                  <h3>同行人信息</h3>
                  <el-collapse v-model="detailActiveCompanions" accordion>
                    <el-collapse-item v-for="(companion, index) in detailData.children" :key="index" :name="(index + 1).toString()">
                      <template #title>
                        <div class="companion-header">
                        <span>同行人： {{ companion.firstName }} &nbsp;{{
                          companion.lastName
                        }}</span>
                        </div>
                      </template>

                      <el-descriptions :column="2" border>
                        <el-descriptions-item label="姓名">
                        {{ companion.firstName }} &nbsp;{{
                          companion.lastName
                        }}
                        </el-descriptions-item>
                        <el-descriptions-item label="性别">
                        {{ companion.sex === "sex_001" ? "男" : "女" }}
                        </el-descriptions-item>
                        <el-descriptions-item label="国籍">
                        {{ companion.countryValue }}
                        </el-descriptions-item>
                        <el-descriptions-item label="组织">
                          {{ companion.organization }}
                        </el-descriptions-item>
                        <el-descriptions-item label="职位">
                          {{ companion.position }}
                        </el-descriptions-item>
                        <el-descriptions-item label="电话号码">
                          {{ companion.telephone }}
                        </el-descriptions-item>
                        <el-descriptions-item label="通讯地址">
                          {{ companion.telAddr }}
                        </el-descriptions-item>
                        <el-descriptions-item label="邮箱">
                          {{ companion.email }}
                        </el-descriptions-item>
                        <el-descriptions-item label="护照号">
                          {{ companion.passportNum }}
                        </el-descriptions-item>
                      <el-descriptions-item>
                        <template #label>
                          是否需要签证邀请函(只涵盖会议期间和路上往返共五天)
                          <!-- <el-tooltip content="只涵盖会议期间和路上往返共五天" placement="top">
                            <el-icon style="margin-left: 2px; cursor: pointer;">
                              <QuestionFilled style="padding-top:2px;" />
                            </el-icon>
                          </el-tooltip> -->
                        </template>
                        {{
                          companion.invitePassportFlag === "invite_passport_flag_001"
                            ? "是"
                            : "否"
                        }}
                      </el-descriptions-item>
                      <el-descriptions-item label="是否第一次来华">
                        {{
                          companion.firstToCn === "first_to_cn_001"
                            ? "是"
                            : "否"
                        }}
                      </el-descriptions-item>
                      <el-descriptions-item label="参会费用">
                        {{ detailData.orderUnit =='1'?'￥':'EUR' }} {{
                          (() => {
                            const types = companion.meetingType.split(',');
                            const total = types.map(type => {
                              const item = participationList.find(item => item.value === type);
                              return item ? +item.price : 0;
                            }).filter(Boolean).reduce((a, b) => a + b, 0);
                            
                            return total + '.00'
                          })()
                        }}
                      </el-descriptions-item>
                      <el-descriptions-item label="参会类型" :span="2">
                        {{
                          companion.meetingType.split(',').map(type => {
                            const item = participationList.find(item => item.value === type);
                            return item ? item.label : '';
                          }).filter(Boolean).join('、')
                        }}
                      </el-descriptions-item>
                      <el-descriptions-item label="组织介绍" :span="2">
                        {{ companion?.orgIntroduce }}
                      </el-descriptions-item>
                      <el-descriptions-item label="个人简历" :span="2">
                        {{
                          companion?.personalIntroduce
                        }}
                      </el-descriptions-item>
                      <el-descriptions-item label="护照照片" :span="2">

                        <el-image style="margin:0 12px;" v-for="(itemUrl, index) in companion.passportImageList" :src="getImage(itemUrl)
                          "></el-image>
                        </el-descriptions-item>
                      </el-descriptions>

                      <!-- 同行人签证信息 -->
                    <div v-if="
                      companion.invitePassportFlag ===
                      'invite_passport_flag_001'
                    " class="visa-info">
                        <h3>签证信息</h3>
                        <el-descriptions :column="2" border>

                          <el-descriptions-item label="出生日期">
                            {{ companion.meetingVisaInfo?.visaBirth }}
                          </el-descriptions-item>
                          <el-descriptions-item label="申请签证地点">
                          {{
                            companion.meetingVisaInfo?.visaApplyPassportSite
                          }}
                          </el-descriptions-item>

                          <el-descriptions-item label="入境日期">
                            {{ companion.meetingVisaInfo?.visaEnterDate }}
                          </el-descriptions-item>
                          <el-descriptions-item label="离境日期">
                            {{ companion.meetingVisaInfo?.visaLeaveDate }}
                          </el-descriptions-item>
                          <el-descriptions-item label="紧急联系人">
                            {{ companion.meetingVisaInfo?.visaUrgentContacter }}
                          </el-descriptions-item>
                          <el-descriptions-item label="邀请函接收邮箱">
                            {{ companion.meetingVisaInfo?.visaEmail }}
                          </el-descriptions-item>
                          <el-descriptions-item label="访问地点">
                            {{ companion.meetingVisaInfo?.visaVisitSite }}
                          </el-descriptions-item>

                        </el-descriptions>
                     </div>
                    </el-collapse-item>
                  </el-collapse>
                </div>
              </div>
            </el-dialog>
          <!-- 转账信息弹窗 -->
          <el-dialog v-model="showTransferForm" title="填写转账信息" width="500px" :close-on-click-modal="false"
            :close-on-press-escape="false" :show-close="false">
            <el-form ref="transferFormRef" :model="transferForm" :rules="transferRules" label-width="120px">
              <el-form-item label="转账账号" prop="remitAccount" required>
                <el-input type="number" v-model.trim="transferForm.remitAccount" placeholder="请输入转账账号" clearable />
              </el-form-item>
              <el-form-item label="持卡人姓名" prop="cardholdName" required>
                <el-input v-model="transferForm.cardholdName" placeholder="请输入持卡人姓名" clearable />
              </el-form-item>
            </el-form>
            <template #footer>
              <span class="dialog-footer">
                <el-button @click="cancelPayment">取消</el-button>
                <el-button type="primary" @click="submitTransferInfo" :loading="isProcessing" :disabled="!isFormValid">
                  确认
                </el-button>
              </span>
            </template>
          </el-dialog>
          </div>
        </div>
      </div>
    </section>


  </div>
</template>

<script>
import { ref, onMounted, reactive, computed, nextTick } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { ArrowDown, QuestionFilled } from "@element-plus/icons-vue";
import { User, Lock, Document } from "@element-plus/icons-vue";
import { useUserStore } from "../../stores/user";
import { createPayment } from "../../api/payment";
import ImageUpload from "@/components/common/ImageUpload.vue";
import {
  getRegistrationInfo,
  updateRegistration,
  cancelSignInfo,
  deleteSignUpInfo,
  updatePeerInfo,
  deletePeerInfo,
  generateOrderNum
} from "../../api/registration";
// import ConferenceRegistration from "../components/registration/ConferenceRegistration.vue";
import {useLanguage} from '@/hooks/useLanguage.js'
export default {
  name: "ProfilePage",
  components: {
    User,
    Lock,
    Document,
    ImageUpload,
    // ConferenceRegistration,
  },
  setup() {
    const router = useRouter();
    const userStore = useUserStore();
    const activeTab = ref("profile");
    const registrationActiveTab = ref("main");
    const activeCompanions = ref([]);
    const profileFormRef = ref(null);
    const passwordFormRef = ref(null);
    const isLoading = ref(false);
    const registrationFormRef = ref(null);
    const companionFormRefs = ref([]);
    const isSubmitting = ref(false);
    const dialogVisible = ref(false);
    const showTransferForm = ref(false);
    const formValid = ref(false);
    const editFormRef = ref(null);
    const { getLocalePath} = useLanguage()
    const editForm = ref({
      signUpFirstName: "",
      signUpLastName: "",
      sex: "",
      country: "",
      organization: "",
      position: "",
      telephone: "",
      telAddr: "",
      email: "",
      passportNum: "",
      invitePassportFlag: "",
      passportImage: "",
      visaInfo: {
        visaBirth: "",
        visaApplyPassportSite: "",
        visaEnterDate: "",
        visaVisitSite: "",
        visaOrgIntroduce: "",

        visaPersonalIntroduce: "",
      },
    });

    // 报名信息
    const registrationInfo = ref({});
    const companions = ref([]);
    const paymentInfo = ref({});
    const tableData = ref([]);
    const tableDataLoad = ref(true);
    // 个人资料表单
    const profileForm = ref({
      lastName: "",
      firstName: "",
      email: "",
      telephone: "",
      organization: "",
      position: "",
      addr: "",
    });

    // 修改密码表单
    const passwordForm = ref({
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    });

    // 报名信息表单
    const registrationForm = ref({
      // 基本信息
      signUpFirstName: "",
      signUpLastName: "",
      sex: "",
      country: "",
      organization: "",
      position: "",
      telephone: "",
      telAddr: "",
      email: "",
      passportNum: "",
      firstToCn: "no",
      invitePassportFlag: "",
      passportImage: "",
      // 签证信息
      visaInfo: {
        visaFirstName: "",
        visaLastName: "",
        visaSex: "",
        visaBirth: "",
        visaCountry: "",
        visaEnterDate: "",
        visaLeaveDate: "",
        getSignUpInfogentContacter: "",

        visaEmail: "",
        visaOrganization: "",
        visaPosition: "",
        visaApplyPassportSite: "",
        visaVisitSite: "",
        visaOrgIntroduce: "",
        visaPersonalIntroduce: "",
        personType: "person_type_001",
      },
    });

   

    const meetingList = ref([
    {
        value: "1",
        name: "国际CMC大会",
        price: 1200,
        description: "会期1.5天",
        features: [
          "各国CMC交流管理咨询经验，探讨管理咨询新趋势",
          "探讨合作机会，推动企业提升管理水平",
          "颁发国际管理咨询案例奖（君士坦丁奖）",
        ],
      },
      {
        value: "2",
        name: "ICMCI年会",
        price: 800,
        description: "会期1.5天",
        features: [
          "总结过去一年工作各成员国组织交流经验、战略执行、会员发展业务项目进展及财务决算",
          "落实新一年工作任务，重点项目推动审议财务预算，增补执委会、相关专业委员会委员和理事",
        ],
      },
      {
        value: "3",
        name: "各国CMC交流管理咨询经验，探讨管理咨询新趋势",
        price: 500,
      },
      {
        value: "4",
        name: "探讨合作机会，推动企业提升管理水平",
        price: 500,
      },
      {
        value: "5",
        name: "颁发国际管理咨询案例奖（君士坦丁奖）",
        price: 500,
      },
      {
        value: "6",
        name: "总结过去一年工作,主要包括各成员国组织交流经验、战略执行、会员发展、业务项目进展及财务决算",
        price: 300,
      },
      {
        value: "7",
        name: "审议财务预算，增补执委会、相关专业委员会委员和理事",
        price: 300,
      },
    ]);

    // 计算会议费用
    const calculateMeetingAmount = (meetingConact) => {
      if (!meetingConact) return 0;

      const meetingValues = meetingConact.split(',');
      let totalAmount = 0;

      meetingValues.forEach(value => {
        const meeting = meetingList.value.find(item => item.value === value);
        if (meeting) {
          totalAmount += meeting.price;
        }
      });

      return totalAmount;
    };

    // 在需要计算费用的地方使用
    const getMeetingAmount = (meetingConact) => {
      return calculateMeetingAmount(meetingConact);
    };

    // 根据meetingType过滤meetingList
    const filteredMeetingList = computed(() => {
      if (!meetingTypeString.value) return meetingList.value;
      const typeArray = meetingTypeString.value.split(",");
      // console.log(
      //   meetingList.value.filter((item) => typeArray.includes(item.value)),
      //   "meetingList.value.filter(item => typeArray.includes(item.value))"
      // );
      return meetingList.value.filter((item) => typeArray.includes(item.value));
    });



    const getImage = (path) => {

      if (!path) return ''
      // 如果是线上环境，使用当前地址栏的地址
      if (import.meta.env.PROD) {
        const baseUrl = window.location.origin
        return `${baseUrl}${path}`
    }
      // 开发环境使用环境变量配置的地址
      return `${import.meta.env.VITE_APP_imgUrl}${path}`
    };
    // 获取用户信息
    const getUserInfo = () => {
      Object.assign(profileForm.value, userStore.userInfo);
    };
    const commandHandler = (row, command) => {
      console.log(row, command);
    };
    const meetingTypeString = ref("");
    // 获取报名信息
    const fetchRegistrationInfo = async () => {
      try {
        tableDataLoad.value = true;
        const response = await getRegistrationInfo(userStore.userInfo.id);
        if (response.code === 1000 && response.data) {
          // 设置主报名人信息
          response.data.data.forEach((item, index) => {
            item.chId = index + 1;
            item.isParent = true;
            if (!item.peerInfoList) {
              item.children = [];
            } else {
              item.peerInfoList.forEach((peerItem, peerIndex) => {
                peerItem.chId = item.chId + peerIndex.toString();
                peerItem.isParent = false;
              });
              
              item.children = item.peerInfoList;
            }
            if (!item.visaInfo) {
              item.visaInfo = {};
            }
            item.signUpInfoId = item.id;
          });
          
          tableDataLoad.value = false;
          tableData.value = response.data.data;
          console.log(tableData.value);

          return;
          const mainInfo = response.data.data[0];
          console.log(mainInfo);
          registrationForm.value = {
            ...mainInfo,
            userId: userStore.userInfo.id,
            visaInfo: mainInfo.visaInfo || {
              visaFirstName: "",
              visaLastName: "",
              visaSex: "",
              visaBirth: "",
              visaCountry: "",
              visaEnterDate: "",
              visaLeaveDate: "",
              getSignUpInfogentContacter: "",
              passportImage: "",
              visaEmail: "",
              visaOrganization: "",
              visaPosition: "",
              visaApplyPassportSite: "",
              visaVisitSite: "",
              visaOrgIntroduce: "",
              visaPersonalIntroduce: "",
              personType: "person_type_001",
            },
          };

          // 设置同行人信息
          companions.value = mainInfo.peerInfoList || [];
        }
      } catch (error) {
        console.error("获取报名信息失败:", error);
        ElMessage.error("获取报名信息失败，请稍后重试");
        getCurrentLangPath()
      }
    };
    // 获取当前语言路径
    const getCurrentLangPath = () => {
      const currentPath = router.currentRoute.value.fullPath
      const langPath =  currentPath.startsWith('/english') ? '/english' : '/chinese'
    
        router.replace({
          path: `${langPath}/login`,
          query: { redirect: router.currentRoute.value.fullPath }
        })
    }
    const getTicketTypeName = (type) => {
      const ticketTypes = {
        full: "全程参会",
        single: "单日参会",
        student: "学生票",
      };
      return ticketTypes[type] || type;
    };

    const getPaymentStatus = (status) => {
      const statusMap = {
        payment_status_001: "待支付",
        payment_status_002: "已支付",
        payment_status_003: "已取消",
      };
      return statusMap[status] || status;
    };

    const goToPayment = async () => {
      try {
        const confirm = await ElMessageBox.confirm("确定取消报名？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "success",
        });
        console.log(confirm);
        if (confirm === "confirm") {
          cancelSignInfo(userStore.userInfo.id).then((res) => {
            if (res.code != 1000) {
              ElMessage.error(res.msg);
            } else {
              ElMessage.success(res.msg);
            }
          });
        } else {
        }
      } catch (error) {
        // 用户点击取消按钮时，直接跳转到支付页面
      }
    };
    const participationList = ref([
    {
        date:'21 Oct.',
        label:'Welcome Reception Event',
        price:'45',
        value:'1'
      },
      {
        date:'22-23 Oct.',
        label:'Conference In-person',
        price:'335',
        value:'2'
      },
      {
        date:'22 Oct.',
        label:'Constantinus Award & Gala Dinner',
        price:'130',
        value:'3'
      },
      {
        date:'23-24 Oct.',
        label:'Annual Meeting In-person',
        price:'245',
        value:'4'
      },
      {
        date:'23-24 Oct.',
        label:'Annual Meeting On-line',
        price:'100',
        value:'5'
      },
      {
        date:'BUNDLES',
        label:'Bundle (Reception, Gala Dinner, Conference, Annual Meeting)',
        price:'700',
        value:'6'
      },
      {
        date:'BUNDLES',
        label:'Bundle (Reception, Gala Dinner, Conference)',
        price:'500',
        value:'7'
      }
    ])
    const transferFormRef = ref(null);
    const isProcessing = ref(false);
    const transferForm = ref({
      remitAccount: "",
      cardholdName: "",
    });
    const isFormValid = computed(() => {
      return transferForm.value.remitAccount && transferForm.value.cardholdName;
    });
    const editItemData = ref({});
    const transferRules = {
      remitAccount: [
        { required: true, message: "请输入转账账号", trigger: "blur" },
      ],
      cardholdName: [
        { required: true, message: "请输入持卡人姓名", trigger: "blur" },
      ],
    };

    const processPayment = async (row) => {
      window.open('https://www.paypal.com/ncp/payment/AX95YG67WQ87E','_blank')
      return
      if (!row.visaInfo || Object.keys(row.visaInfo).length == 0) {
        row.visaInfo = null;
      }
      editItemData.value = row;
      // 重置表单数据
      transferForm.value = {
        remitAccount: "",
        cardholdName: "",
      };
      console.log(row)
      showTransferForm.value = true;
      // 等待弹窗完全打开后再清除校验状态
      await nextTick();
      if (transferFormRef.value) {
        transferFormRef.value.clearValidate();
      }
    };

    const submitTransferInfo = async () => {
      if (!transferFormRef.value) return;

      try {
        // 先进行表单校验
        const valid = await transferFormRef.value.validate();
        if (!valid) {
          return;
        }

        isProcessing.value = true;
        const getOrderNum = await generateOrderNum()
        // 创建支付订单
        const payResult = await createPayment({
          ...editItemData.value,
          remitAccount: transferForm.value.remitAccount,
          cardholdName: transferForm.value.cardholdName,
          status: "meeting_status_003",
          remindStatus: "remind_status_002",
          orderNum: getOrderNum.data.data,
        });

        if (payResult.code === 1000) {
          ElMessage.success("支付信息提交成功");
          showTransferForm.value = false;
          fetchRegistrationInfo();
        } else {
          ElMessage.error(payResult.msg || "提交失败，请重试");
        }
      } catch (error) {
        console.error("提交支付信息失败:", error);
        ElMessage.error("提交支付信息失败，请重试");
      } finally {
        isProcessing.value = false;
      }
    };
    const cancelPayment = () => {
      showTransferForm.value = false;
      // 重置表单数据
      transferForm.value = {
        remitAccount: "",
        cardholdName: "",
      };
    };


    const isParentFlag = ref(null);
    // 编辑按钮点击事件
    const handleEdit = (row) => {
      editForm.value = {
        ...row,
        visaInfo: row.visaInfo || row.meetingVisaInfo,
      };
      // dialogVisible.value = true;



      const registrationData = editForm.value;
      // console.log('原始报名数据:', registrationData)

      // 处理参会信息
      const meetingTypes = registrationData.meetingTypes ? registrationData.meetingTypes.split(',') : []
      // console.log('分割后的参会信息:', meetingTypes)

      // 更新数据结构
      const formattedData = {
        ...registrationData,
        meetingTypes: meetingTypes.filter(type => ['1', '2'].includes(type)),
        forums: meetingTypes.filter(type => !['1', '2'].includes(type))
      }
      // console.log('格式化后的数据:', formattedData)

      // 分离同行人信息
      // const peerPeople = formattedData.peerInfoList || []


      // 存储报名信息
      sessionStorage.setItem('registrationFormData', JSON.stringify(formattedData))


      // 跳转到报名页面
      router.push(getLocalePath('registration'))
    }

    // 详情按钮点击事件
    const handleDetail = (row) => {
      isParentFlag.value = row.isParent;
      detailData.value = row;
      detailDialogVisible.value = true;
      meetingTypeString.value = row.meetingType;
      // 设置默认展开报名人信息，收起同行人信息
      detailActiveCompanions.value = ['0'];
    };

    // 删除按钮点击事件
    const handleDelete = async (row) => {
      try {
        await ElMessageBox.confirm("确定要删除该报名人信息吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });
        // 实现删除逻辑
        console.log("删除", row);
        const result = await deleteSignUpInfo(row.id);
        if (result.code === 1000) {
          ElMessage.success(result.msg);
          fetchRegistrationInfo();
        } else {
          ElMessage.warning(result.msg);
        }
      } catch (error) {
        // 用户取消删除
      }
    };
    // 删除按钮点击事件
    const handlePeerDelete = async (row) => {
      try {
        await ElMessageBox.confirm("确定要删除该同行人报名信息吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });
        // 实现删除逻辑
        console.log("删除", row);
        const result = await deletePeerInfo(row.id);
        if (result.code === 1000) {
          ElMessage.success(result.msg);
          fetchRegistrationInfo();
        } else {
          ElMessage.warning(result.msg);
        }
      } catch (error) {
        // 用户取消删除
      }
    };

    // 取消报名按钮点击事件
    const handleUpdata = async (row) => { };
    // 取消报名按钮点击事件
    const handleCancel = async (row) => {
      try {
        await ElMessageBox.confirm("确定要取消报名吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });
        // 实现取消报名逻辑

        const result = await cancelSignInfo(row.id);
        if (result.code === 1000) {
          ElMessage.success(result.msg);
          fetchRegistrationInfo();
        } else {
          ElMessage.warning(result.msg);
        }
      } catch (error) {
        // 用户取消操作
      }
    };

    // 保存编辑
    const handleSaveEdit = async () => {
      if (!editFormRef.value) return;

      try {
        await editFormRef.value.validate();
        isSubmitting.value = true;
        if (editForm.value.isParent) {
          editForm.value.status = "meeting_status_001";
          const response = await updateRegistration(editForm.value);
          if (response.code === 1000) {
            ElMessage.success("报名信息更新成功");
            dialogVisible.value = false;
            fetchRegistrationInfo(); // 重新获取最新数据
          } else {
            ElMessage.error(response.message || "更新失败，请稍后重试");
          }
        } else {
          const response = await updatePeerInfo(editForm.value);
        if (response.code === 1000) {
            ElMessage.success("报名信息更新成功");
            dialogVisible.value = false;
            fetchRegistrationInfo(); // 重新获取最新数据
        } else {
            ElMessage.error(response.message || "更新失败，请稍后重试");
        }
        }
      } catch (error) {
        console.error("更新报名信息失败:", error);
        ElMessage.error("更新失败，请检查表单填写是否正确");
      } finally {
        isSubmitting.value = false;
      }
    };

    // 关闭弹框
    const handleClose = (done) => {
      ElMessageBox.confirm("确认关闭？")
        .then(() => {
          done();
        })
        .catch(() => {
          // 取消关闭
        });
    };

    const detailDialogVisible = ref(false);
    const detailActiveCompanions = ref(0);
    const detailData = ref({});

    const currentMeetingForm = reactive({
      meetingType: [],
      forums: [],
    });


    onMounted(() => {
      getUserInfo();
      fetchRegistrationInfo();
    });

    return {
      activeTab,
      registrationActiveTab,
      activeCompanions,
      profileForm,
      passwordForm,
      getImage,
      tableData,
      profileFormRef,
      passwordFormRef,
      isLoading,
      registrationInfo,
      companions,
      paymentInfo,
      registrationForm,
      registrationFormRef,
      companionFormRefs,
      isSubmitting,

 
      getTicketTypeName,
      getPaymentStatus,
      transferFormRef,
      goToPayment,
    
    
 
      filteredMeetingList,
      dialogVisible,
      editForm,
      editFormRef,
      handleEdit,
      meetingList,
      handleDetail,
      isParentFlag,
      isProcessing,
      handleDelete,
      handleCancel,
      handleSaveEdit,
      handleClose,
      detailDialogVisible,
      detailActiveCompanions,
      detailData,
      tableDataLoad,
      commandHandler,
      currentMeetingForm,

      processPayment,

      
      formValid,
   

      handlePeerDelete,
      transferForm,
      submitTransferInfo,
      participationList,
      showTransferForm,
      isFormValid,
      transferRules,
      cancelPayment,
    };
  },
};
</script>

<style scoped>
@import url('@/assets/css/profile.css');

</style>
