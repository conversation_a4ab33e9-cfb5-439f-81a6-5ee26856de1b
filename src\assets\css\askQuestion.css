.conference-overview-container{
    background: url('@/assets/images/home-bg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    min-height: 100vh;
    padding: 60px;
  }
  .faq-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #e4e8eb 100%);
   
  }
  
  .page-header {
    background: linear-gradient(rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.3)), url('@/assets/images/dahui.png');
    background-size: cover;
    background-position: center;
  
    height: 200px;
    text-align: center;
    margin-top: 80px;  }
  
  .page-header h1 {
    font-size: 42px;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    animation: fadeInDown 1s ease-out;
  }
  
  .page-header p {
    font-size: 18px;
    opacity: 0.9;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    animation: fadeInUp 1s ease-out;
  }
  
  @keyframes fadeInDown {
    from {
      opacity: 0;
      transform: translateY(-20px);
    }
  
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
  
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .faq-card {
    max-width: 1000px;
    padding: 40px;
    margin: 0 auto;
    border-radius: 16px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    border: none;
    overflow: hidden;
    transition: transform 0.3s ease;
  }
  
  .faq-card:hover {
    transform: translateY(-5px);
  }
  
  :deep(.el-collapse-item__header) {
    font-size: 18px;
    font-weight: 500;
    color: var(--primary-color);
    padding: 20px;
    border-bottom: 1px solid #eee;
    transition: all 0.3s ease;
  }
  
  :deep(.el-collapse-item__header:hover) {
    background-color: rgba(30, 136, 229, 0.05);
  }
  
  :deep(.el-collapse-item__content) {
    padding: 20px;
  }
  
  .faq-content {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    padding: 10px;
  }
  
  .faq-content i {
    color: var(--primary-color);
    margin-top: 4px;
    font-size: 24px;
  }
  
  .faq-content p {
    font-size: 18px;
    line-height: 1.8;
    color: #555;
    margin: 0;
  }
  
  .faq-content h3 {
    font-size: 16px;
    /* color: var(--primary-color); */
    margin: 20px 0 10px;
  }
  
  .faq-content a {
    /* color: var(--primary-color); */
    text-decoration: none;
    transition: color 0.3s ease;
  }
  
  .faq-content a:hover {
    color: #1565c0;
    text-decoration: underline;
  }
  
  .notice-content {
    flex: 1;
  }
  
  .notice-intro {
    font-size: 20px;
    font-weight: 500;
    color: var(--primary-color);
    margin-bottom: 24px;
    line-height: 1.6;
  }
  
  .notice-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  
  .notice-item {
    display: flex;
    gap: 16px;
    align-items: flex-start;
  }
  
  .notice-label {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 18px;
    min-width: 24px;
  }
  
  .notice-item p {
    font-size: 18px;
    line-height: 1.8;
    margin: 0;
  }
  
  .penalty-list {
    margin: 12px 0 0 40px;
    padding: 0;
    list-style-type: disc;
  }
  
  .penalty-list li {
    font-size: 18px;
    margin-bottom: 12px;
    line-height: 1.8;
    color: #555;
  }
  
  .penalty-list li:last-child {
    margin-bottom: 0;
  }
  
  @media (max-width: 768px) {
    .page-header {
      padding: 80px 0 40px;
    }
  
    .page-header h1 {
      font-size: 32px;
    }
  
    .page-header p {
      font-size: 16px;
    }
  
    .faq-card {
      margin: 20px;
    }
  
    :deep(.el-collapse-item__header) {
      font-size: 16px;
      padding: 15px;
    }
  
    .faq-content {
      flex-direction: column;
      gap: 10px;
    }
  
    .faq-content i {
      margin-bottom: 10px;
    }
  
    .faq-content p {
      font-size: 16px;
    }
  
    .faq-content h3 {
      font-size: 20px;
    }
  
    .notice-intro {
      font-size: 18px;
    }
  
    .notice-item {
      gap: 12px;
    }
  
    .penalty-list {
      margin-left: 24px;
    }
  }