<template>
  <div class="companion-registration-page">
    <!-- 页面头部 -->
    <section class="page-header">
      <div class="container">
        <h1>添加同行人</h1>
        <p>最多可添加5位同行人</p>
      </div>
    </section>

    <!-- 同行人表单 -->
    <section class="registration-form-section">
      <div class="container">
        <div class="form-container">
          <h2>同行人信息</h2>
          <p class="form-desc">请填写同行人信息，带 <span class="required">*</span> 为必填项</p>

          <el-collapse v-model="activeCompanions" accordion>
            <el-collapse-item v-for="(companion, index) in companions" :key="index" :name="index">
              <template #title>
                <div class="companion-header">
                  同行人： {{ companion.firstName }}&nbsp;{{ companion.lastName }}
                  <el-button type="danger" size="small" @click.stop="removeCompanion(index)">
                    删除
                  </el-button>
                </div>
              </template>

              <el-form  :ref="el => formRefs[index] = el" :model="companion" :rules="rules" label-width="150px"
                class="companion-form" label-position="top">
                <div class="form-section">

                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="First Name" prop="firstName" required>
                        <el-input v-model="companion.firstName" placeholder="请输入first Name" />
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="Last Name" prop="lastName" required>
                        <el-input v-model="companion.lastName" placeholder="请输入Last Name" />
                      </el-form-item>
                    </div>
                    
                  </div>

                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="性别" prop="sex" required>
                        <el-radio-group v-model="companion.sex">
                          <el-radio label="sex_001">男</el-radio>
                          <el-radio label="sex_002">女</el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="国籍" prop="country" required>
                        <el-input v-model="companion.country" placeholder="请输入国籍" />
                      </el-form-item>
                    </div>
                  </div>

                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="组织" prop="organization" required>
                        <el-input v-model="companion.organization" placeholder="请输入组织名称" />
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="职位" prop="position" required>
                        <el-input v-model="companion.position" placeholder="请输入职位" />
                      </el-form-item>
                    </div>
                  </div>

                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="电话号码" prop="telephone">
                        <el-input v-model="companion.telephone" placeholder="请输入电话号码" />
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="通讯地址" prop="telAddr" required>
                        <el-input v-model="companion.telAddr" placeholder="请输入通讯地址" />
                      </el-form-item>
                    </div>
                  </div>

                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="邮箱" prop="email" required>
                        <el-input v-model="companion.email" placeholder="请输入邮箱" />
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="护照号" prop="passportNum" required>
                        <el-input v-model="companion.passportNum" placeholder="请输入护照号" />
                      </el-form-item>
                    </div>
                  </div>


                  <div class="form-row">
                  <div class="form-group">

                    <el-form-item label="是否需要签证邀请函"  prop="invitePassportFlag" required>
                 
                      <!-- <label>
                        是否需要签证邀请函
                        <el-tooltip class="item" effect="dark" content="只涵盖会议期间和路上往返共五天" placement="top">
                          <el-icon>
                            <QuestionFilled />
                          </el-icon>
                        </el-tooltip>
                      </label> -->
                   
                      <el-radio-group v-model="companion.invitePassportFlag" >
                        <el-radio label="invite_passport_flag_001">是</el-radio>
                        <el-radio label="invite_passport_flag_002">否</el-radio>
                      </el-radio-group>
           
                    </el-form-item>
                  </div>
                  <div class="form-group">
                <el-form-item label="是否第一次来华" prop="firstToCn" required>
                  <el-radio-group v-model="companion.firstToCn">
                    <el-radio label="first_to_cn_001">是</el-radio>
                    <el-radio label="first_to_cn_002">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </div>
                </div>
                </div>

                <div class="form-section" v-if="companion.invitePassportFlag === 'invite_passport_flag_001'">
                  <h3>签证信息</h3>

                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="First Name" prop="visaInfo.visaFirstName" required>

                        <el-input v-model="companion.visaInfo.visaFirstName" placeholder="请输入First Name" />
                      </el-form-item>

                    </div>
                    <div class="form-group">
                      <el-form-item label="Last Name" prop="visaInfo.visaLastName" required>

                        <el-input v-model="companion.visaInfo.visaLastName" placeholder="请输入Last Name" />
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="性别" prop="visaInfo.visaSex" required>
                        <el-radio-group v-model="companion.visaInfo.visaSex">
                          <el-radio label="sex_001">男</el-radio>
                          <el-radio label="sex_002">女</el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="出生日期" prop="visaInfo.visaBirth" required>
                        <el-date-picker v-model="companion.visaInfo.visaBirth" type="date" format="YYYY-MM-DD"
                          placeholder="请选择出生日期" style="width: 100%" />
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="申请签证地点" prop="visaInfo.visaApplyPassportSite" required>
                        <el-input v-model="companion.visaInfo.visaApplyPassportSite" placeholder="请输入申请签证地点" />
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="国籍" prop="visaInfo.visaCountry" required>
                        <el-input v-model="companion.visaInfo.visaCountry" placeholder="请输入国籍" />
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="入境日期" prop="visaInfo.visaEnterDate" required>
                        <el-date-picker v-model="companion.visaInfo.visaEnterDate" type="date" placeholder="请选择入境日期"
                          style="width: 100%" />
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="离境日期" prop="visaInfo.visaLeaveDate" required>
                        <el-date-picker v-model="companion.visaInfo.visaLeaveDate" type="date" placeholder="请选择离境日期"
                          style="width: 100%" />
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="紧急联系人" prop="visaInfo.visaUrgentContacter" required>
                        <el-input v-model="companion.visaInfo.visaUrgentContacter" placeholder="请输入紧急联系人" />
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="邀请函接收邮箱" prop="visaInfo.visaEmail" required>
                        <el-input v-model="companion.visaInfo.visaEmail" placeholder="请输入紧急联系人" />
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="访问地点" prop="visaInfo.visaVisitSite" required>
                        <el-input v-model="companion.visaInfo.visaVisitSite" placeholder="请输入访问地点" />
                      </el-form-item>
                    </div>
                  </div>

                  <div class="form-group" style="margin-bottom: 20px;">
                    <el-form-item label="组织介绍" prop="visaInfo.visaOrgIntroduce" required>
                      <el-input v-model="companion.visaInfo.visaOrgIntroduce" type="textarea" :rows="6"
                        placeholder="请用英文填写，至少150个单词" />
                      <p class="word-count" v-if="companion.visaInfo.visaOrgIntroduce">
                        当前字数：{{ countWords(companion.visaInfo.visaOrgIntroduce) }}
                      </p>
                    </el-form-item>
                  </div>

                  <div class="form-group">
                    <el-form-item label="个人简历" prop="visaInfo.visaPersonalIntroduce" required>
                      <el-input v-model="companion.visaInfo.visaPersonalIntroduce" type="textarea" :rows="6"
                        placeholder="请用英文填写，至少150个单词" />
                      <p class="word-count" v-if="companion.visaInfo.visaPersonalIntroduce">
                        当前字数：{{ countWords(companion.visaInfo.visaPersonalIntroduce) }}
                      </p>
                    </el-form-item>
                  </div>

                  <div class="form-group" style="margin-top: 16px;">
                    <ImageUpload label="护照照片" prop="visaInfo.visaImage" v-model="companion.visaInfo.visaImage"
                      required />
                  </div>
                </div>
              </el-form>
            </el-collapse-item>
          </el-collapse>

          <div class="form-actions">
            <el-button type="primary" @click="addCompanion" :disabled="companions.length >= 5">
              添加同行人
            </el-button>
            <el-button type="success" @click="submitAllCompanions" :loading="isSubmitting">
              提交所有同行人
            </el-button>
            <!-- <el-button type="success" v-if="companions.length == 0" @click="toPaymentMoney">
              支付
            </el-button> -->
            <el-button @click="goBack">返回</el-button>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { submitCompanion, getRegistrationInfo } from '../../api/registration'
import { useRegistrationStore } from '../../stores/registration'
import { useUserStore } from '../../stores/user'
import ImageUpload from '@/components/common/ImageUpload.vue'
import dayjs from 'dayjs'  // 引入 dayjs 处理日期

export default {
  name: 'CompanionRegistration',
  components: {
    ImageUpload
  },
  setup() {
    const router = useRouter()
    const registrationStore = useRegistrationStore()
    const userStore = useUserStore()
    const registrationId = computed(() => registrationStore.registrationId)
    const companionId = computed(() => registrationStore.companionId)
    const isSubmitting = ref(false)
    const formRefs = ref([])
    const activeCompanions = ref(0)
    const isEdit = ref(false)
    const registrationInfo = ref(null)
    const meeringUseInfo = ref({})
    onMounted(async () => {
      try {
        // 获取注册信息
    
        meeringUseInfo.value = await getRegistrationInfo(userStore.userInfo.id)
        // 加载同行人信息
        await loadCompanionInfo()
        
      } catch (error) {
        console.error('获取注册信息失败', error)
        ElMessage.error('获取注册信息失败')
      }
    })

    // 初始化一个同行人
    const companions = ref([])

    // 加载同行人信息
    const loadCompanionInfo = async () => {
      try {
        const savedData = sessionStorage.getItem('companionFormData')
        if (savedData) {
          const data = JSON.parse(savedData)
          // console.log(data)
          data.forEach(item=>{
            delete item.id 
            delete item.meetingVisaInfo?.id
            item.visaInfo = item.meetingVisaInfo
          })
          companions.value = Array.isArray(data) ? data : [data]
          sessionStorage.removeItem('companionFormData')
          isEdit.value = true
          // 等待 DOM 更新后重置表单校验状态
          nextTick(() => {
            formRefs.value.forEach(formRef => {
              if (formRef) {
                formRef.clearValidate()
              }
            })
          })
        } else {
          // 如果没有存储的数据，初始化一个空的同行人表单
          companions.value = [{
            firstName: '',
            lastName: '',
            sex: '',
            country: '',
            organization: '',
            position: '',
            telephone: '',
            telAddr: '',
            email: '',
            passportNum: '',
            firstToCn: 'first_to_cn_002',
            invitePassportFlag: 'invite_passport_flag_002',
            visaInfo: {
              visaFirstName: '',
              visaLastName: '',
              visaSex: '',
              visaBirth: '',
              visaCountry: '',
              visaEnterDate: '',
              visaLeaveDate: '',
              visaUrgentContacter: '',
              visaImage: '',
              visaEmail: '',
              visaOrganization: '',
              visaPosition: '',
              visaApplyPassportSite: '',
              visaVisitSite: '',
              visaOrgIntroduce: '',
              visaPersonalIntroduce: '',
              personType: 'person_type_002'
            }
          }]
        }
      } catch (error) {
        console.error('加载同行人信息失败', error)
        ElMessage.error('加载同行人信息失败')
      }
    }

    // 字数统计函数
    const countWords = (text) => {
      if (!text) return 0
      return text.trim().split(/\s+/).length
    }

    const rules = {
      firstName: [
        { required: true, message: '请输入First Name', trigger: 'blur' }
      ],
      lastName: [
        { required: true, message: '请输入Last Name', trigger: 'blur' }
      ],
      sex: [
        { required: true, message: '请选择性别', trigger: 'change' }
      ],
      country: [
        { required: true, message: '请输入国籍', trigger: 'blur' }
      ],
      organization: [
        { required: true, message: '请输入组织名称', trigger: 'blur' }
      ],
      position: [
        { required: true, message: '请输入职位', trigger: 'blur' }
      ],
      telephone: [
        { required: true, message: '请输入电话', trigger: 'blur' }
      ],
      telAddr: [
        { required: true, message: '请输入通讯地址', trigger: 'blur' }
      ],
      email: [
        { required: true, message: '请输入邮箱', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
      ],
      passportNum: [
        { required: true, message: '请输入护照号', trigger: 'blur' }
      ],
      invitePassportFlag: [
        { required: true, message: '请选择是否需要签证邀请函', trigger: 'change' }
      ],
      'visaInfo.visaFirstName': [
        { required: true, message: '请输入签证first Name', trigger: 'blur' }
      ],
      'visaInfo.visaLastName': [
        { required: true, message: '请输入签证Last Name', trigger: 'blur' }
      ],
      'visaInfo.sex': [
        { required: true, message: '请选择签证性别', trigger: 'change' }
      ],
      'visaInfo.visaBirth': [
        { required: true, message: '请选择出生日期', trigger: 'change' }
      ],
      'visaInfo.visaCountry': [
        { required: true, message: '请输入签证国籍', trigger: 'blur' }
      ],
      'visaInfo.visaEnterDate': [
        { required: true, message: '请选择入境日期', trigger: 'change' }
      ],
      'visaInfo.visaSex': [
        { required: true, message: '请选择性别', trigger: 'change' }
      ],
      'visaInfo.visaLeaveDate': [
        { required: true, message: '请选择离境日期', trigger: 'change' }
      ],
      'visaInfo.visaUrgentContacter': [
        { required: true, message: '请输入紧急联系人', trigger: 'blur' }
      ],
      'visaInfo.visaEmail': [
        { required: true, message: '请输入邀请函接收邮箱', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
      ],
      'visaInfo.visaApplyPassportSite': [
        { required: true, message: '请输入申请签证地点', trigger: 'blur' }
      ],
      'visaInfo.visaVisitSite': [
        { required: true, message: '请输入访问地点', trigger: 'blur' }
      ],
      'visaInfo.visaOrgIntroduce': [
        { required: true, message: '请输入组织介绍', trigger: 'blur' },
        { validator: (rule, value, callback) => {
          if (value && countWords(value) < 150) {
            callback(new Error('组织介绍至少需要150个单词'))
          } else {
            callback()
          }
        }, trigger: 'blur' }
      ],
      'visaInfo.visaPersonalIntroduce': [
        { required: true, message: '请输入个人简历', trigger: 'blur' },
        { validator: (rule, value, callback) => {
          if (value && countWords(value) < 150) {
            callback(new Error('个人简历至少需要150个单词'))
          } else {
            callback()
          }
        }, trigger: 'blur' }
      ],
      'visaInfo.visaImage': [
        { required: true, message: '请上传照片', trigger: 'change' }
      ]
    }
    const toPaymentMoney = () => {
      router.push('/payment')
    }
    // 添加同行人
    const addCompanion = () => {
      if (companions.value.length >= 5) {
        ElMessage.warning('最多只能添加5位同行人')
        return
      }

      companions.value.push({
        // 基本信息
        firstName: '',
        lastName: '',
        sex: '',
        country: '',
        organization: '',
        position: '',
        telephone: '',
        telAddr: '',
        email: '',
        passportNum: '',
        firstToCn: 'first_to_cn_001',
        invitePassportFlag: 'invite_passport_flag_002',

        // 签证信息
        visaInfo: {
          visaFirstName: '',
          visaLastName: '',
          visaSex: '',
          visaBirth: '',
          visaCountry: '',
          visaEnterDate: '',
          visaLeaveDate: '',
          visaUrgentContacter: '',
          visaImage: '',
          visaEmail: '',
          visaOrganization: '',
          visaPosition: '',
          visaApplyPassportSite: '',
          visaVisitSite: '',
          visaOrgIntroduce: '',
          visaPersonalIntroduce: '',
          personType: 'person_type_002' // 同行人
        }
      })

      // 展开新添加的同行人表单
      activeCompanions.value = companions.value.length - 1
    }

    // 删除同行人
    const removeCompanion = (index) => {
      companions.value.splice(index, 1)
      if (activeCompanions.value === index) {
        activeCompanions.value = Math.max(0, index - 1)
      }
    }

    // 格式化日期函数
    const formatDate = (date) => {
      if (!date) return null
      return dayjs(date).format('YYYY-MM-DD')
    }

    // 提交所有同行人
    const submitAllCompanions = async () => {
      try {
        isSubmitting.value = true

        // 验证所有表单
        const validations = await Promise.all(
          formRefs.value.map(form => form?.validate())
        )

        // 处理每个同行人的数据
        const companionsData = companions.value.map(companion => ({
          ...companion,
          signUpInfoId: registrationId.value,
          visaInfo: companion.invitePassportFlag === 'invite_passport_flag_001'
            ? {
              ...companion.visaInfo,
              visaBirth: formatDate(companion.visaInfo.visaBirth),
              visaEnterDate: formatDate(companion.visaInfo.visaEnterDate),
              visaLeaveDate: formatDate(companion.visaInfo.visaLeaveDate)
            }
            : null
        }))

        // 提交数据
        const response = await submitCompanion(companionsData)
        // 检查所有提交是否成功
        const allSuccess = response.code === 1000 ? true : false
        // registrationStore.setCompanionId(response.data.data.id)

        if (allSuccess) {
          ElMessage.success('所有同行人信息提交成功')
          router.push('/payment')
        } else {
          ElMessage.error('部分同行人信息提交失败，请重试')
        }
      } catch (error) {
        let firstErrorField = null
        for (const formRef of formRefs.value) {
          if (formRef?.$el) {
            firstErrorField = formRef.$el.querySelector('.is-error')
            if (firstErrorField) break
          }
        }
        if (firstErrorField) {
          // 滚动到错误表单项
          firstErrorField.scrollIntoView({ 
            behavior: 'smooth',
            block: 'center'
          })
        }
        console.error('提交同行人信息失败', error)
        ElMessage.error('提交同行人信息失败，请检查表单填写是否正确')
      } finally {
        isSubmitting.value = false
      }
    }

    const goBack = () => {
      try {
        // 获取用户报名信息
        // const registrationResponse = await getRegistrationInfo(userStore.userInfo.id)
        const registrationResponse = meeringUseInfo.value;
     
        
        if (registrationResponse.code === 1000) {
          const registrationData = registrationResponse.data.data[0]
          // console.log('原始报名数据:', registrationData)
          
          // 处理参会信息
          const meetingTypes = registrationData.meetingTypes ? registrationData.meetingTypes.split(',') : []
          // console.log('分割后的参会信息:', meetingTypes)
          
          // 更新数据结构
          const formattedData = {
            ...registrationData,
            meetingTypes: meetingTypes.filter(type => ['1', '2'].includes(type)),
            forums: meetingTypes.filter(type => !['1', '2'].includes(type))
          }
          // console.log('格式化后的数据:', formattedData)
          
     
          // delete mainRegistrationData.peerInfoList
          
          // 存储主报名人信息
          localStorage.setItem('registrationFormData', JSON.stringify(formattedData))
          
      
          router.push('/registration')
        } else {
          ElMessage.error('获取报名信息失败')
        }
      } catch (error) {
        console.error('获取报名信息失败', error)
        ElMessage.error('获取报名信息失败，请稍后重试')
      }
    }

    return {
      companions,
      formRefs,
      activeCompanions,
      rules,
      isSubmitting,
      addCompanion,
      removeCompanion,
      submitAllCompanions,
      countWords,
      goBack,
      formatDate,
      toPaymentMoney
    }
  }
}
</script>

<style scoped>
.companion-registration-page {
  padding-bottom: 80px;
}

.page-header {
  background: linear-gradient(rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.3)), url('@/assets/images/dahui.png');
  background-size: cover;
  background-position: center;

  height: 200px;
  text-align: center;
  margin-top: 80px;
}

.page-header h1 {
  font-size: 36px;
  margin-bottom: 10px;
}

.page-header p {
  font-size: 18px;
  opacity: 0.9;
}

.registration-form-section {
  padding: 80px 0;
}

.form-container {
  max-width: 800px;
  margin: 0 auto;
  background-color: var(--bg-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: 40px;
}

.form-container h2 {
  text-align: center;
  margin-bottom: 10px;
}

.form-desc {
  text-align: center;
  color: var(--text-light);
  margin-bottom: 30px;
}

.required {
  color: var(--error-color);
}

.form-section {
  margin-bottom: 40px;
  padding-bottom: 30px;
  border-bottom: 1px solid var(--border-color);
}

.form-section:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.form-section h3 {
  font-size: 20px;
  margin-bottom: 20px;
  color: var(--primary-color);
}

.companion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-right: 20px;
}

.companion-form {
  padding: 20px 0;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 40px;
}

.word-count {
  font-size: 12px;
  color: var(--text-light);
  margin-top: 5px;
}

.form-hint {
  font-size: 12px;
  color: var(--text-light);
  margin-top: 5px;
}

:deep(.el-collapse-item__header) {
  font-size: 16px;
  font-weight: 500;
}

:deep(.el-collapse-item__content) {
  padding: 20px;
}

:deep(.el-form-item) {
  margin-bottom: 0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--text-color);
}

:deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--border-color) inset;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px var(--primary-color) inset;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px var(--primary-color) inset;
}

:deep(.el-radio-group) {
  display: flex;
  gap: 20px;
}

:deep(.el-radio) {
  margin-right: 0;
}

:deep(.el-textarea__inner) {
  min-height: 100px;
  resize: vertical;
}

:deep(.el-date-editor) {
  width: 100%;
}

.form-row .form-group {
  margin-bottom: 20px;
}

@media (max-width: 768px) {
  .form-container {
    padding: 30px 20px;
  }

  .form-row {
    flex-direction: column;
    gap: 0;

  }

  .form-actions {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }
}
</style>