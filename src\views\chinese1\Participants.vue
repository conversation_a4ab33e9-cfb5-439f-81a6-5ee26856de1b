<template>
  <div class="participants-page">
    <!-- 页面头部 -->
    <section class="page-header">
      <div class="container">
        <h1 class="animate__animated animate__fadeInDown">重要嘉宾</h1>
        <p class="animate__animated animate__fadeInUp">汇聚全球管理咨询精英，共襄盛举</p>
      </div>
    </section>

    <!-- 参会人员内容 -->
     <div class="conference-overview-container">
    <section class="participants-section">
      <div class="container">
        <div class="about-highlights bg" id="speakers" v-intersection-observer="handleSpeakersIntersection">
          <h2 class="section-title" :class="{ 'animate__animated animate__fadeInLeft': speakersVisible }" style="margin-top: 40px;">重要嘉宾</h2>
      <div class="speakers-grid">
            <div 
              v-for="(_, index) in 9" 
              :key="index"
              class="speaker-card"
              :class="{ 'animate__animated animate__fadeInUp': speakersVisible }"
              :style="{ animationDelay: `${index * 0.15}s` }"
            >
        <div class="speaker-image image-container image-placeholder">
                <img 
                  :src="getSpeakerImage(index)" 
                  :alt="getSpeakerName(index)"
                 loading="lazy"
                  @load="handleImageLoad"
                >
        </div>
        <div class="speaker-info">
                <h3>{{ getSpeakerName(index) }}</h3>
                <p class="speaker-title">{{ getSpeakerTitle(index) }}</p>
                <p class="speaker-bio">{{ getSpeakerBio(index) }}</p>
        </div>
    </div>
  </div>
</div> 
      </div>
    </section>
     </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import 'animate.css'

export default {
  name: 'Participants',
  directives: {
    intersectionObserver: {
      mounted(el, binding) {
        const observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              binding.value(true)
            } else {
              binding.value(false)
            }
          })
        }, {
          threshold: 0.1,
          rootMargin: '0px'
        })
        observer.observe(el)
      },
      updated(el, binding) {
        const observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              binding.value(true)
            } else {
              binding.value(false)
            }
          })
        }, {
          threshold: 0.1,
          rootMargin: '0px'
        })
        observer.observe(el)
      }
    }
  },
  setup() {
    const speakersVisible = ref(false)

    const handleSpeakersIntersection = (isVisible) => {
      speakersVisible.value = isVisible
    }

    const handleImageLoad = (event) => {
      event.target.parentElement.classList.remove('image-placeholder')
    }

    // 获取嘉宾信息的辅助函数
    const getSpeakerImage = (index) => {
      const images = [
        'https://images.unsplash.com/photo-1560250097-0b93528c311a?auto=format&fit=crop&w=500&q=80',
        'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?auto=format&fit=crop&w=500&q=80',
        'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?auto=format&fit=crop&w=500&q=80'
      ]
      return images[index % images.length]
    }

    const getSpeakerName = (index) => {
      const names = ['张教授', '李博士', '王教授']
      return names[index % names.length]
    }

    const getSpeakerTitle = (index) => {
      const titles = [
        '清华大学经济管理学院教授',
        '麦肯锡全球合伙人',
        '北京大学光华管理学院教授'
      ]
      return titles[index % titles.length]
    }

    const getSpeakerBio = (index) => {
      const bios = [
        '专注于数字化转型与管理创新研究，发表多篇高水平学术论文。',
        '拥有20年管理咨询经验，专注于企业战略与数字化转型。',
        '研究领域包括企业创新、战略管理和组织变革。'
      ]
      return bios[index % bios.length]
    }

    return {
      speakersVisible,
      handleSpeakersIntersection,
      handleImageLoad,
      getSpeakerImage,
      getSpeakerName,
      getSpeakerTitle,
      getSpeakerBio
    }
  }
}
</script>

<style scoped>
@import url('@/assets/css/participants.css');
</style> 