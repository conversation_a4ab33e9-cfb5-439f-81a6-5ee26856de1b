<template>
  <div class="about-page">
    <!-- 页面头部 -->
    <section class="page-header about-header">
      <div class="container">
        <h1 class="animate__animated animate__fadeInDown">关于会议</h1>
        <p class="animate__animated animate__fadeInUp">2025年10月21-24日 · 上海浦东香格里拉大酒店</p>
      </div>
    </section>
<div class="conference-overview-container">
    <!-- 会议简介 -->
    <section class="about-intro" id="intro" v-intersection-observer="handleIntroIntersection">
      <div class="container">
        <h2 class="section-title" :class="{ 'animate__animated animate__fadeInDown': introVisible }">会议简介</h2>
        <div class="intro-grid">
          <div class="intro-content" :class="{ 'animate__animated animate__fadeInLeft': introVisible }">
            <!-- <h2>CMC&ICMCI 2025上海国际管理咨询大会</h2> -->
            <p class="lead">数智引领未来，咨询助推管理创新与可持续发展</p>
            <p>ICMCI国际管理咨询大会是全球管理咨询行业最具影响力的年度盛会，汇聚来自世界各地的管理咨询精英、企业领袖和学术专家，共同探讨行业发展趋势、分享最佳实践经验。</p>
            <div class="key-info">
              <div class="info-item" :class="{ 'animate__animated animate__fadeInUp': introVisible }" :style="{ animationDelay: '0.2s' }">
                <i class="fas fa-calendar-alt"></i>
                <div class="info-content">
                  <h4>会议时间</h4>
                  <p>2025年10月21-24日</p>
                </div>
              </div>
              <div class="info-item" :class="{ 'animate__animated animate__fadeInUp': introVisible }" :style="{ animationDelay: '0.3s' }">
                <i class="fas fa-map-marker-alt"></i>
                <div class="info-content">
                  <h4>会议地点</h4>
                  <p>上海浦东香格里拉大酒店</p>
                </div>
              </div>
            </div>
          </div>
          <div class="intro-image" :class="{ 'animate__animated animate__fadeInRight': introVisible }">
            <img src="@/assets/images/about1.jpg" alt="会议场景">
          </div>
        </div>
      </div>
    </section>

    <!-- 会议主题 -->
    <section class="about-theme bg" id="theme" v-intersection-observer="handleThemeIntersection">
      <div class="container">
        <h2 class="section-title" :class="{ 'animate__animated animate__fadeInDown': themeVisible }">会议主题</h2>
        <div class="theme-content">
          <p class="theme-desc" :class="{ 'animate__animated animate__fadeIn': themeVisible }">国际CMC大会会议主题：数智引领未来，咨询助推管理创新与可持续发展
            数字化不仅对产业对企业对社会都会产生深远的影响，人工智能（AI）无疑成为了最具变革性力量的领域之一。企业如何应对新一轮挑战？管理咨询如何迅速融入新浪潮，创新管理理念，研发新工具，助力企业实现高质量可持续发展。</p>
          <div class="theme-grid">
            <div class="theme-item" v-for="(item, index) in 4" :key="index"
              :class="{ 'animate__animated animate__fadeInUp': themeVisible }"
              :style="{ animationDelay: `${index * 0.2}s` }">
              <div class="theme-icon">
                <i class="fas fa-robot"></i>
              </div>
              <h3>人工智能与管理咨询</h3>
              <p>探讨AI技术如何重塑管理咨询行业，以及咨询顾问如何利用AI工具提升服务价值。</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 会议亮点 -->
    <section class="about-highlights" id="highlights" v-intersection-observer="handleHighlightsIntersection">
      <div class="container">
        <h2 class="section-title" :class="{ 'animate__animated animate__fadeInDown': highlightsVisible }">会议亮点</h2>
        <div class="highlights-grid">
          <div class="highlight-card" v-for="(item, index) in 4" :key="index"
            :class="{ 'animate__animated animate__fadeInUp': highlightsVisible }"
            :style="{ animationDelay: `${index * 0.2}s` }">
            <div class="highlight-icon">
              <i class="fas fa-globe"></i>
            </div>
            <h3>国际化视野</h3>
            <p>汇聚来自30多个国家和地区的管理咨询专家，共同探讨全球管理咨询行业的发展趋势。</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 重要嘉宾 -->
    <div class="about-highlights bg" id="speakers" v-intersection-observer="handleSpeakersIntersection">
      <h2 class="section-title" :class="{ 'animate__animated animate__fadeInDown': speakersVisible }">重要嘉宾</h2>
      <div class="more-speakers" @click="getLocalePath($router.push('participants'))">查看更多</div>
      <div class="speakers-grid">
        <div class="speaker-card" v-for="(item, index) in 6" :key="index"
          :class="{ 'animate__animated animate__fadeInUp': speakersVisible }"
          :style="{ animationDelay: `${index * 0.2}s` }">
          <div class="speaker-image image-container image-placeholder">
            <img src="@/assets/images/speaker.jpg" 
                 alt="张教授"
                 loading="lazy"
                 onload="this.parentElement.classList.remove('image-placeholder')">
          </div>
          <div class="speaker-info">
            <h3>张教授</h3>
            <p class="speaker-title">清华大学经济管理学院教授</p>
            <p class="speaker-bio">专注于数字化转型与管理创新研究，发表多篇高水平学术论文。</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 分论坛内容 -->
    <section class="forum-section" id="forum" v-intersection-observer="handleForumIntersection">
      <h2 class="section-title" :class="{ 'animate__animated animate__fadeInDown': forumVisible }">分论坛会议</h2>
      <div class="container">
        <div class="forum-grid">
          <div class="forum-card" v-for="(item, index) in 5" :key="index"
            :class="{ 'animate__animated animate__fadeInUp': forumVisible }"
            :style="{ animationDelay: `${index * 0.2}s` }">
            <div class="forum-image">
              <img src="https://picsum.photos/800/400?random=1" alt="AI重塑产业格局">
            </div>
            <div class="forum-content">
              <h2>各国CMC交流管理咨询经验，探讨管理咨询新趋势</h2>
              <p>探讨人工智能技术如何改变传统产业格局，分析对企业管理带来的机遇与挑战，分享前沿实践案例。</p>
              <div class="forum-details">
                <div class="detail-item">
                  <i class="fas fa-calendar"></i>
                  <span>2024年10月22日 14:00-16:00</span>
                </div>
                <div class="detail-item">
                  <i class="fas fa-map-marker-alt"></i>
                  <span>上海国际会议中心 - 分会场A</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 合作伙伴 -->
    <section class="about-theme bg" id="partners" v-intersection-observer="handlePartnersIntersection">
      <div class="container">
        <h2 class="section-title" :class="{ 'animate__animated animate__fadeInDown': partnersVisible }">合作伙伴</h2>
        <div class="theme-content">
          <p class="theme-desc" :class="{ 'animate__animated animate__fadeIn': partnersVisible }">
            中国国际商会（CCOIC）是中国最大的贸易投资促进机构，拥有超过20万家会员企业，覆盖国民经济各个行业。作为中国贸促会（CCPIT）的直属机构，中国国际商会致力于促进中外经贸交流与合作。
          </p>
          <div class="theme-grid">
            <div class="theme-item" v-for="(item, index) in partnerItems" :key="index"
              :class="{ 'animate__animated animate__fadeInUp': partnersVisible }"
              :style="{ animationDelay: `${index * 0.2}s` }">
              <div class="theme-icon">
                <i :class="item.icon"></i>
              </div>
              <h3>{{ item.title }}</h3>
              <p>{{ item.desc }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 媒体支持 -->
    <section class="media-support bg" id="media" v-intersection-observer="handleMediaIntersection">
      
      <div class="container">
        <h2 class="section-title" :class="{ 'animate__animated animate__fadeInDown': mediaVisible }">媒体支持</h2>
        <div class="media-desc" :class="{ 'animate__animated animate__fadeIn': mediaVisible }">感谢以下媒体对本次大会的大力支持</div>
        <div class="media-grid">
          <div class="media-card" v-for="(item, index) in 4" :key="index"
            :class="{ 'animate__animated animate__fadeInUp': mediaVisible }"
            :style="{ animationDelay: `${index * 0.2}s` }">
            <div class="media-icon">
              <i class="fas fa-newspaper"></i>
            </div>
            <div class="media-info">
              <h3>新华网</h3>
              <p>国家重点新闻网站</p>
              <div class="media-tags">
                <span>官方媒体</span>
                <span>全程报道</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 住宿信息模块 -->
    <section class="about-hotel " id="hotel">
      <div class="container">
        <h2 class="section-title" :class="{ 'animate__animated animate__fadeInDown': forumVisible }">住宿信息</h2>
        <!-- <h2 class="section-title"></h2> -->
        <div class="hotel-grid">
          <div class="hotel-card">
            <div class="hotel-image">
              <img src="@/assets/images/shanghaipudong.jpg" alt="香格里拉大酒店">
            </div>
            <div class="hotel-info">
              <h3>上海浦东香格里拉大酒店</h3>
              <p class="hotel-address">地址：上海市浦东新区富城路33号</p>
              <p class="hotel-desc">位于黄浦江畔，毗邻陆家嘴金融区，拥有优雅舒适的客房和世界级的会议设施，是本次大会主会场及推荐下榻酒店。</p>
              <a class="hotel-link" href="https://www.shangri-la.com/cn/shanghai/pudongshangrila/" target="_blank">酒店详情</a>
              <el-button type="primary" v-if="hotelMoney">在线预订</el-button>
            </div>
          </div>
          <div class="hotel-card">
            <div class="hotel-image">
              <img src="@/assets/images/yangguangdajiudian.jpg" alt="阳光大酒店">
            </div>
            <div class="hotel-info">
              <h3>上海阳光大酒店</h3>
              <p class="hotel-address">地址：上海市静安区延安中路789号</p>
              <p class="hotel-desc">地理位置优越，交通便利，配备多样化的客房和完善的配套设施，是参会嘉宾的理想住宿选择。</p>
              <a class="hotel-link" href="https://www.sunshinehotelshanghai.com/" target="_blank">酒店详情</a>
              <el-button type="primary" v-if="hotelMoney">在线预订</el-button>
            </div>
          </div>
        </div>
      </div>
    </section>


  </div>
  </div>
</template>

<script setup>
import { onMounted, watch, ref } from 'vue'
import { useRoute } from 'vue-router'
import { getHotelInfo } from '@/api/agenda'
import { useUserStore } from "../../stores/user";
import { useLanguage } from '@/hooks/useLanguage'
const route = useRoute()
const userStore = useUserStore();
const {getLocalePath} = useLanguage()

// 添加动画状态控制
const introVisible = ref(false)
const themeVisible = ref(false)
const highlightsVisible = ref(false)
const speakersVisible = ref(false)
const forumVisible = ref(false)
const mediaVisible = ref(false)
const partnersVisible = ref(false)

// 处理各个部分的可见性
const handleIntroIntersection = (isVisible) => {
  if (isVisible && !introVisible.value) {
    introVisible.value = true
  }
}

const handleThemeIntersection = (isVisible) => {
  if (isVisible && !themeVisible.value) {
    themeVisible.value = true
  }
}

const handleHighlightsIntersection = (isVisible) => {
  if (isVisible && !highlightsVisible.value) {
    highlightsVisible.value = true
  }
}

const handleSpeakersIntersection = (isVisible) => {
  if (isVisible && !speakersVisible.value) {
    speakersVisible.value = true
  }
}

const handleForumIntersection = (isVisible) => {
  if (isVisible && !forumVisible.value) {
    forumVisible.value = true
  }
}

const handleMediaIntersection = (isVisible) => {
  if (isVisible && !mediaVisible.value) {
    mediaVisible.value = true
  }
}

const handlePartnersIntersection = (isVisible) => {
  if (isVisible && !partnersVisible.value) {
    partnersVisible.value = true
  }
}

// 添加自定义指令
const vIntersectionObserver = {
  mounted(el, binding) {
    if (typeof binding.value !== 'function') {
      console.warn('v-intersection-observer 指令需要一个函数作为值')
      return
    }

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          try {
            binding.value(true)
          } catch (error) {
            console.error('执行 intersection observer 回调时出错:', error)
          }
        }
      })
    }, {
      threshold: 0.1,
      rootMargin: '0px'
    })

    try {
      observer.observe(el)
    } catch (error) {
      console.error('观察元素时出错:', error)
    }
  },
  unmounted(el) {
    // 清理 observer
    if (el._observer) {
      el._observer.disconnect()
    }
  }
}

function scrollToAnchor() {
  const anchor = route.query.anchor
  if (anchor) {
    setTimeout(() => {
      const el = document.getElementById(anchor)
      if (el) {
        el.scrollIntoView({ behavior: 'smooth' })
      }
    }, 500)
  }
}

const hotelMoney = ref(false)

const partnerItems = [
  {
    icon: 'fas fa-handshake',
    title: '促进中外经贸交流',
    desc: '搭建国际经贸合作平台，促进中外企业交流与合作'
  },
  {
    icon: 'fas fa-globe-asia',
    title: '推动企业走出去',
    desc: '协助中国企业开拓国际市场，实现全球化发展'
  },
  {
    icon: 'fas fa-building',
    title: '协助外资进入',
    desc: '为外国企业提供中国市场准入和投资咨询服务'
  },
  {
    icon: 'fas fa-chart-line',
    title: '专业贸易服务',
    desc: '提供专业的贸易投资促进和商事法律服务'
  }
]

onMounted(() => {
  scrollToAnchor()
  // debugger
  if(userStore.userInfo.account){
    getHotelInfo(userStore.userInfo.account).then(res=>{
      console.log(res)
      hotelMoney.value = res.data.data;
    })
  }
})

watch(() => route.query.anchor, (newAnchor) => {
  if (newAnchor) {
    scrollToAnchor()
  }
}, { immediate: true })

</script>

<style scoped>
@import url('@/assets/css/about.css');



/* 其他现有样式保持不变 */
</style>
