.conference-overview-container{
    background: url('@/assets/images/home-bg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    min-height: 100vh;
    
  }
  .page-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1565c0 100%);
    color: var(--text-white);
    padding: 120px 0 60px;
    text-align: center;
  }
  
  .page-header h1 {
    font-size: 36px;
    margin-bottom: 10px;
  }
  
  .dashboard-section {
    padding: 80px 0;
    /* background-color: var(--bg-light); */
  }
  
  .dashboard-container {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .dashboard-sidebar {
    background-color: var(--bg-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    padding: 30px;
    height: fit-content;
  }
  
  .user-profile {
    display: flex;
    align-items: center;
    gap: 15px;
    padding-bottom: 20px;
    margin-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
  }
  
  .user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
  }
  
  .user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .user-info h3 {
    font-size: 18px;
    margin-bottom: 5px;
  }
  
  .user-info p {
    color: var(--text-light);
    font-size: 14px;
  }
  
  .dashboard-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }
  
  .dashboard-nav a {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 15px;
    border-radius: var(--radius-sm);
    color: var(--text-color);
    transition: var(--transition);
    margin-bottom: 5px;
  }
  
  .dashboard-nav a:hover {
    background-color: rgba(30, 136, 229, 0.1);
  }
  
  .dashboard-nav a.active {
    background-color: var(--primary-color);
    color: var(--text-white);
  }
  
  .sidebar-footer {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
  }
  
  .dashboard-content {
    background-color: var(--bg-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    padding: 30px;
    overflow: hidden;
  }
  
  .dashboard-tab-content h2 {
    font-size: 24px;
    margin-bottom: 30px;
    color: var(--primary-color);
  }
  
  .overview-cards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 40px;
  }
  
  .overview-card {
    background-color: var(--bg-light);
    border-radius: var(--radius-md);
    padding: 20px;
    display: flex;
    gap: 15px;
    flex-direction: column;
    text-align: center;
    justify-content: center;
    align-items: center;
  }
  
  .card-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: rgba(30, 136, 229, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: var(--primary-color);
  }
  
  .card-content h3 {
    font-size: 16px;
    margin-bottom: 5px;
  }
  
  .status-confirmed {
    color: #4caf50;
    font-weight: 500;
  }
  
  .card-action {
    display: inline-block;
    margin-top: 10px;
    font-size: 14px;
    color: var(--primary-color);
  }
  
  .upcoming-events h3 {
    font-size: 18px;
    margin-bottom: 20px;
  }
  
  .event-timeline {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  
  .timeline-item {
    display: flex;
    gap: 20px;
    background-color: var(--bg-light);
    border-radius: var(--radius-md);
    padding: 20px;
  }
  
  .timeline-date {
    text-align: center;
    min-width: 60px;
  }
  
  .date-day {
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
  }
  
  .date-month {
    font-size: 14px;
    color: var(--text-light);
  }
  
  .timeline-content h4 {
    font-size: 16px;
    margin-bottom: 5px;
  }
  
  .timeline-content p {
    color: var(--text-light);
    font-size: 14px;
  }
  
  .profile-form {
    max-width: 800px;
  }
  
  .form-section {
    margin-bottom: 40px;
    padding-bottom: 30px;
    border-bottom: 1px solid var(--border-color);
  }
  
  .form-section:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
  }
  
  .form-section h3 {
    font-size: 18px;
    margin-bottom: 20px;
    color: var(--primary-color);
  }
  
  .form-hint {
    font-size: 12px;
    color: var(--text-light);
    margin-top: 5px;
  }
  
  .form-actions {
    display: flex;
    gap: 15px;
    margin-top: 30px;
  }
  
  @media (max-width: 992px) {
    .dashboard-container {
      grid-template-columns: 1fr;
    }
  
    .overview-cards {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  @media (max-width: 576px) {
    .overview-cards {
      grid-template-columns: 1fr;
    }
  
    .form-row {
      grid-template-columns: 1fr;
    }
    
    .form-actions {
      flex-direction: column;
    }
  }