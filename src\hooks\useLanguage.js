import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'

export function useLanguage() {
  const route = useRoute()
  const router = useRouter()

  // 获取当前语言
  const currentLanguage = computed(() => {
    // 优先从localStorage获取语言设置
    const savedLang = localStorage.getItem('preferredLanguage')
    if (savedLang) {
      return savedLang
    }
    // 如果没有保存的语言设置，则从路由路径判断
    return route.path.startsWith('/english') ? 'english' : 'chinese'
  })

  // 获取当前语言的基础路径
  const languagePath = computed(() => {
    return `/${currentLanguage.value}`
  })

  // 切换语言
  const switchLanguage = () => {
    const newLang = currentLanguage.value === 'chinese' ? 'english' : 'chinese'
    // 保存语言选择到localStorage
    localStorage.setItem('preferredLanguage', newLang)
    const newPath = route.path.replace(`/${currentLanguage.value}`, `/${newLang}`)
    return router.push(newPath)
  }

  // 获取带语言前缀的路径
  const getLocalePath = (path) => {
    // 如果path已经包含语言前缀，直接返回
    if (path.startsWith('/chinese/') || path.startsWith('/english/')) {
      return path
    }
    // 去掉可能存在的前导斜杠
    const cleanPath = path.replace(/^\//, '')
    return `${languagePath.value}/${cleanPath}`
  }

  // 重定向到带语言前缀的路径
  const redirectToLocalePath = (path) => {
    const localePath = getLocalePath(path)
    return router.push(localePath)
  }

  // 检查路径是否包含语言前缀
  const hasLanguagePrefix = (path) => {
    return path.startsWith('/chinese/') || path.startsWith('/english/')
  }

  return {
    currentLanguage,
    languagePath,
    switchLanguage,
    getLocalePath,
    redirectToLocalePath,
    hasLanguagePrefix
  }
} 