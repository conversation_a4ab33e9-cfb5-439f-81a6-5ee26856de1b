{"name": "cice-vue", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@fortawesome/fontawesome-free": "^6.7.2", "animate.css": "^4.1.1", "axios": "^1.8.4", "dayjs": "^1.11.13", "element-plus": "^2.9.7", "html2pdf.js": "^0.10.3", "pinia-plugin-persistedstate": "^4.2.0", "qrcode.js": "^0.0.1", "scrollreveal": "^4.0.9", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "pinia": "^3.0.2", "sm-crypto": "^0.3.13", "vite": "^6.2.0"}}