<template>
  <div class="combined-registration-page">
    <!-- 页面头部 -->
    <section class="page-header">
      <div class="container">
        <!-- <h1 class="animate__animated animate__fadeInDown">Register for the conference</h1>
      <p class="animate__animated animate__fadeInUp">October 21-24, 2025 · Pudong Shangri-La, Shanghai</p> -->
      </div>
    </section>
    <div class="conference-overview-container">
    <!-- 报名表单 -->
    <section class="registration-form-section">
      <div class="container">
        <div class="form-container">
          <h2>Register for the conference</h2>
          <p class="form-desc">
            Please fill in the following information to complete the registration. Fields marked with <span class="required">*</span> are mandatory
           
          </p>

          <!-- 报名人表单 -->
          <el-collapse v-model="activeFormsMain">
            <el-collapse-item name="main">
              <template #title>
                <div class="form-header">
                  <span>【 Attendee Information 】 {{ mainForm.firstName }}&nbsp;{{ mainForm.lastName }}</span>
                </div>
              </template>
              <el-form ref="mainFormRef" :model="mainForm" :rules="mainRules" class="registration-form"
                label-position="top">
                <!-- 报名人表单内容 -->
                <div class="form-section">
                  <h3>Basic information</h3>
                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="Surname/Family name" prop="firstName" required>
                        <el-input v-model="mainForm.firstName" placeholder="Please enter your Surname/Family name" />
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="First name/Given name" prop="lastName" required>
                        <el-input v-model="mainForm.lastName" placeholder="Please enter your First name/Given name" />
                      </el-form-item>
                    </div>
                  </div>

                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="Gender" prop="sex" required>
                        <el-radio-group v-model="mainForm.sex">
                          <el-radio label="sex_001">Male</el-radio>
                          <el-radio label="sex_002">Female</el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="Nationality/Region" prop="country" required>
                        <el-select v-model="mainForm.country" placeholder="Please select your Nationality/Region">
                          <el-option v-for="item in countryOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                        </el-select>
                      </el-form-item>
                    </div>
                  </div>

                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="Organization/institution Name" prop="organization" required>
                        <el-input v-model="mainForm.organization" placeholder="Please enter your organization/institution Name" />
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="Position" prop="position" required>
                        <el-input v-model="mainForm.position" placeholder="Please enter your position" />
                      </el-form-item>
                    </div>
                  </div>

                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="Phone No." prop="telephone" required>
                        <el-input v-model="mainForm.telephone" placeholder="Please enter your Phone No." />
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="Mail address" prop="telAddr" required>
                        <el-input v-model="mainForm.telAddr" placeholder="Please enter your mail address" />
                      </el-form-item>
                    </div>
                  </div>

                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="Email" prop="email" required>
                        <el-input v-model="mainForm.email" placeholder="Please enter your Email" />
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="Passport No." prop="passportNum" required>
                        <el-input v-model="mainForm.passportNum" placeholder="Please enter your passport No." />
                      </el-form-item>
                    </div>
                  </div>

                  <div class="form-group">
                    <el-form-item label="Organizational Profile" prop="orgIntroduce" required>
                      <el-input v-model="mainForm.orgIntroduce" type="textarea" :rows="4" placeholder="Please enter your organizational profile" />
                      <p class="word-count" v-if="mainForm.orgIntroduce">
                        Current word count：{{ countWords(mainForm.orgIntroduce) }}
                      </p>
                    </el-form-item>
                  </div>

                  <div class="form-group" style="margin: 20px 0">
                    <el-form-item label="Personal Profile" prop="personalIntroduce" required>
                      <el-input v-model="mainForm.personalIntroduce" type="textarea" :rows="4" placeholder="Please enter your personal profile" />
                      <p class="word-count" v-if="mainForm.personalIntroduce">
                        Current word count：{{ countWords(mainForm.personalIntroduce) }}
                      </p>
                    </el-form-item>
                  </div>

                  <div class="form-group">
                    <ImageUpload label="Passport photo" prop="passportImageList" v-model="mainForm.passportImageList"
                      :required="true" />
                  </div>
                  <div class="form-group" style="margin: 20px 0">
                    <el-form-item label="Participate in activities" prop="meetingType" required>
                      <el-checkbox-group v-model="mainForm.meetingType" @change="handleCheckedChange" class="meeting-type-group">
                        <el-checkbox 
                          v-for="item in participationList" 
                          :key="item.value" 
                          :label="item.value"
                          :disabled="isOptionDisabled(item.value)"
                          class="meeting-type-checkbox"
                        >
                          <div class="meeting-type-content">
                            <div class="meeting-type-info">
                              <span class="meeting-date">{{item.date}}</span>
                              <span class="meeting-label">{{item.label}}</span>
                            </div>
                            <span class="meeting-price">EUR {{item.price}}</span>
                          </div>
                        </el-checkbox>
                      </el-checkbox-group>
                    </el-form-item>
                  </div>
                  <div class="form-row" style="margin: 20px 0">
                    <div class="form-group">
                      <el-form-item label="Is it your first time coming to China?" prop="firstToCn" required>
                        <el-radio-group v-model="mainForm.firstToCn">
                          <el-radio label="first_to_cn_001">YES</el-radio>
                          <el-radio label="first_to_cn_002">NO</el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item prop="invitePassportFlag" required>
                        <template #label>
                          Do you need a visa invitation letter?(Only covering the conference period and round-trip travel for a total of five days)
                          <!-- <el-tooltip content="只涵盖会议期间和路上往返共五天" placement="top">
                            <el-icon style="margin-left: 2px; cursor: pointer">
                              <QuestionFilled style="padding-top: 2px" />
                            </el-icon>
                          </el-tooltip> -->
                        </template>
                        <el-radio-group v-model="mainForm.invitePassportFlag">
                          <el-radio label="invite_passport_flag_001">YES</el-radio>
                          <el-radio label="invite_passport_flag_002">NO</el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </div>
                  </div>
                </div>

                <!-- 报名人签证信息部分 -->
                <div class="form-section" v-if="
                  mainForm.invitePassportFlag === 'invite_passport_flag_001'
                ">
                  <h3>Visa</h3>

                  <div class="form-row">
                    <!-- <div class="form-group">
                      <el-form-item
                        label="性别"
                        prop="visaInfo.visaSex"
                        required
                      >
                        <el-radio-group v-model="mainForm.visaInfo.visaSex">
                          <el-radio label="sex_001">Male</el-radio>
                          <el-radio label="sex_002">Female</el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </div> -->
                    <div class="form-group">
                      <el-form-item label="Date of Birth" prop="visaInfo.visaBirth" required>
                        <el-date-picker v-model="mainForm.visaInfo.visaBirth" type="date" placeholder="Please select Date of Birth"
                          value-format="YYYY-MM-DD" />
                      </el-form-item>
                    </div>

                    <div class="form-group">
                      <el-form-item label="Date of Entry" prop="visaInfo.visaEnterDate" required>
                        <el-date-picker v-model="mainForm.visaInfo.visaEnterDate" type="date" placeholder="Please select date of entry"
                          value-format="YYYY-MM-DD" :disabled-date="disableOtherDates" :default-value="new Date('2025-10-01')"/>
                      </el-form-item>
                    </div>
                  </div>

                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="Date of departure" prop="visaInfo.visaLeaveDate" required>
                        <el-date-picker v-model="mainForm.visaInfo.visaLeaveDate" type="date" placeholder="Please select date of departure"
                          value-format="YYYY-MM-DD" :disabled-date="disableOtherDates" :default-value="new Date('2025-10-01')"/>
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="Emergency contact" prop="visaInfo.visaUrgentContacter" required>
                        <el-input v-model="mainForm.visaInfo.visaUrgentContacter" placeholder="Please enter your emergency contact" />
                      </el-form-item>
                    </div>
                  </div>

                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="Location for passport application" prop="visaInfo.visaApplyPassportSite" required>
                        <!-- <el-select
                          v-model="mainForm.visaInfo.visaApplyPassportSite"
                          placeholder="Please select 申请护照地点"
                        >
                          <el-option
                            v-for="item in passportSiteOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select> -->
                        <el-input v-model="mainForm.visaInfo.visaApplyPassportSite" placeholder="Please select location for passport application" />
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="Places to Visit" prop="visaInfo.visaVisitSite" required>
                        <!-- <el-select
                          v-model="mainForm.visaInfo.visaVisitSite"
                          placeholder="Please select 访问地点"
                        >
                          <el-option
                            v-for="item in visitSiteOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select> -->
                        <el-input v-model="mainForm.visaInfo.visaVisitSite" placeholder="Please enter your places to Visit" />
                      </el-form-item>
                    </div>
                  </div>
                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="Invitation letter receiving email" prop="visaInfo.visaEmail" required>
                        <el-input v-model="mainForm.visaInfo.visaEmail" placeholder="Please enter your invitation letter receiving email" />
                      </el-form-item>
                    </div>
                  </div>
                </div>
              </el-form>
            </el-collapse-item>
          </el-collapse>
          <!-- 同行人表单 -->

       
          <el-collapse v-model="activeForms" @change="handleCollapseChange">
            <el-collapse-item v-for="(companion, index) in companions" :key="index" :name="'companion-' + index">
              <template #title>
                <div class="form-header">
                  <span>【 Companion Information 】{{ companion.firstName }}&nbsp;{{ companion.lastName }}</span>
                  <el-button type="danger" size="small" @click.stop="removeCompanion(companion, index)">
                    Delete
                  </el-button>
                </div>
              </template>
              <el-form :ref="(el) => (companionFormRefs[index] = el)" :model="companion" :rules="companionRules"
                class="companion-form" label-position="top">
                <div class="form-section">
                  <h3>Basic information</h3>
                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="Surname/Family name" prop="firstName" required>
                        <el-input v-model="companion.firstName" placeholder="Please enter your Surname/Family name" />
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="First name/Given name" prop="lastName" required>
                        <el-input v-model="companion.lastName" placeholder="Please enter your First name/Given name" />
                      </el-form-item>
                    </div>
                  </div>

                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="Gender" prop="sex" required>
                        <el-radio-group v-model="companion.sex">
                          <el-radio label="sex_001">Male</el-radio>
                          <el-radio label="sex_002">Female</el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="Nationality/Region" prop="country" required>
                        <el-select v-model="companion.country" placeholder="Please select your nationality">
                          <el-option v-for="item in countryOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                        </el-select>
                      </el-form-item>
                    </div>
                  </div>

                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="Organization/institution Name" prop="organization" required>
                        <el-input v-model="companion.organization" placeholder="Please enter your organization/institution Name" />
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="Position" prop="position" required>
                        <el-input v-model="companion.position" placeholder="Please enter your position" />
                      </el-form-item>
                    </div>
                  </div>

                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="Phone No." prop="telephone" required>
                        <el-input v-model="companion.telephone" placeholder="Please enter your Phone No." />
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="Mail address" prop="telAddr" required>
                        <el-input v-model="companion.telAddr" placeholder="Please enter your mail address" />
                      </el-form-item>
                    </div>
                  </div>

                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="Email" prop="email" required>
                        <el-input v-model="companion.email" placeholder="Please enter your Email" />
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="Passport No." prop="passportNum" required>
                        <el-input v-model="companion.passportNum" placeholder="Please enter your passport No." />
                      </el-form-item>
                    </div>
                  </div>

                  <div class="form-group">
                    <el-form-item label="Organizational Profile" prop="orgIntroduce" required>
                      <el-input v-model="companion.orgIntroduce" type="textarea" :rows="4" placeholder="Please enter your organizational profile" />
                      <p class="word-count" v-if="companion.orgIntroduce">
                        Current word count：{{ countWords(companion.orgIntroduce) }}
                      </p>
                    </el-form-item>
                  </div>

                  <div class="form-group" style="margin: 20px 0">
                    <el-form-item label="Personal Profile" prop="personalIntroduce" required>
                      <el-input v-model="companion.personalIntroduce" type="textarea" :rows="4" placeholder="Please enter your personal profile" />
                      <p class="word-count" v-if="companion.personalIntroduce">
                        Current word count：{{ countWords(companion.personalIntroduce) }}
                      </p>
                    </el-form-item>
                  </div>

                  <div class="form-group">
                    <ImageUpload label="Passport photo" prop="passportImageList" v-model="companion.passportImageList"
                      :required="true" />
                  </div>
                  <div class="form-group" style="margin: 20px 0">
                    <el-form-item label="Participate in activities" prop="meetingType" required>
                      <el-checkbox-group v-model="companion.meetingType" @change="(val) => handleCompanionCheckedChange(val, index)" class="meeting-type-group">
                        <el-checkbox 
                          v-for="item in participationList" 
                          :key="item.value" 
                          :label="item.value"
                          :disabled="isCompanionOptionDisabled(item.value, index)"
                          class="meeting-type-checkbox"
                        >
                          <div class="meeting-type-content">
                            <div class="meeting-type-info">
                              <span class="meeting-date">{{item.date}}</span>
                              <span class="meeting-label">{{item.label}}</span>
                            </div>
                            <span class="meeting-price">EUR {{item.price}}</span>
                          </div>
                        </el-checkbox>
                      </el-checkbox-group>
                    </el-form-item>
                  </div>
                  <div class="form-row" style="margin: 20px 0">
                    <div class="form-group">
                      <el-form-item label="Is it your first time coming to China?" prop="firstToCn" required>
                        <el-radio-group v-model="companion.firstToCn">
                          <el-radio label="first_to_cn_001">YES</el-radio>
                          <el-radio label="first_to_cn_002">NO</el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </div>
              
                    <div class="form-group">
                      <el-form-item prop="invitePassportFlag" required>
                        <template #label>
                          Do you need a visa invitation letter?(Only covering the conference period and round-trip travel for a total of five days)
                          <!-- <el-tooltip content="只涵盖会议期间和路上往返共五天" placement="top">
                            <el-icon style="margin-left: 2px; cursor: pointer">
                              <QuestionFilled style="padding-top: 2px" />
                            </el-icon>
                          </el-tooltip> -->
                        </template>
                        <el-radio-group v-model="companion.invitePassportFlag">
                          <el-radio label="invite_passport_flag_001">YES</el-radio>
                          <el-radio label="invite_passport_flag_002">NO</el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </div>
                  </div>
                </div>

                <!-- 同行人签证信息部分 -->
                <div class="form-section" v-if="
                  companion.invitePassportFlag === 'invite_passport_flag_001'
                ">
                  <h3>Visa Information</h3>
                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="Date of Birth" prop="visaInfo.visaBirth" required>
                        <el-date-picker v-model="companion.visaInfo.visaBirth" type="date" placeholder="Please select date of birth"
                          value-format="YYYY-MM-DD" />
                      </el-form-item>
                    </div>
                    <!-- </div>

                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item
                        label="国籍"
                        prop="visaInfo.visaCountry"
                        required
                      >
                        <el-select
                          v-model="companion.visaInfo.visaCountry"
                          placeholder="Please select 国籍"
                        >
                          <el-option
                            v-for="item in countryOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </div> -->
                    <div class="form-group">
                      <el-form-item label="Date of Entry" prop="visaInfo.visaEnterDate" required>
                        <el-date-picker v-model="companion.visaInfo.visaEnterDate" type="date" placeholder="Please select date of entry"
                          value-format="YYYY-MM-DD" :disabled-date="disableOtherDates" :default-value="new Date('2025-10-01')"/>
                      </el-form-item>
                    </div>
                  </div>

                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="Date of departure" prop="visaInfo.visaLeaveDate" required>
                        <el-date-picker v-model="companion.visaInfo.visaLeaveDate" type="date" placeholder="Please select date of departure"
                          value-format="YYYY-MM-DD" :disabled-date="disableOtherDates" :default-value="new Date('2025-10-01')"/>
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="Emergency contact" prop="visaInfo.visaUrgentContacter" required>
                        <el-input v-model="companion.visaInfo.visaUrgentContacter" placeholder="Please enter your emergency contact" />
                      </el-form-item>
                    </div>
                  </div>

                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="Location for passport application" prop="visaInfo.visaApplyPassportSite" required>
                        <el-input v-model="companion.visaInfo.visaApplyPassportSite" placeholder="Please select location for passport application" />
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="Places to Visit" prop="visaInfo.visaVisitSite" required>
                        <el-input v-model="companion.visaInfo.visaVisitSite" placeholder="Please enter your places to Visit" />
                      </el-form-item>
                    </div>
                  </div>
                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="Invitation letter receiving email" prop="visaInfo.visaEmail" required>
                        <el-input v-model="companion.visaInfo.visaEmail" placeholder="Please enter your invitation letter receiving email" />
                      </el-form-item>
                    </div>
                  </div>
                </div>
              </el-form>
            </el-collapse-item>
          </el-collapse>
          <div style="
              display: flex;
              justify-content: flex-end;
              margin: 12px 45px 12px 0;
            ">
            <el-button type="primary" @click="addCompanion" size="small" :disabled="companions.length >= 5">
              Add peers info
            </el-button>
          </div>
          <div class="total-amount" >
            <span class="amount-label">Total Cost：</span>
            <span class="amount-value">EUR {{ calculateAmount() }}</span>
          </div>
          
          <!-- 参会信息 -->
          <!-- <div class="form-section" style="margin-top: 10px;">
            <p>参会信息</p>
            <ConferenceRegistration v-model="mainForm" :companions="companions" />
          </div> -->

          <!-- 操作按钮 -->
          <div class="form-actions">
            <el-button type="primary" @click="submitAll('save')" :loading="isSubmitting">
             Save
            </el-button>
            <el-button type="success" @click="submitAll('pay')" :loading="isSubmitting">
              Pay
            </el-button>
            <!-- <el-button @click="goBack">返回</el-button> -->
          </div>
        </div>
      </div>
    </section>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, nextTick } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
// import ConferenceRegistration from "@/components/registration/ConferenceRegistration.vue";
import ImageUpload from "@/components/common/ImageUpload.vue";
import { getHotelInfo } from '@/api/agenda'
import {
  submitRegistration,
  updateRegistration,
  submitCompanion,
  updatePeerInfo,
  deletePeerInfo,
  passportInfo
} from "@/api/registration";
import { getDictCode } from "@/api/dictCode";
import { useUserStore } from "@/stores/user";
import { useRegistrationStore } from "@/stores/registration";
import dayjs from "dayjs";
import { QuestionFilled } from "@element-plus/icons-vue";
import { useLanguage } from '@/hooks/useLanguage'
// 返回 'chinese' 或 'english'
export default {
  name: "CombinedRegistration",
  components: {
    // ConferenceRegistration,
    ImageUpload,
    QuestionFilled,
  },
  setup() {
    const router = useRouter();
    const userStore = useUserStore();
    const registrationStore = useRegistrationStore();
    const isSubmitting = ref(false);
    const activeFormsMain = ref(["main"]);
    const activeForms = ref([]);
    const mainFormRef = ref(null);
    const companionFormRefs = ref([]);
    const isEdit = ref(false);
    const { currentLanguage, getLocalePath } = useLanguage()
    // console.log(currentLanguage.value)
    
    // <el-checkbox label="1">欢迎晚宴(1000元)</el-checkbox>
    //                       <el-checkbox label="2">国际CMC大会(2000元)</el-checkbox>
    //                       <el-checkbox label="3">ICMCI年会(3000元)</el-checkbox>
    //                       <el-checkbox label="4">颁奖(4000元)</el-checkbox>
    // // 报名人表单数据
    const isIndeterminate = ref(false);
    const checkAll = ref(false);
    const participationList = ref([
      {
        date:'21 Oct.',
        label:'Welcome Reception Event',
        price:'45',
        value:'1'
      },
      {
        date:'22-23 Oct.',
        label:'Conference In-person',
        price:'335',
        value:'2'
      },
      {
        date:'22 Oct.',
        label:'Constantinus Award & Gala Dinner',
        price:'130',
        value:'3'
      },
      {
        date:'23-24 Oct.',
        label:'Annual Meeting In-person',
        price:'245',
        value:'4'
      },
      {
        date:'23-24 Oct.',
        label:'Annual Meeting On-line',
        price:'100',
        value:'5'
      },
      {
        date:'BUNDLES',
        label:'Bundle (Reception, Gala Dinner, Conference, Annual Meeting)',
        price:'700',
        value:'6'
      },
      {
        date:'BUNDLES',
        label:'Bundle (Reception, Gala Dinner, Conference)',
        price:'500',
        value:'7'
      }
    ])
    const mainForm = ref({
      // 基本信息

      userId: userStore.userInfo.id,
      // firstName: "fengbao",
      // lastName: "wei",
      // sex: "sex_001",
      // country: "country_code_1",
      // organization: "organization_001",
      // position: "position_001",
      // telephone: "18612345678",
      // telAddr: "telAddr_001",
      // email: "<EMAIL>",
      // passportNum: "1234567890",
      // firstToCn: "first_to_cn_001",
      // invitePassportFlag: "invite_passport_flag_002",
      // orgIntroduce: "p er so n al Intr o  d  u c e_0 0 1",
      // personalIntroduce: "p er so n al Intr o  d  u c e_0 0 1",
      // passportImageList: ['/group1/M00/00/40/wKhxXmgkQvyAejGGAAfDs8JmB5I805.jpg'],



      firstName: "",
      lastName: "",
      sex: "",
      country: "",
      organization: "",
      position: "",
      telephone: "",
      telAddr: "",
      email: "",
      passportNum: "",
      firstToCn: "",
      invitePassportFlag: "",
      orgIntroduce: "",
      personalIntroduce: "",
      passportImageList: [],
      meetingType: '',
      // Visa
      visaInfo: {
        visaFirstName: "",
        visaLastName: "",
        visaSex: "",
        visaBirth: "",
        visaCountry: "",
        visaEnterDate: "",
        visaLeaveDate: "",
        visaUrgentContacter: "",
        visaEmail: "",
        visaOrganization: "",
        visaPosition: "",
        visaApplyPassportSite: "",
        visaVisitSite: "",
        visaOrgIntroduce: "",
        visaPersonalIntroduce: "",
        personType: "",
      },
      // 参会信息
      meetingType: [],
      forums: [],
    });

    // 同行人列表
    const companions = ref([]);

    // 国籍选项
    const countryOptions = ref([]);
    // 护照申请地点选项
    const passportSiteOptions = [
      { value: "passport_site_001", label: "北京" },
      { value: "passport_site_002", label: "上海" },
      { value: "passport_site_003", label: "广州" },
      // ... 其他地点选项
    ];

    // 访问地点选项
    const visitSiteOptions = [
      { value: "visit_site_001", label: "北京" },
      { value: "visit_site_002", label: "上海" },
      { value: "visit_site_003", label: "广州" },
      // ... 其他地点选项
    ];

  

    // 报名人表单验证规则
    const mainRules = {
      firstName: [
        { required: true, message: "Please enter your Surname/Family name", trigger: "blur" },
        { 
          validator: async (rule, value, callback) => {
              // 添加字数验证
         
            if (countWords(value) > 64) {
              callback(new Error("First name cannot exceed 64 words"));
              return;
            }

            if (!mainForm.value.lastName || !mainForm.value.passportNum) {
              callback();
              return;
            }
            if (!value || !mainForm.value.lastName || !mainForm.value.passportNum) {
              callback();
              return;
            }
          

            try {
              // 检查是否与任何同行人重复
              const isDuplicate = companions.value.some(companion => 
                companion.firstName === value && 
                companion.lastName === mainForm.value.lastName && 
                companion.passportNum === mainForm.value.passportNum
              );
              
              if (isDuplicate) {
                callback(new Error('This passport information is duplicated with a companion'));
                return;
              }

              const res = await passportInfo({
                firstName: value,
                lastName: mainForm.value.lastName,
                passportNum: mainForm.value.passportNum,
                signUpInfoId: mainForm.value.id
              });
              if (res.data.data) {
                callback(new Error('This user has already registered'));
              } else {
                callback();
                // 清除关联字段的验证状态
                mainFormRef.value?.clearValidate(['lastName', 'passportNum']);
              }
            } catch (error) {
              callback();
            }
          },
          trigger: 'blur'
        }
      ],
      lastName: [
        { required: true, message: "Please enter your First name/Given name", trigger: "blur" },
        { 
          validator: async (rule, value, callback) => {
            // 添加字数验证
           
            if (countWords(value) > 64) {
              callback(new Error("Last name cannot exceed 64 words"));
              return;
            }
            if (!value || !mainForm.value.firstName || !mainForm.value.passportNum) {
              callback();
              return;
            }
            

            if (!mainForm.value.firstName || !mainForm.value.passportNum) {
              callback();
              return;
            }

            try {
              // 检查是否与任何同行人重复
              const isDuplicate = companions.value.some(companion => 
                companion.firstName === mainForm.value.firstName && 
                companion.lastName === value && 
                companion.passportNum === mainForm.value.passportNum
              );
              
              if (isDuplicate) {
                callback(new Error('This passport information is duplicated with a companion'));
                return;
              }

              const res = await passportInfo({
                firstName: mainForm.value.firstName,
                lastName: value,
                passportNum: mainForm.value.passportNum,
                signUpInfoId: mainForm.value.id
              });
              if (res.data.data) {
                callback(new Error('This user has already registered'));
              } else {
                callback();
                // 清除关联字段的验证状态
                mainFormRef.value?.clearValidate(['firstName', 'passportNum']);
              }
            } catch (error) {
              callback();
            }
          },
          trigger: 'blur'
        }
      ],
      passportNum: [
        { required: true, message: "Please enter your passport No.", trigger: "blur" },
        { 
          validator: async (rule, value, callback) => {
            // 添加字数验证
            if (countWords(value) > 16) {
              callback(new Error("Passport No. cannot exceed 16 characters"));
              return;
            }
            if (!value || !mainForm.value.firstName || !mainForm.value.lastName) {
              callback();
              return;
            }
            

            if (!mainForm.value.firstName || !mainForm.value.lastName) {
              callback();
              return;
            }

            try {
              // 检查是否与任何同行人重复
              const isDuplicate = companions.value.some(companion => 
                companion.firstName === mainForm.value.firstName && 
                companion.lastName === mainForm.value.lastName && 
                companion.passportNum === value
              );
              
              if (isDuplicate) {
                callback(new Error('This passport information is duplicated with a companion'));
                return;
              }

              const res = await passportInfo({
                firstName: mainForm.value.firstName,
                lastName: mainForm.value.lastName,
                passportNum: value,
                signUpInfoId: mainForm.value.id
              });
              if (res.data.data) {
                callback(new Error('This user has already registered'));
              } else {
                callback();
                // 清除关联字段的验证状态
                mainFormRef.value?.clearValidate(['firstName', 'lastName']);
              }
            } catch (error) {
              callback();
            }
          },
          trigger: 'blur'
        }
      ],
      telAddr: [
        { required: true, message: "Please enter your mail address", trigger: "blur" },
        { 
          validator: (rule, value, callback) => {
           
            if (countWords(value) > 255) {
              callback(new Error("Address cannot exceed 255 words"));
            } else {
              callback();
            }
            if (!value) {
              callback();
              return;
            }
            
          },
          trigger: "blur"
        }
      ],
      sex: [{ required: true, message: "Please select gender", trigger: "change" }],
      country: [{ required: true, message: "Please select your Nationality/Region", trigger: "change" }],
      organization: [
        { required: true, message: "Please enter your organization/institution Name", trigger: "blur" },
        { 
          validator: (rule, value, callback) => {
            
            if (countWords(value) > 128) {
              callback(new Error("Organization name cannot exceed 128 words"));
            } else {
              callback();
            }
            if (!value) {
              callback();
              return;
            }
            
          },
          trigger: "blur"
        }
      ],
      position: [
        { required: true, message: "Please enter your position", trigger: "blur" },
        { 
          validator: (rule, value, callback) => {
           
            if (countWords(value) > 64) {
              callback(new Error("Position cannot exceed 64 words"));
            } else {
              callback();
            }
            if (!value) {
              callback();
              return;
            }
            
          },
          trigger: "blur"
        }
      ],
      telephone: [
        { required: true, message: "Please enter your Phone No.", trigger: "blur" },
        { 
          validator: (rule, value, callback) => {
            if (countWords(value) > 32) {
              callback(new Error("Phone No. cannot exceed 32 characters"));
            } else {
              callback();
            }
            if (!value) {
              callback();
              return;
            }
            
          },
          trigger: "blur"
        }
      ],
      email: [
        { required: true, message: "Please enter your Email", trigger: "blur" },
        { type: "email", message: "Please enter your a valid email address", trigger: "blur" },
      ],
      firstToCn: [
        { required: true, message: "Please choose whether it is your first time coming to China", trigger: "change" },
      ],
      invitePassportFlag: [
        {
          required: true,
          message: "Please choose whether a visa invitation letter is required",
          trigger: "change",
        },
      ],
      meetingType: [
        { required: true, message: "Please select Participate in activities", trigger: "change" },
      ],
      orgIntroduce: [
        { required: true, message: "Please enter your Organizational Profile", trigger: "blur" },
        {
          validator: (rule, value, callback) => {
            if (!value) {
              callback();
              return;
            }
            const wordCount = countWords(value);
            if (wordCount < 30) {
              callback(new Error("Organizational Profile requires at least 30 characters"));
            } else if (wordCount > 150) {
              callback(new Error("The Organizational Profile cannot exceed 150 characters at most"));
            } else {
              callback();
            }
          },
          trigger: "blur",
        },
      ],
      personalIntroduce: [
        { required: true, message: "Please enter your Personal Profile", trigger: "blur" },
        {
          validator: (rule, value, callback) => {
            if (!value) {
              callback();
              return;
            }
            const wordCount = countWords(value);
            if (wordCount < 30) {
              callback(new Error("Personal introduction requires at least 30 characters"));
            } else if (wordCount > 150) {
              callback(new Error("Personal introduction cannot exceed 150 characters at most"));
            } else {
              callback();
            }
          },
          trigger: "blur",
        },
      ],
      passportImageList: [
        { required: true, message: "Please upload passport photo", trigger: "change" },
        {
          validator: (rule, value, callback) => {
            if (!value) {
              callback(new Error("Please upload passport photo"));
            } else {
              callback();
            }
          },
          trigger: "change",
        },
      ],
      'visaInfo.visaBirth': [
        { required: true, message: "Please select your date of birth", trigger: "change" },
      ],
      'visaInfo.visaEnterDate': [
        { required: true, message: "Please select the date of entry", trigger: "change" },
      ],
      'visaInfo.visaLeaveDate': [
        { required: true, message: "Please select the departure date", trigger: "change" },
      ],
      'visaInfo.visaUrgentContacter': [
        { required: true, message: "Please enter your the emergency contact person", trigger: "blur" },
      ],
      'visaInfo.visaEmail': [
        { required: true, message: "Please enter your the email address for receiving the invitation letter", trigger: "blur" },
        { type: "email", message: "Please enter your a valid email address", trigger: "blur" },
      ],
      'visaInfo.visaApplyPassportSite': [
        { required: true, message: "Please enter your the location for passport application", trigger: "blur" },
      ],
      'visaInfo.visaVisitSite': [
        { required: true, message: "Please enter your the visit location", trigger: "blur" },
      ],
    };

    // 同行人表单验证规则
    const companionRules = {
      firstName: [
        { required: true, message: "Please enter your Surname/Family name", trigger: "blur" },
        { 
          validator: async (rule, value, callback) => {
            // 添加字数验证
            if (countWords(value) > 64) {
              callback(new Error("First name cannot exceed 64 words"));
              return;
            }

            const companion = companions.value.find(c => c.firstName === value);
            if (!value || !companion?.lastName || !companion?.passportNum) {
              callback();
              return;
            }
            try {
              // 检查是否与报名人重复
              if (mainForm.value.firstName === value && 
                  mainForm.value.lastName === companion.lastName && 
                  mainForm.value.passportNum === companion.passportNum) {
                  callback(new Error('This passport information is duplicated with the registrant'));
                  return;
              }

              // 检查是否与其他同行人重复
              const isDuplicate = companions.value.some(c => 
                c !== companion && 
                c.firstName === value && 
                c.lastName === companion.lastName && 
                c.passportNum === companion.passportNum
              );
              
              if (isDuplicate) {
                callback(new Error('This passport information is duplicated with another companion'));
                return;
              }

              const res = await passportInfo({
                firstName: value,
                lastName: companion.lastName,
                passportNum: companion.passportNum,
                peerInfoId: companion.id
              });
              if (res.data.data) {
                callback(new Error('This user has already registered'));
              } else {
                callback();
                const index = companions.value.findIndex(c => c.firstName === value);
                if (index !== -1 && companionFormRefs.value[index]) {
                  companionFormRefs.value[index].clearValidate(['lastName', 'passportNum']);
                }
              }
            } catch (error) {
              callback();
            }
          },
          trigger: 'blur'
        }
      ],
      lastName: [
        { required: true, message: "Please enter your First name/Given name", trigger: "blur" },
        { 
          validator: async (rule, value, callback) => {
            // 添加字数验证
            if (countWords(value) > 64) {
              callback(new Error("Last name cannot exceed 64 words"));
              return;
            }

            const companion = companions.value.find(c => c.lastName === value);
            if (!value || !companion?.firstName || !companion?.passportNum) {
              callback();
              return;
            }
            try {
              // 检查是否与报名人重复
              if (mainForm.value.firstName === companion.firstName && 
                  mainForm.value.lastName === value && 
                  mainForm.value.passportNum === companion.passportNum) {
                callback(new Error('This passport information is duplicated with the registrant'));
                return;
              }

              // 检查是否与其他同行人重复
              const isDuplicate = companions.value.some(c => 
                c !== companion && 
                c.firstName === companion.firstName && 
                c.lastName === value && 
                c.passportNum === companion.passportNum
              );
              
              if (isDuplicate) {
                callback(new Error('This passport information is duplicated with another companion'));
                return;
              }

              const res = await passportInfo({
                firstName: companion.firstName,
                lastName: value,
                passportNum: companion.passportNum,
                peerInfoId: companion.id
              });
              if (res.data.data) {
                callback(new Error('This user has already registered'));
              } else {
                callback();
                // 获取当前同行人的索引
                const index = companions.value.findIndex(c => c.lastName === value);
                if (index !== -1 && companionFormRefs.value[index]) {
                  companionFormRefs.value[index].clearValidate(['firstName', 'passportNum']);
                }
              }
            } catch (error) {
              callback();
            }
          },
          trigger: 'blur'
        }
      ],
      passportNum: [
        { required: true, message: "Please enter your passport No.", trigger: "blur" },
        { 
          validator: async (rule, value, callback) => {
            // 添加字数验证
            if (countWords(value) > 16) {
              callback(new Error("passport No. cannot exceed 16 characters"));
              return;
            }

            const companion = companions.value.find(c => c.passportNum === value);
            if (!value || !companion?.firstName || !companion?.lastName) {
              callback();
              return;
            }
            try {
              // 检查是否与报名人重复
              if (mainForm.value.firstName === companion.firstName && 
                  mainForm.value.lastName === companion.lastName && 
                  mainForm.value.passportNum === value) {
                callback(new Error('This passport information is duplicated with the registrant'));
                return;
              }

              // 检查是否与其他同行人重复
              const isDuplicate = companions.value.some(c => 
                c !== companion && 
                c.firstName === companion.firstName && 
                c.lastName === companion.lastName && 
                c.passportNum === value
              );
              
              if (isDuplicate) {
                callback(new Error('This passport information is duplicated with another companion'));
                return;
              }

              const res = await passportInfo({
                firstName: companion.firstName,
                lastName: companion.lastName,
                passportNum: value,
                peerInfoId: companion.id
              });
              if (res.data.data) {
                callback(new Error('This user has already registered'));
              } else {
                callback();
                // 获取当前同行人的索引
                const index = companions.value.findIndex(c => c.passportNum === value);
                if (index !== -1 && companionFormRefs.value[index]) {
                  companionFormRefs.value[index].clearValidate(['firstName', 'lastName']);
                }
              }
            } catch (error) {
              callback();
            }
          },
          trigger: 'blur'
        }
      ],
      sex: [{ required: true, message: "Please select gender", trigger: "change" }],
      country: [{ required: true, message: "Please select your Nationality/Region", trigger: "change" }],
      organization: [
        { required: true, message: "Please enter your organization/institution Name", trigger: "blur" },
        { 
          validator: (rule, value, callback) => {
            if (countWords(value) > 128) {
              callback(new Error("Organization name cannot exceed 128 words"));
            } else {
              callback();
            }
            if (!value) {
              callback();
              return;
            }
          },
          trigger: "blur"
        }
      ],
      position: [
        { required: true, message: "Please enter your position", trigger: "blur" },
        { 
          validator: (rule, value, callback) => {
            if (countWords(value) > 64) {
              callback(new Error("Position cannot exceed 64 words"));
            } else {
              callback();
            }
            if (!value) {
              callback();
              return;
            }
          },
          trigger: "blur"
        }
      ],
      telephone: [
        { required: true, message: "Please enter your Phone No.", trigger: "blur" },
        { 
          validator: (rule, value, callback) => {
            if (countWords(value) > 32) {
              callback(new Error("Phone No. cannot exceed 32 characters"));
            } else {
              callback();
            }
            if (!value) {
              callback();
              return;
            }
          },
          trigger: "blur"
        }
      ],
      telAddr: [
        { required: true, message: "Please enter your mail address", trigger: "blur" },
        { 
          validator: (rule, value, callback) => {
            if (countWords(value) > 255) {
              callback(new Error("Address cannot exceed 255 words"));
            } else {
              callback();
            }
            if (!value) {
              callback();
              return;
            }
          },
          trigger: "blur"
        }
      ],
      email: [
        { required: true, message: "Please enter your Email", trigger: "blur" },
        { type: "email", message: "Please enter your a valid email address", trigger: "blur" },
      ],
      firstToCn: [
        { required: true, message: "Please choose whether it is your first time coming to China", trigger: "change" },
      ],
      invitePassportFlag: [
        {
          required: true,
          message: "Please choose whether a visa invitation letter is required",
          trigger: "change",
        },
      ],
      orgIntroduce: [
        { required: true, message: "Please enter your Organizational Profile", trigger: "blur" },
        {
          validator: (rule, value, callback) => {
            if (!value) {
              callback();
              return;
            }
            const wordCount = countWords(value);
            if (wordCount < 30) {
              callback(new Error("Organizational Profile requires at least 30 characters"));
            } else if (wordCount > 150) {
              callback(new Error("The Organizational Profile cannot exceed 150 characters at most"));
            } else {
              callback();
            }
          },
          trigger: "blur",
        },
      ],
      personalIntroduce: [
        { required: true, message: "Please enter your Personal Profile", trigger: "blur" },
        {
          validator: (rule, value, callback) => {
            if (!value) {
              callback();
              return;
            }
            const wordCount = countWords(value);
            if (wordCount < 30) {
              callback(new Error("Personal introduction requires at least 30 characters"));
            } else if (wordCount > 150) {
              callback(new Error("Personal introduction cannot exceed 150 characters at most"));
            } else {
              callback();
            }
          },
          trigger: "blur",
        },
      ],
      passportImageList: [
        { required: true, message: "Please upload passport photo", trigger: "change" },
        {
          validator: (rule, value, callback) => {
            if (!value) {
              callback(new Error("Please upload passport photo"));
            } else {
              callback();
            }
          },
          trigger: "change",
        },
      ],
      meetingType: [
        { required: true, message: "Please select Participate in activities", trigger: "change" },
        { 
          validator: (rule, value, callback) => {
            if (!value || value.length === 0) {
              callback(new Error('Please select at least one type of attendance'));
            } else {
              callback();
            }
          },
          trigger: 'change'
        }
      ],
      'visaInfo.visaBirth': [
        { required: true, message: "Please select your date of birth", trigger: "change" },
      ],
      'visaInfo.visaEnterDate': [
        { required: true, message: "Please select the date of entry", trigger: "change" },
      ],
      'visaInfo.visaLeaveDate': [
        { required: true, message: "Please select the departure date", trigger: "change" },
      ],
      'visaInfo.visaUrgentContacter': [
        { required: true, message: "Please enter your the emergency contact person", trigger: "blur" },
      ],
      'visaInfo.visaEmail': [
        { required: true, message: "Please enter your the email address for receiving the invitation letter", trigger: "blur" },
        { type: "email", message: "Please enter your a valid email address", trigger: "blur" },
      ],
      'visaInfo.visaApplyPassportSite': [
        { required: true, message: "Please enter your the location for passport application", trigger: "blur" },
      ],
      'visaInfo.visaVisitSite': [
        { required: true, message: "Please enter your the visit location", trigger: "blur" },
      ],
    };

   

    // 加载数据
    const loadData = async () => {
      try {
        const savedData = sessionStorage.getItem("registrationFormData");
        if (savedData) {
          const data = JSON.parse(savedData);
            console.log(1,data)
          // 处理报名人数据

          const types = data.meetingType.split(',')
          mainForm.value = {
            ...data,
            meetingType: types,
            visaInfo: {
              ...mainForm.value.visaInfo,
              ...(data.visaInfo || {}),
            },
          };
          
          // 设置报名人的全选状态
          checkAll.value = types.length === 4;
          isIndeterminate.value = types.length > 0 && types.length < 4;

          isEdit.value = true;

          // 加载同行人数据
          const companionData = data?.peerInfoList || [];

          if (companionData.length > 0) {
            companions.value = companionData.map((item) => {
              const types = item.meetingType.split(',')
              return {
                ...item,
                visaInfo: item.meetingVisaInfo || {},
                meetingType: types,
                checkAll: types.length === 4,
                isIndeterminate: types.length > 0 && types.length < 4
              }
            });
          } else {
            companions.value = [];
          }
          sessionStorage.removeItem("registrationFormData");
        }
        // 重置表单验证状态
        nextTick(() => {
          if (mainFormRef.value) {
            mainFormRef.value.clearValidate();
          }
          companionFormRefs.value.forEach((formRef) => {
            if (formRef) {
              formRef.clearValidate();
            }
          });
        });
      } catch (error) {
        console.log(error);
      }
    };
    const handleCheckAllChange = (val) => {
      mainForm.value.meetingType = val ? participationList.value.map(item => item.value) : [];
      isIndeterminate.value = false;
    };

    const handleCheckedChange = (value) => {
      // 选中全包，只能选它
      if (value.includes('6')) {
        mainForm.value.meetingType = ['6'];
        return;
      }
      // 选中第二个优惠价，只能选 4、5、7
      if (value.includes('7')) {
        mainForm.value.meetingType = value.filter(v => ['4', '5', '7'].includes(v));
        return;
      }
      // 其它情况，正常处理
      const checkedCount = value.length;
      checkAll.value = checkedCount === participationList.value.length;
      isIndeterminate.value = checkedCount > 0 && checkedCount < participationList.value.length;
    };
    const hotelMoney  = ref()
    onMounted(() => {
      loadData();
      if(userStore.userInfo.account){
        getHotelInfo(userStore.userInfo.account).then(res=>{
          console.log(res)
          hotelMoney.value = res.data.data;
        })
      }
      getDictCode("country_code").then((res) => {
        countryOptions.value = res.data.data.map((item) => ({
          value: item.dictionaryCode,
          label: item.dictionaryValue,
        }));
      });
    });
    const  disableOtherDates =(date) =>{
      // 获取日期的月份（注意：JavaScript的月份是从0开始的，10表示11月，所以9才是10月）
      const month = date.getMonth();
      // 获取日期的日
      const day = date.getDate();
      
      // 只允许选择10月(9)的19日至25日
      return month !== 9 || day < 19 || day > 25;
    }
    // 添加同行人
    const addCompanion = () => {
      if (companions.value.length >= 5) {
        ElMessage.warning("最多只能添加5位同行人");
        return;
      }

      const newCompanion = {
        // firstName: "fengbao",
        // lastName: "wei",
        // sex: "sex_001",
        // country: "country_code_1",
        // organization: "organization_001",
        // position: "position_001",
        // telephone: "18612345678",
        // telAddr: "telAddr_001",
        // email: "<EMAIL>",
        // passportNum: "1234567890",
        // firstToCn: "first_to_cn_001",
        // invitePassportFlag: "invite_passport_flag_002",
        // orgIntroduce: "p er so n al Intr o  d  u c e_0 0 1",
        // personalIntroduce: "p er so n al Intr o  d  u c e_0 0 1",
        // passportImageList: ['/group1/M00/00/40/wKhxXmgkQvyAejGGAAfDs8JmB5I805.jpg '],



        firstName: "",
        lastName: "",
        sex: "",
        country: "",
        organization: "",
        position: "",
        telephone: "",
        telAddr: "",
        email: "",
        passportNum: "",
        firstToCn: "",
        invitePassportFlag: "",
        orgIntroduce: "",
        personalIntroduce: "",
        passportImageList: [],

        meetingType: [],
        checkAll: false,
        isIndeterminate: false,
        visaInfo: {
          visaFirstName: "",
          visaLastName: "",
          visaSex: "",
          visaBirth: "",
          visaCountry: "",
          visaEnterDate: "",
          visaLeaveDate: "",
          visaUrgentContacter: "",
          visaEmail: "",
          visaOrganization: "",
          visaPosition: "",
          visaApplyPassportSite: "",
          visaVisitSite: "",
          visaOrgIntroduce: "",
          visaPersonalIntroduce: "",
          personType: "person_type_002",
        },
      };

      companions.value.push(newCompanion);
      // 自动展开新添加的同行人折叠面板，同时保持其他面板的状态
      const newCompanionName = "companion-" + (companions.value.length - 1);
      if (!activeForms.value.includes(newCompanionName)) {
        activeForms.value.push(newCompanionName);
      }
    };

    // 删除同行人
    const removeCompanion = (item, index) => {
      // 如果有id，调用删除接口
      if (item.id) {
        deletePeerInfo(item.id)
          .then(() => {
            doRemove();
          })
          .catch(() => { });
      } else {
        doRemove();
      }

      function doRemove() {
        ElMessageBox.confirm("Are you sure you want to delete this companion ?", "prompt", {
          confirmButtonText: "sure",
          cancelButtonText: "cancel",
          type: "warning",
        })
          .then(() => {
            companions.value.splice(index, 1);

            // 更新折叠面板状态：移除被删除的面板，并重新索引其他面板
            const removedPanelName = "companion-" + index;
            activeForms.value = activeForms.value
              .filter(name => name !== removedPanelName) // 移除被删除的面板
              .map(name => {
                // 重新索引：如果面板索引大于被删除的索引，则索引减1
                const match = name.match(/companion-(\d+)/);
                if (match) {
                  const panelIndex = parseInt(match[1]);
                  if (panelIndex > index) {
                    return `companion-${panelIndex - 1}`;
                  }
                }
                return name;
              });

            ElMessage.success("Delete successfully");
          })
          .catch(() => { });
      }
    };

    // 格式化日期
    const formatDate = (date) => {
      if (!date) return null;
      return dayjs(date).format("YYYY-MM-DD");
    };

    // 处理折叠面板变化
    const handleCollapseChange = (activeNames) => {
      // 处理折叠面板的展开/收起
      activeForms.value = activeNames;
      // console.log(activeForms.value)
    };
    // 字数统计函数
    const countWords = (text) => {
      if (!text) return 0;
      // return text.trim().length
      return text.trim().split(/\s+/).length;
    };

    // 处理同行人护照照片上传成功
    const handleCompanionVisaImageSuccess = (response, index) => {
      if (response.code === 1000) {
        companions.value[index].passportImageList = response.data;
        nextTick(() => {
          if (companionFormRefs.value[index]) {
            companionFormRefs.value[index].clearValidate(["passportImageList"]);
            companionFormRefs.value[index].validateField("passportImageList");
          }
        });
      } else {
        ElMessage.error("Passport photo upload failed");
      }
    };

    // 计算金额
    const calculateAmount = () => {
      let amount = 0;
      // 计算报名人的参会费用
   
        mainForm.value.meetingType.forEach(type => {
        const selectedItem = participationList.value.find(item => item.value === type);
        if (selectedItem) {
          amount += Number(selectedItem.price);
        }
      });
      
      

      // 计算所有同行人的参会费用
      companions.value.forEach(companion => {
        // 如果是全选，费用固定为8000
   
          companion.meetingType.forEach(type => {
            const selectedItem = participationList.value.find(item => item.value === type);
            if (selectedItem) {
              amount += Number(selectedItem.price);
            }
          });
        
      });

      return amount+'.00';
    };

    // 同行人全选处理
    const handleCompanionCheckAllChange = (val, index) => {
      companions.value[index].meetingType = val ? participationList.value.map(item => item.value) : [];
      companions.value[index].isIndeterminate = false;
    };

    // 同行人复选框组变化处理
    const handleCompanionCheckedChange = (value, index) => {
      // 选中全包，只能选它
      if (value.includes('6')) {
        companions.value[index].meetingType = ['6'];
        return;
      }
      // 选中第二个优惠价，只能选 4、5、7
      if (value.includes('7')) {
        companions.value[index].meetingType = value.filter(v => ['4', '5', '7'].includes(v));
        return;
      }
      // 其它情况，正常处理
      const checkedCount = value.length;
      companions.value[index].checkAll = checkedCount === participationList.value.length;
      companions.value[index].isIndeterminate = checkedCount > 0 && checkedCount < participationList.value.length;
    };

    // 同行人选项禁用逻辑
    const isCompanionOptionDisabled = (value, index) => {
      const selected = companions.value[index].meetingType;
      // 选中全包
      if (selected.includes('6')) {
        return value !== '6';
      }
      // 选中第二个优惠价
      if (selected.includes('7')) {
        // 只允许 4、5、7 选，其他禁用
        return !['4', '5', '7'].includes(value);
      }
      return false;
    };

    // 提交所有信息
    const submitAll = async (type) => {
      try {
      
        isSubmitting.value = true;

        // 验证报名人表单
        await mainFormRef.value.validate();

        // 验证同行人表单
        if (companions.value.length > 0) {
          for (let i = 0; i < companions.value.length; i++) {
            await companionFormRefs.value[i].validate();
          }
        }
 
         const enterDate = new Date(mainForm.value.visaInfo.visaEnterDate);
          const leaveDate = new Date(mainForm.value.visaInfo.visaLeaveDate);
          console.log(enterDate,leaveDate)
            if (leaveDate <= enterDate) {
            ElMessage.error('The departure date must be later than the entry date');
            return false;
          }
        // 计算总金额
        let totalMoney = calculateAmount();

        // 准备报名人数据
        const mainFormData = {
          ...mainForm.value,
          status: "meeting_status_001",
          orderAmtPayable: totalMoney,
          orderPaidAmt: 0,
          passportImageList: mainForm.value.passportImageList,
          passportImage: "",
          orderUnit:'2', //orderUnit  1 人民币  2 欧元  3美元
          meetingType: mainForm.value.meetingType.sort().join(","),
          visaInfo:
            mainForm.value.invitePassportFlag === "invite_passport_flag_001"
              ? {
                ...mainForm.value.visaInfo,
                visaBirth: formatDate(mainForm.value.visaInfo.visaBirth),
                visaEnterDate: formatDate(
                  mainForm.value.visaInfo.visaEnterDate
                ),
              }
              : null,
        };

        // 提交报名人信息
        let response;
        if (isEdit.value) {
          mainFormData.id = mainForm.value.id;
          response = await updateRegistration(mainFormData);
        } else {
          response = await submitRegistration(mainFormData);
        }

        if (response.code !== 1000) {
          ElMessage.error(response.message || "提交失败");
          return;
        }

        // 新增
        if (!isEdit.value) {
          registrationStore.setRegistrationId(response.data.data.id);
        }

        // 如果有同行人，提交同行人信息
        if (companions.value.length > 0) {
          const companionsData = companions.value.map((companion) => {
            if (companion.id) {
              delete companion.id;
            }
            if (companion.meetingVisaInfo) {
              delete companion.meetingVisaInfo.id;
            }
            if (companion.visaInfo) {
              delete companion.visaInfo.id;
            }
            return {
              ...companion,
              signUpInfoId: !isEdit.value
                ? response.data.data.id
                : mainFormData.id,
              meetingType: companion.meetingType.sort().join(','),
              visaInfo:
                companion.invitePassportFlag === "invite_passport_flag_001"
                  ? {
                    ...companion.visaInfo,
                    visaBirth: formatDate(companion.visaInfo.visaBirth),
                    visaEnterDate: formatDate(
                      companion.visaInfo.visaEnterDate
                    ),
                    visaLeaveDate: formatDate(
                      companion.visaInfo.visaLeaveDate
                    ),
                  }
                  : null,
            };
          });
          //   updatePeerInfo
          const companionResponse = await submitCompanion(companionsData);
          if (companionResponse.code !== 1000) {
            ElMessage.error("同行人信息提交失败");
            return;
          }
        }
        if (type === "save") {
          router.push({
            path: getLocalePath("dashboard"),
            query: { tab: "registrations" },
          });
        } else {
          router.push(getLocalePath("payment"));
        }
        ElMessage.success("提交成功");
      } catch (error) {
        console.error("提交失败", error);
        // 获取第一个错误表单项
        let firstErrorField = null;
        let errorFormType = '';
        let errorFormIndex = -1;
        
        // 检查主表单
        if (mainFormRef.value?.$el) {
          firstErrorField = mainFormRef.value.$el.querySelector('.is-error');
          if (firstErrorField) {
            errorFormType = '报名人';
          }
        }
        
        // 如果主表单没有错误，检查同行人表单
        if (!firstErrorField && companions.value.length > 0) {
          for (let i = 0; i < companionFormRefs.value.length; i++) {
            const formRef = companionFormRefs.value[i];
            if (formRef?.$el) {
              firstErrorField = formRef.$el.querySelector('.is-error');
              if (firstErrorField) {
                errorFormType = '同行人';
                errorFormIndex = i + 1;
                break;
              }
            }
          }
        }
        
        // 如果找到错误表单项，滚动到该位置
        if (firstErrorField) {
          firstErrorField.scrollIntoView({ 
            behavior: 'smooth',
            block: 'center'
          });
          
          // 展开包含错误表单项的折叠面板
          if (errorFormType === '报名人') {
            activeFormsMain.value = ['main'];
          } else if (errorFormType === '同行人') {
            const errorPanelName = `companion-${errorFormIndex - 1}`;
            if (!activeForms.value.includes(errorPanelName)) {
              activeForms.value.push(errorPanelName);
            }
          }
          let tip = ''
          if(errorFormIndex == 1){
            tip = 'Please complete the information of the second colleague'
          }else if(errorFormIndex == 2){
            tip = 'Please complete the information of the third colleague'
          }else if(errorFormIndex == 3){
            tip = 'Please complete the information of the fourth colleague'
          }else if(errorFormIndex == 4){
            tip = 'Please complete the information of the fifth colleague'
          }else if(errorFormIndex == 5){
            tip = 'Please complete the information of the sixth colleague'
          }

          // 显示具体的错误提示
          const errorMessage = errorFormType === '报名人' 
            ? 'Please complete the applicant information'
            : tip;
          ElMessage.error(errorMessage);
        } else {
          // 保留原有的通用错误提示
          ElMessage.error("Please check if the form is filled out correctly");
        }
      } finally {
        isSubmitting.value = false;
      }
    };

    const goBack = () => {
      router.back();
    };

    const isOptionDisabled = (value) => {
      const selected = mainForm.value.meetingType;
      // 选中全包
      if (selected.includes('6')) {
        return value !== '6';
      }
      // 选中第二个优惠价
      if (selected.includes('7')) {
        // 只允许 4、5、7 选，其他禁用
        return !['4', '5', '7'].includes(value);
      }
      return false;
    };

    return {
      mainForm,
      handleCheckAllChange,
      handleCheckedChange,
      handleCompanionCheckAllChange,
      handleCompanionCheckedChange,
      mainFormRef,
      calculateAmount,
      companions,
      companionFormRefs,
      activeForms,
      activeFormsMain,
      isSubmitting,
      addCompanion,
      removeCompanion,
      submitAll,
      goBack,
      formatDate,
      countryOptions,
      passportSiteOptions,
      visitSiteOptions,
      disableOtherDates,
      mainRules,
      companionRules,
    
      handleCollapseChange,
      countWords,
      participationList,
      handleCompanionVisaImageSuccess,
      isIndeterminate,
      checkAll,
      isOptionDisabled,
      isCompanionOptionDisabled
    };
  },
};
</script>

<style scoped>

.meeting-type-checkbox.is-disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.meeting-type-checkbox.is-disabled .meeting-type-content {
  color: #999;
}
.conference-overview-container{
  background: url('@/assets/images/home-bg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  min-height: 100vh;
  
}
.total-amount {
  margin-top: 20px;
  text-align: right;
  padding: 15px;
  background-color: var(--bg-light);
  border-radius: var(--radius-sm);
}

.amount-label {
  font-size: 16px;
  color: var(--text-light);
}

.amount-value {
  font-size: 20px;
  font-weight: 700;
  color: var(--primary-color);
  margin-left: 10px;
}
.combined-registration-page {
  /* padding-bottom: 80px; */
}

.page-header {
  background: linear-gradient(rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.3)), url('@/assets/images/dahui.png');
  background-size: cover;
  background-position: center;

  height: 200px;
  text-align: center;
  margin-top: 80px;
}

.page-header h1 {
  font-size: 42px;
  margin-bottom: 15px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  animation: fadeInDown 1s ease-out;
}

.page-header p {
  font-size: 18px;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  animation: fadeInUp 1s ease-out;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.registration-form-section {
  padding: 80px 0;
}

.form-container {
  max-width: 860px;
  margin: 0 auto;
  background-color: var(--bg-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: 40px;
}

.form-container h2 {
  text-align: center;
  margin-bottom: 10px;
}

.form-desc {
  text-align: center;
  color: var(--text-light);
  margin-bottom: 30px;
}

.required {
  color: var(--error-color);
}

.form-section {
  margin-bottom: 40px;
  padding-bottom: 30px;
  border-bottom: 1px solid var(--border-color);
}

.form-section:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.form-section h3 {
  font-size: 20px;
  margin-bottom: 20px;
  color: var(--primary-color);
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 91%;
  /* padding-right: 20px; */
  font-size: 16px;
  font-weight: 500;
}

.form-header :deep(.el-button) {
  flex-shrink: 0;
  margin-left: 10px;
}

.form-header > span {

  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 10px;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 40px;
}

:deep(.el-collapse-item__header) {
  font-size: 16px;
  font-weight: 500;
}

:deep(.el-collapse-item__content) {
  padding: 20px;
}

:deep(.el-form-item) {
  margin-bottom: 0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--text-color);
}

:deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--border-color) inset;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px var(--primary-color) inset;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px var(--primary-color) inset;
}

:deep(.el-radio-group) {
  display: flex;
  gap: 20px;
}

:deep(.el-radio) {
  margin-right: 0;
}

:deep(.el-textarea__inner) {
  min-height: 100px;
  resize: vertical;
}

:deep(.el-date-editor) {
  width: 100%;
}

.form-row .form-group {
  margin-bottom: 20px;
}

@media (max-width: 768px) {
  .form-container {
    padding: 30px 20px;
  }

  .form-row {
    flex-direction: column;
    gap: 0;
  }

  .form-actions {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }
}

.meeting-type-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
}

.meeting-type-checkbox {
  width: 100%;
  margin-right: 0 !important;
  padding: 16px 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.meeting-type-checkbox:hover {
  border-color: var(--primary-color);
  background-color: rgba(25, 118, 210, 0.04);
}

.meeting-type-checkbox.is-checked {
  border-color: var(--primary-color);
  background-color: rgba(25, 118, 210, 0.08);
}

.meeting-type-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.meeting-type-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.meeting-date {
  color: var(--primary-color);
  font-weight: 500;
  font-size: 16px;
  width: 90px;
}

.meeting-label {
  font-size: 16px;
  color: #333;
}

.meeting-price {
  font-size: 18px;
  font-weight: 600;
  color: var(--primary-color);
}

:deep(.el-checkbox__input) {
  margin-right: 12px;
}
:deep(.el-checkbox__label) {
  width: 100%;
}

:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

:deep(.el-checkbox__inner) {
  width: 20px;
  height: 20px;
  border-radius: 4px;
}

:deep(.el-checkbox__inner::after) {
  height: 10px;
  width: 5px;
  left: 6px;
  top: 2px;
}
.meeting-date {
    font-size: 14px;
    min-width: 120px;
  }
@media (max-width: 768px) {
  .meeting-type-checkbox {
    padding: 12px 16px;
  }
  
  .meeting-date {
    font-size: 14px;
    min-width: 130px;
  }
  
  .meeting-label {
    font-size: 14px;
  }
  
  .meeting-price {
    font-size: 16px;
  }
}

</style>
