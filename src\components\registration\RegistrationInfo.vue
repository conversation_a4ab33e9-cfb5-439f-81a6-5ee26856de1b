<template>
  <div class="registration-info">
    <el-tabs v-model="activeTab" class="registration-tabs">
      <el-tab-pane label="报名人信息" name="main">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="姓名">{{ registrationInfo.name }}</el-descriptions-item>
          <el-descriptions-item label="邮箱">{{ registrationInfo.email }}</el-descriptions-item>
          <el-descriptions-item label="手机号码">{{ registrationInfo.phone }}</el-descriptions-item>
          <el-descriptions-item label="公司/组织">{{ registrationInfo.company }}</el-descriptions-item>
          <el-descriptions-item label="职位">{{ registrationInfo.position }}</el-descriptions-item>
          <el-descriptions-item label="参会类型">{{ getMeetingTypeName(registrationInfo.meetingType) }}</el-descriptions-item>
          <el-descriptions-item label="报名状态">{{ getStatusName(registrationInfo.status) }}</el-descriptions-item>
          <el-descriptions-item label="报名时间">{{ registrationInfo.createTime }}</el-descriptions-item>
        </el-descriptions>
      </el-tab-pane>
      
      <el-tab-pane label="同行人信息" name="companion">
        <el-collapse v-model="activeCompanions">
          <el-collapse-item 
            v-for="(companion, index) in companionInfo" 
            :key="index"
            :title="`同行人 ${index + 1}`"
          >
            <el-descriptions :column="2" border>
              <el-descriptions-item label="姓名">{{ companion.name }}</el-descriptions-item>
              <el-descriptions-item label="邮箱">{{ companion.email }}</el-descriptions-item>
              <el-descriptions-item label="手机号码">{{ companion.phone }}</el-descriptions-item>
              <el-descriptions-item label="公司/组织">{{ companion.company }}</el-descriptions-item>
              <el-descriptions-item label="职位">{{ companion.position }}</el-descriptions-item>
              <el-descriptions-item label="报名状态">{{ getStatusName(companion.status) }}</el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
      </el-tab-pane>
      
      <el-tab-pane label="支付信息" name="payment">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单编号">{{ paymentInfo.orderNo }}</el-descriptions-item>
          <el-descriptions-item label="订单金额">{{ paymentInfo.orderAmtPayable }}</el-descriptions-item>
          <el-descriptions-item label="已付金额">{{ paymentInfo.orderPaidAmt }}</el-descriptions-item>
          <el-descriptions-item label="支付状态">{{ getPaymentStatusName(paymentInfo.paymentStatus) }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ paymentInfo.createTime }}</el-descriptions-item>
          <el-descriptions-item label="支付时间">{{ paymentInfo.paymentTime }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="payment-actions" v-if="paymentInfo.paymentStatus === 'payment_status_001'">
          <el-button type="primary" @click="goToPayment">继续支付</el-button>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getRegistrationInfo, getCompanionInfo } from '@/api/registration'

export default {
  name: 'RegistrationInfo',
  setup() {
    const router = useRouter()
    const activeTab = ref('main')
    const activeCompanions = ref([])

    const registrationInfo = ref({
      name: '',
      email: '',
      phone: '',
      company: '',
      position: '',
      meetingType: '',
      status: '',
      createTime: ''
    })

    const companionInfo = ref([])

    const paymentInfo = ref({
      orderNo: '',
      orderAmtPayable: 0,
      orderPaidAmt: 0,
      paymentStatus: '',
      createTime: '',
      paymentTime: ''
    })

    const getMeetingTypeName = (type) => {
      const types = {
        'full': '全程参会',
        'single': '单日参会',
        'student': '学生票'
      }
      return types[type] || type
    }

    const getStatusName = (status) => {
      const statuses = {
        'meeting_status_001': '已报名，未缴费',
        'meeting_status_002': '已报名，已缴费',
        'meeting_status_003': '已取消'
      }
      return statuses[status] || status
    }

    const getPaymentStatusName = (status) => {
      const statuses = {
        'payment_status_001': '未支付',
        'payment_status_002': '已支付',
        'payment_status_003': '已退款'
      }
      return statuses[status] || status
    }

    const goToPayment = () => {
      router.push('/payment')
    }

    const loadRegistrationInfo = async (userId) => {
      try {
        const response = await getRegistrationInfo(userId)
        if (response.code === 1000) {
          const data = response.data.data[0]
          registrationInfo.value = {
            ...data,
            name: `${data.signUpLastName}${data.signUpFirstName}`,
            meetingType: data.meetingType,
            status: data.status,
            createTime: data.createTime
          }
          
          // 加载同行人信息
          const companionResponse = await getCompanionInfo(data.id)
          if (companionResponse.code === 1000) {
            companionInfo.value = companionResponse.data.map(item => ({
              ...item,
              name: `${item.signUpLastName}${item.signUpFirstName}`
            }))
          }
          
          // 加载支付信息
          paymentInfo.value = {
            orderNo: data.orderNo,
            orderAmtPayable: data.orderAmtPayable,
            orderPaidAmt: data.orderPaidAmt,
            paymentStatus: data.paymentStatus,
            createTime: data.createTime,
            paymentTime: data.paymentTime
          }
        }
      } catch (error) {
        console.error('加载报名信息失败', error)
      }
    }

    onMounted(() => {
      // 从父组件传入 userId
      const userId = localStorage.getItem('userId')
      if (userId) {
        loadRegistrationInfo(userId)
      }
    })

    return {
      activeTab,
      activeCompanions,
      registrationInfo,
      companionInfo,
      paymentInfo,
      getMeetingTypeName,
      getStatusName,
      getPaymentStatusName,
      goToPayment
    }
  }
}
</script>

<style scoped>
.registration-info {
  margin-top: 20px;
}

.payment-actions {
  margin-top: 20px;
  text-align: center;
}
</style> 