.conference-overview-container{
    background: url('@/assets/images/home-bg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    min-height: 100vh;
    
  }
.page-header {
  background: linear-gradient(rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.3)), url('@/assets/images/dahui.png');
  background-size: cover;
  background-position: center;

  height: 200px;
  text-align: center;
  margin-top: 80px;
  }
  
  .page-header h1 {
    font-size: 36px;
    margin-bottom: 10px;
  }
  
  .payment-section {
    padding: 80px 0 0;
  }
  
  .payment-container {
    max-width: 800px;
    margin: 0 auto;
    background-color: var(--bg-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: 40px;
  }
  
  .order-summary {
    margin-bottom: 40px;
    padding-bottom: 30px;
    border-bottom: 1px solid var(--border-color);
  }
  
  .payment-container h2 {
    margin-bottom: 20px;
    color: var(--primary-color);
  }
  
  .order-details {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
  
  .order-item {
    padding: 15px;
    background-color: var(--bg-light);
    border-radius: var(--radius-md);
  }
  
  .item-label {
    font-size: 14px;
    color: var(--text-light);
    margin-bottom: 5px;
  }
  
  .item-value {
    font-size: 16px;
    font-weight: 500;
  }
  
  .item-value.amount {
    font-size: 20px;
    color: var(--primary-color);
    font-weight: 700;
  }
  
  .payment-methods {
    margin: 20px 0;
    color: #707070;
  }
  
  .payment-options {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-bottom: 30px;
  }
  
  .payment-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .payment-option.active {
    border-color: var(--primary-color);
    background-color: rgba(30, 136, 229, 0.05);
  }
  
  .option-icon {
    font-size: 30px;
    margin-bottom: 10px;
    color: var(--primary-color);
  }
  
  .option-name {
    font-weight: 500;
  }
  
  .payment-details {
    background-color: var(--bg-light);
    border-radius: var(--radius-md);
    padding: 30px;
    min-height: 250px;
  }
  
  .qr-code-container {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .qr-code {
    width: 200px;
    height: 200px;
    margin-bottom: 20px;
  }
  
  .qr-code img {
    width: 100%;
    height: 100%;
  }
  
  .qr-code-tip {
    text-align: center;
    color: var(--text-light);
  }
  
  .bank-info h3 {
    margin-bottom: 20px;
    font-size: 18px;
  }
  
  .bank-details {
    margin-bottom: 20px;
  }
  
  .bank-item {
    display: flex;
    margin-bottom: 15px;
  }
  
  .bank-label {
    width: 100px;
    font-weight: 500;
  }
  
  .bank-value {
    flex: 1;
  }
  
  .bank-note {
    color: var(--text-light);
    font-style: italic;
  }
  
  .payment-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
  }
  
  .payment-help-section {
    padding: 80px 0;
    background-color: var(--bg-light);
  }
  
  .payment-help {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 50px;
  }
  
  .help-item {
    background-color: var(--bg-color);
    border-radius: var(--radius-md);
    padding: 30px;
    box-shadow: var(--shadow-md);
  }
  
  .help-item h3 {
    font-size: 18px;
    margin-bottom: 15px;
    color: var(--primary-color);
  }
  
  .help-item p {
    margin-bottom: 10px;
    color: var(--text-light);
  }
  
  @media (max-width: 768px) {
    .payment-container {
      padding: 30px 20px;
    }
    
    .order-details {
      grid-template-columns: 1fr;
    }
    
    .payment-options {
      grid-template-columns: 1fr;
    }
    
    .payment-actions {
      flex-direction: column;
    }
    
    .payment-help {
      grid-template-columns: 1fr;
    }
  }
  
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }