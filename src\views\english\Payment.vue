<template>
  <div class="payment-page">
    <!-- 页面头部 -->
    <section class="page-header">
      <!-- <div class="container">
        <h1>Payment information confirmation</h1>
      </div> -->
    </section>

    <!-- 支付内容 -->
    <div class="conference-overview-container">
      <section class="payment-section">
        <div class="container">
          <div class="payment-container">
            <div class="payment-summary">
              <h2>Applicant</h2>
              <div>
                <ul>
                  <li v-for="(item, index) in [meeringUseInfo?.data?.data?.[0]]" class="order-details">
                    <span>Full Name：{{ item?.firstName }} {{ item?.lastName }}</span>
                    <span>Organization/Institution：{{ item?.organization }}</span>
                  </li>
                </ul>
              </div>
              <h2 style="margin-top: 20px;" v-show = "meeringUseInfo?.data?.data?.[0].peerInfoList?.length > 0">Companions</h2>
              <div>
                <ul>
                  <li v-for="(item, index) in meeringUseInfo?.data?.data?.[0].peerInfoList" class="order-details">
                    <span>Full Name：{{ item?.firstName }} {{ item?.lastName }}</span>
                    <span>Organization/Institution：{{ item?.organization }}</span>
                  </li>
                </ul>
              </div>
              <el-divider></el-divider>
              <ul>
                <li class="order-details">
                  <div style="font-weight: 700;">
                    <span>Total Cost：</span>
                    <span>{{ orderUnit == '1' ? '￥' : 'EUR' }} {{ singleFee
                    }}</span>
                  </div>
                </li>
              </ul>


            </div>

            <div class="payment-methods">
              <p>
                * Please note that due to PayPal's processing fees (4.4% + $0.30 per transaction), your refund will not
                be for the full amount but rather the net amount after deducting these fees. Thank you for your
                understanding. <br/>For refunds, please send an email to: <EMAIL>.
              </p>
            </div>

            <div class="payment-actions">
              <button class="btn btn-primary" @click="processPayment" :disabled="isProcessing">
                {{ isProcessing ? 'pending...' : 'Confirm Payment' }}
              </button>
              <button class="btn btn-outline" @click="goBack">Return to Modify</button>
            </div>
          </div>
        </div>
      </section>

      <!-- 支付帮助 -->
      <section style="width: 800px;margin:20px auto 0;color:red">

      </section>

      <!-- 转账信息弹窗 -->
      <el-dialog align-center v-model="showTransferForm" :title="tips ?'prompt':'Confirm Payment'" width="600px" :close-on-click-modal="false"
        :close-on-press-escape="false" :show-close="false">
        <div class="payment-methods" v-if="tips" style="font-size: 20px;">
          After successfully paying the conference fee, please return to this page for secondary confirmation of payment;
          otherwise, it will be deemed as non-payment of the conference fee! 
          </div>  
                  
        <template #footer>
          <span class="dialog-footer" style="justify-content: center;">
            <el-button v-if="tips"  @click="cancelPayment">cancel</el-button>
            <el-button type="primary" v-if="tips" @click="alreadKnowHandler" :loading="isProcessing" >
              I already know
            </el-button>
            <el-button type="primary" v-if="!tips" @click="showTransferForm = false" >
              Payment failed
            </el-button>
            <el-button type="primary" v-if="!tips" @click="submitTransferInfo" :loading="isProcessing" >
               payment successfully
            </el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed, reactive } from 'vue'
import { useRouter } from 'vue-router'
import SectionTitle from '@/components/common/SectionTitle.vue'
import { createPayment } from '@/api/payment'
import { getRegistrationInfo, getCompanionInfo, generateOrderNum } from '@/api/registration'
import { useUserStore } from '@/stores/user'
import { useRegistrationStore } from '@/stores/registration'
import { ElMessage } from 'element-plus'
import { useLanguage } from '@/hooks/useLanguage'
// import ConferenceRegistration from '../components/registration/ConferenceRegistration.vue'
export default {
  name: 'PaymentPage',
  components: {
    SectionTitle,
    // ConferenceRegistration
  },
  setup() {
    const router = useRouter()
    const userStore = useUserStore()
    const registrationStore = useRegistrationStore()
    const registrationInfo = computed(() => registrationStore.registrationInfo || {})
    const companionId = computed(() => registrationStore.companionId)
    const { getLocalePath } = useLanguage()
    const orderData = ref({
      id: 0,
      name: '',
      ticketType: '',
      amount: 0
    })
    const tableData = ref([
      { item: '国际CMC大会', price: '¥2,800', quantity: 1, subtotal: '¥2,800' },
      { item: 'ICMCI年会', price: '¥2,500', quantity: 1, subtotal: '¥2,500' },
      { item: '全程参会优惠', price: '-¥500', quantity: 1, subtotal: '-¥500' }
    ]);
    const total = ref('¥4,800');
    const paymentMethod = ref('wechat')
    const isProcessing = ref(false)
    const showTransferForm = ref(false)
    const transferFormRef = ref(null)
    const transferForm = ref({
      remitAccount: '',
      cardholdName: ''
    })

    const transferRules = {
      remitAccount: [
        { required: true, message: 'Please enter Transfer account', trigger: ['blur', 'change'] }
      ],
      cardholdName: [
        { required: true, message: 'Please enter Cardholder name', trigger: ['blur', 'change'] }
      ]
    }
    const currentMeetingForm = reactive({
      meetingType: [],
      forums: []
    })
    const conferenceFormRef = ref(null)

    // 监听表单数据变化，控制提交按钮状态
    const isFormValid = computed(() => {
      return transferForm.value.remitAccount && transferForm.value.cardholdName
    })
    const meeringUseInfo = ref({})

    // 计算参会人数
    const totalAttendees = computed(() => {
      if (!meeringUseInfo.value?.data?.data?.[0]?.peerInfoList) return 1
      return meeringUseInfo.value.data.data[0].peerInfoList.length + 1
    })

    // 计算单人费用
    const singleFee = computed(() => {
      return meeringUseInfo.value?.data?.data?.[0]?.orderAmtPayable || 0
    })

    // 计算总费用
    const totalFee = computed(() => {
      return (singleFee.value * totalAttendees.value).toFixed(2)
    })
    const orderUnit = ref('EUR');
    const tips = ref(true)
    onMounted(async () => {
      meeringUseInfo.value = await getRegistrationInfo(userStore.userInfo.id)
      // console.log(meeringUseInfo.value)
      currentMeetingForm.meetingType = registrationInfo.value.meetingType;
      currentMeetingForm.forums = registrationInfo.value.meetingType;
      orderUnit.value = registrationInfo.value.orderUnit;
    })

    const processPayment = async () => {
      // 检查是否选择了会议
      // if (currentMeetingForm.meetingType.length === 0 && currentMeetingForm.forums.length === 0) {
      //   ElMessage.warning('请至少选择一个主论坛或分论坛')
      //   return
      // }

      showTransferForm.value = true
      tips.value = true;
      
    }
    const alreadKnowHandler = () => {
      tips.value = false;
      setTimeout(()=>{
        window.open('https://www.paypal.com/ncp/payment/AX95YG67WQ87E', '_blank')
      },800)
    }
    const submitTransferInfo = async () => {

      try {
        const getOrderNum = await generateOrderNum()
        // const response = await getRegistrationInfo(userStore.userInfo.id)

        if (meeringUseInfo.value.code === 1000 && meeringUseInfo.value.data) {
          const data = meeringUseInfo.value.data.data[0]

          // 创建支付订单
          const payResult = await createPayment({
            ...data,
            registrationId: orderData.value.id,
            amount: orderData.value.amount,
            paymentMethod: paymentMethod.value,
            orderNum: getOrderNum.data.data,
            // 订单金额
            orderAmtPayable: singleFee.value,
            // 截止支付日期

            orderPaidAmt: 0,
            // 转账账号
            // remitAccount: transferForm.value.remitAccount,
            // cardholdName: transferForm.value.cardholdName,
            status: 'meeting_status_003',
            remindStatus: 'remind_status_002'
          })
          if(payResult.code == 1000){
            router.push({
              path: getLocalePath("dashboard"),
              query: { tab: "registrations" },
            });
            ElMessage.success('Payment information submitted successfully')
          }else{
            ElMessage.success('Payment information submitted successfully')
          }

        }
      } catch (error) {

        ElMessage.error('提交支付信息失败，请重试')
      } finally {
        isProcessing.value = false
        showTransferForm.value = false
      }
    }

    const cancelPayment = () => {
      showTransferForm.value = false
      transferForm.value = {
        remitAccount: '',
        cardholdName: ''
      }
    }

    const goBack = async () => {
      try {
        // 获取用户报名信息
        // const registrationResponse = await getRegistrationInfo(userStore.userInfo.id)
        const registrationResponse = meeringUseInfo.value;
        console.log('获取到的报名信息:', registrationResponse)

        if (registrationResponse.code === 1000) {
          const registrationData = registrationResponse.data.data[0]
          console.log('原始报名数据:', registrationData)

          // 处理参会信息
          const meetingTypes = registrationData.meetingTypes ? registrationData.meetingTypes.split(',') : []
          console.log('分割后的参会信息:', meetingTypes)

          // 更新数据结构
          const formattedData = {
            ...registrationData,
            meetingTypes: meetingTypes.filter(type => ['1', '2'].includes(type)),
            forums: meetingTypes.filter(type => !['1', '2'].includes(type))
          }
          console.log('格式化后的数据:', formattedData)

          // 分离同行人信息
          // const peerPeople = formattedData.peerInfoList || []


          // 存储报名信息
          sessionStorage.setItem('registrationFormData', JSON.stringify(formattedData))


          // 跳转到报名页面
          router.push('/english/registration')
        } else {
          ElMessage.error('获取报名信息失败')
        }
      } catch (error) {
        console.error('获取报名信息失败', error)
        ElMessage.error('获取报名信息失败，请稍后重试')
      }
    }

    return {
      alreadKnowHandler,
      orderData,
      paymentMethod,
      currentMeetingForm,
      isProcessing,
      processPayment,
      goBack,
      tableData,
      tips,
      registrationInfo,
      total,
      showTransferForm,
      transferForm,
      transferFormRef,
      submitTransferInfo,
      cancelPayment,
      isFormValid,
      conferenceFormRef,
      meeringUseInfo,
      totalAttendees,
      singleFee,
      totalFee,
      transferRules,
      orderUnit

    }
  }
}
</script>

<style scoped>
@import url('@/assets/css/payment.css');
::v-deep(.el-dialog__title){
  font-size: 18px;
  color: red;
  font-weight: 700;
}
</style>
