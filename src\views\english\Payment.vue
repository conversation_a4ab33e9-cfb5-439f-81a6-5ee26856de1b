<template>
  <div class="payment-page">
    <!-- 页面头部 -->
    <section class="page-header">
      <div class="container">
        <h1>Payment information confirmation</h1>
      </div>
    </section>

    <!-- 支付内容 -->
    <div class="conference-overview-container">
      <section class="payment-section">
        <div class="container">
          <div class="payment-container">
            <div class="payment-summary">
              <h2>Applicant</h2>
              <div >
                <ul >
                  <li v-for="(item, index) in [meeringUseInfo?.data?.data?.[0]]" class="order-details">
                    <span>Full Name：{{ item?.firstName }} {{ item?.lastName }}</span>
                    <span>Organization/Institution：{{ item?.organization }}</span>
                  </li>
                </ul>
              </div>
              <h2 style="margin-top: 20px;">Companions</h2>
              <div >
                <ul>
                  <li v-for="(item, index) in meeringUseInfo?.data?.data?.[0].peerInfoList" class="order-details">
                    <span>Full Name：{{ item?.firstName }} {{ item?.lastName }}</span>
                    <span>Organization/Institution：{{ item?.organization }}</span>
                  </li>
                </ul>
              </div>
              <el-divider></el-divider>
              <ul>
                <li class="order-details">

                   <span>Total Cost：</span>
                    <span>{{ orderUnit == '1' ? '￥' : 'EUR' }} {{ singleFee
                    }}</span>
                </li>
              </ul>
              <!-- <h2>订单信息</h2>
              <div class="order-details">
          
                <div class="order-item">
                  <span class="item-label">姓名:</span>
                  <span class="item-value" id="user-name">&nbsp;{{ meeringUseInfo?.data?.data?.[0]?.firstName }} &nbsp;{{
                    meeringUseInfo?.data?.data?.[0]?.lastName}}</span>
                </div>
                <div class="order-item">
                  <span class="item-label">电子邮箱:</span>
                  {{ meeringUseInfo?.data?.data?.[0]?.email }}
                </div>
                <div class="order-item">
                  <span class="item-label">参会人数:</span>
                  <span class="item-value" id="conference-type">&nbsp;{{ totalAttendees }}</span>
                </div>
                <div class="order-item">
                  <span class="item-label">参会费用:</span>
                  <span class="item-value" id="conference-type">&nbsp; {{ orderUnit == '1' ? '￥' : 'EUR' }} {{ singleFee
                    }}</span>
                </div>

              </div> -->

              <!-- <div style="margin-top:12px;">
                        <h3>费用明细</h3>
                      
                        <el-table :data="tableData" style="width: 100%" border>  
                          <el-table-column prop="item" label="项目" width="200"></el-table-column>  
                          <el-table-column prop="price" label="单价" width="120"></el-table-column>  
                          <el-table-column prop="quantity" label="数量" width="100"></el-table-column>  
                          <el-table-column prop="subtotal" label="小计" width="120"></el-table-column>  
                        </el-table>  
                        
                        <div style="margin-top: 20px; font-weight: bold;">  
                          总计: {{ total }}  
                        </div>  
                       
                    </div> -->
              <!-- <div class="form-section">
                      <h3>参会信息</h3>
                      <ConferenceRegistration 
                        ref="conferenceFormRef"
                        v-model="currentMeetingForm" 
                        @validate="isValid => isFormValid = isValid"
                      />
                    </div> -->
            </div>

            <div class="payment-methods">
              <!-- <h2>选择支付方式</h2>
            
            <div class="payment-options">
          
              <div 
                class="payment-option" 
                :class="{ 'active': true }"
                @click="paymentMethod = 'bank'"
              >
                <div class="option-icon">
                  <i class="fas fa-university"></i>
                </div>
                <div class="option-name">银行转账</div>
              </div>
            </div>
            
            <div class="payment-details">
     
              
              <div  class="bank-info">
                <h3>银行转账信息</h3>
                <div class="bank-details">
                  <div class="bank-item">
                    <div class="bank-label">收款单位</div>
                    <div class="bank-value">上海国际管理咨询大会组委会</div>
                  </div>
                  <div class="bank-item">
                    <div class="bank-label">开户银行</div>
                    <div class="bank-value">中国工商银行上海市分行</div>
                  </div>
                  <div class="bank-item">
                    <div class="bank-label">银行账号</div>
                    <div class="bank-value">6222 0000 0000 0000</div>
                  </div>
                  <div class="bank-item">
                    <div class="bank-label">转账说明</div>
                    <div class="bank-value">请在转账说明中注明：ICMCI2025+姓名</div>
                  </div>
                </div>
            
              </div>
            </div> -->
            </div>

            <div class="payment-actions">
              <button class="btn btn-primary" @click="processPayment" :disabled="isProcessing">
                {{ isProcessing ? 'pending...' : 'Confirm Payment' }}
              </button>
              <button class="btn btn-outline" @click="goBack">Return to Modify</button>
            </div>
          </div>
        </div>
      </section>

      <!-- 支付帮助 -->
      <!-- <section class="payment-help-section">
        <div class="container">
          <SectionTitle title="支付帮助" />

          <div class="payment-help">
            <div class="help-item">
              <h3>支付问题</h3>
              <p>如果您在支付过程中遇到任何问题，请联系我们的财务团队：</p>
              <p>电话：+86 10 8888 8888</p>
              <p>邮箱：<EMAIL></p>
            </div>

            <div class="help-item">
              <h3>发票信息</h3>
              <p>完成支付后，我们将根据您提供的发票信息开具电子发票，并发送至您的注册邮箱。</p>
              <p>如需修改发票信息，请在支付前联系我们。</p>
            </div>

            <div class="help-item">
              <h3>退款政策</h3>
              <p>2025年9月15日前取消：全额退款</p>
              <p>2025年9月16日至10月10日取消：退款50%</p>
              <p>2025年10月11日后取消：不予退款</p>
            </div>
          </div>
        </div>
      </section> -->

      <!-- 转账信息弹窗 -->
      <el-dialog v-model="showTransferForm" title="填写转账信息" width="500px" :close-on-click-modal="false"
        :close-on-press-escape="false" :show-close="false">
        <el-form ref="transferFormRef" :model="transferForm" :rules="transferRules" label-width="120px"
          :validate-on-rule-change="true">
          <el-form-item label="转账账号" prop="remitAccount" required>
            <el-input v-model.trim="transferForm.remitAccount" placeholder="请输入转账账号" type="number" clearable />
          </el-form-item>
          <el-form-item label="持卡人姓名" prop="cardholdName" required>
            <el-input v-model="transferForm.cardholdName" placeholder="请输入持卡人姓名" clearable />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="cancelPayment">取消</el-button>
            <el-button type="primary" @click="submitTransferInfo" :loading="isProcessing" :disabled="!isFormValid">
              确认
            </el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed, reactive } from 'vue'
import { useRouter } from 'vue-router'
import SectionTitle from '@/components/common/SectionTitle.vue'
import { createPayment } from '@/api/payment'
import { getRegistrationInfo, getCompanionInfo, generateOrderNum } from '@/api/registration'
import { useUserStore } from '@/stores/user'
import { useRegistrationStore } from '@/stores/registration'
import { ElMessage } from 'element-plus'
import { useLanguage } from '@/hooks/useLanguage'
// import ConferenceRegistration from '../components/registration/ConferenceRegistration.vue'
export default {
  name: 'PaymentPage',
  components: {
    SectionTitle,
    // ConferenceRegistration
  },
  setup() {
    const router = useRouter()
    const userStore = useUserStore()
    const registrationStore = useRegistrationStore()
    const registrationInfo = computed(() => registrationStore.registrationInfo || {})
    const companionId = computed(() => registrationStore.companionId)
    const { getLocalePath } = useLanguage()
    const orderData = ref({
      id: 0,
      name: '',
      ticketType: '',
      amount: 0
    })
    const tableData = ref([
      { item: '国际CMC大会', price: '¥2,800', quantity: 1, subtotal: '¥2,800' },
      { item: 'ICMCI年会', price: '¥2,500', quantity: 1, subtotal: '¥2,500' },
      { item: '全程参会优惠', price: '-¥500', quantity: 1, subtotal: '-¥500' }
    ]);
    const total = ref('¥4,800');
    const paymentMethod = ref('wechat')
    const isProcessing = ref(false)
    const showTransferForm = ref(false)
    const transferFormRef = ref(null)
    const transferForm = ref({
      remitAccount: '',
      cardholdName: ''
    })

    const transferRules = {
      remitAccount: [
        { required: true, message: '请输入转账账号', trigger: ['blur', 'change'] }
      ],
      cardholdName: [
        { required: true, message: '请输入持卡人姓名', trigger: ['blur', 'change'] }
      ]
    }
    const currentMeetingForm = reactive({
      meetingType: [],
      forums: []
    })
    const conferenceFormRef = ref(null)

    // 监听表单数据变化，控制提交按钮状态
    const isFormValid = computed(() => {
      return transferForm.value.remitAccount && transferForm.value.cardholdName
    })
    const meeringUseInfo = ref({})

    // 计算参会人数
    const totalAttendees = computed(() => {
      if (!meeringUseInfo.value?.data?.data?.[0]?.peerInfoList) return 1
      return meeringUseInfo.value.data.data[0].peerInfoList.length + 1
    })

    // 计算单人费用
    const singleFee = computed(() => {
      return meeringUseInfo.value?.data?.data?.[0]?.orderAmtPayable || 0
    })

    // 计算总费用
    const totalFee = computed(() => {
      return (singleFee.value * totalAttendees.value).toFixed(2)
    })
    const orderUnit = ref('EUR');
    onMounted(async () => {
      meeringUseInfo.value = await getRegistrationInfo(userStore.userInfo.id)
      // console.log(meeringUseInfo.value)
      currentMeetingForm.meetingType = registrationInfo.value.meetingType;
      currentMeetingForm.forums = registrationInfo.value.meetingType;
      orderUnit.value = registrationInfo.value.orderUnit;
    })

    const processPayment = async () => {
      // 检查是否选择了会议
      // if (currentMeetingForm.meetingType.length === 0 && currentMeetingForm.forums.length === 0) {
      //   ElMessage.warning('请至少选择一个主论坛或分论坛')
      //   return
      // }

      // showTransferForm.value = true
      submitTransferInfo()
      window.open('https://www.paypal.com/ncp/payment/M4GWHAHSA85LC','_blank')
    }

    const submitTransferInfo = async () => {

      try {
        const getOrderNum = await generateOrderNum()
        // const response = await getRegistrationInfo(userStore.userInfo.id)

        if (meeringUseInfo.value.code === 1000 && meeringUseInfo.value.data) {
          const data = meeringUseInfo.value.data.data[0]

          // 创建支付订单
          const payResult = await createPayment({
            ...data,
            registrationId: orderData.value.id,
            amount: orderData.value.amount,
            paymentMethod: paymentMethod.value,
            orderNum: getOrderNum.data.data,
            // 订单金额
            orderAmtPayable: singleFee.value,
            // 截止支付日期

            orderPaidAmt: 0,
            // 转账账号
            // remitAccount: transferForm.value.remitAccount,
            // cardholdName: transferForm.value.cardholdName,
            status: 'meeting_status_003',
            remindStatus: 'remind_status_002'
          })
          
      
        }
      } catch (error) {
       
        ElMessage.error('提交支付信息失败，请重试')
      } finally {
        isProcessing.value = false
        showTransferForm.value = false
      }
    }

    const cancelPayment = () => {
      showTransferForm.value = false
      transferForm.value = {
        remitAccount: '',
        cardholdName: ''
      }
    }

    const goBack = async () => {
      try {
        // 获取用户报名信息
        // const registrationResponse = await getRegistrationInfo(userStore.userInfo.id)
        const registrationResponse = meeringUseInfo.value;
        console.log('获取到的报名信息:', registrationResponse)

        if (registrationResponse.code === 1000) {
          const registrationData = registrationResponse.data.data[0]
          console.log('原始报名数据:', registrationData)

          // 处理参会信息
          const meetingTypes = registrationData.meetingTypes ? registrationData.meetingTypes.split(',') : []
          console.log('分割后的参会信息:', meetingTypes)

          // 更新数据结构
          const formattedData = {
            ...registrationData,
            meetingTypes: meetingTypes.filter(type => ['1', '2'].includes(type)),
            forums: meetingTypes.filter(type => !['1', '2'].includes(type))
          }
          console.log('格式化后的数据:', formattedData)

          // 分离同行人信息
          // const peerPeople = formattedData.peerInfoList || []


          // 存储报名信息
          sessionStorage.setItem('registrationFormData', JSON.stringify(formattedData))


          // 跳转到报名页面
          router.push('/english/registration')
        } else {
          ElMessage.error('获取报名信息失败')
        }
      } catch (error) {
        console.error('获取报名信息失败', error)
        ElMessage.error('获取报名信息失败，请稍后重试')
      }
    }

    return {
      orderData,
      paymentMethod,
      currentMeetingForm,
      isProcessing,
      processPayment,
      goBack,
      tableData,
      registrationInfo,
      total,
      showTransferForm,
      transferForm,
      transferFormRef,
      submitTransferInfo,
      cancelPayment,
      isFormValid,
      conferenceFormRef,
      meeringUseInfo,
      totalAttendees,
      singleFee,
      totalFee,
      transferRules,
      orderUnit

    }
  }
}
</script>

<style scoped>
@import url('@/assets/css/payment.css');
</style>
