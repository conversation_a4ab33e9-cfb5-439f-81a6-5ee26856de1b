.conference-overview-container{
  background: url('@/assets/images/home-bg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  min-height: 100vh;
  
}
/* 添加页面容器样式，防止溢出 */
.agenda-page {
  width: 100%;
  overflow-x: hidden;
  position: relative;
}

.page-header {
  background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('@/assets/images/bingmayong.png');
  background-size: cover;
  background-position: center;
  color: var(--text-white);
  padding: 120px 0 60px;
  text-align: center;
  position: relative;
}

.page-header h1 {
  font-size: 42px;
  margin-bottom: 15px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  animation: fadeInDown 1s ease-out;
}

.page-header p {
  font-size: 18px;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  animation: fadeInUp 1s ease-out;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.preview-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
  margin: 40px 0;
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
}

.preview-card {
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: var(--transition);
}

.preview-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-lg);
}

/* 标签页容器样式优化 */
.agenda-tabs {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 40px;
  position: relative;
  z-index: 10;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  padding: 0 20px;
}

/* 标签页容器可见状态 */
.agenda-tabs[v-intersection-observer-visible="true"] {
  opacity: 1;
  transform: translateY(0);
}

/* 标签页样式优化 */
.agenda-tab {
  opacity: 1 !important;
  padding: 15px 30px;
  background-color: var(--bg-light);
  border-radius: var(--radius-sm) var(--radius-sm) 0 0;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--border-color);
  border-bottom: none;
  position: relative;
  top: 1px;
  will-change: transform, background-color;
  white-space: nowrap;
  flex-shrink: 0;
  box-sizing: border-box;
}

/* 标签页动画 */
.agenda-tab.animate__animated {
  animation-duration: 0.6s;
  animation-fill-mode: both;
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* 标签页交互效果 */
.agenda-tab:hover {
  background-color: rgba(30, 136, 229, 0.1);
  transform: translateY(-2px);
}

.agenda-tab.active {
  background-color: var(--bg-color);
  color: var(--primary-color);
  border-bottom: none;
  border-top: 3px solid var(--primary-color);
  z-index: 1;
}

/* 内容区域样式优化 */
.agenda-content {
  width: 100%;
  max-width: 800px;
  margin: 0 auto 60px;
  box-sizing: border-box;
  padding: 0 20px;
  /* background-color: var(--bg-light); */
}

/* 日程项目样式优化 */
.agenda-item {
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 30px;
  display: grid;
  grid-template-columns: 120px 1fr;
  gap: 30px;
  background-color: var(--bg-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  padding: 30px;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .agenda-tabs {
    gap: 10px;
    padding: 0 10px;
  }

  .agenda-tab {
    padding: 12px 20px;
    font-size: 14px;
  }

  .agenda-content {
    padding: 0 15px;
  }

  .agenda-item {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .agenda-time {
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 10px;
  }

  .cta-card {
    flex-direction: column;
  }

  .cta-actions {
    border-left: none;
    border-top: 1px solid var(--border-color);
    padding-top: 20px;
  }
}

.agenda-days {
  margin-bottom: 40px;
  border: 1px solid var(--border-color);
  border-radius: 0 var(--radius-sm) var(--radius-sm) var(--radius-sm);
  padding: 30px;
  background-color: var(--bg-color);
}

.agenda-day {
  display: none;
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.5s ease, transform 0.5s ease;
  padding: 30px;
}

.agenda-day.active {
  display: block;
  opacity: 1;
  transform: translateY(0);
}

.agenda-items {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.agenda-item {
  display: grid;
  grid-template-columns: 120px 1fr;
  gap: 30px;
  background-color: var(--bg-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  padding: 30px;
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.5s ease, transform 0.5s ease;
  margin-bottom: 30px;
}

.agenda-day.active .agenda-item {
  opacity: 1;
  transform: translateY(0);
}

.agenda-day.active .agenda-item:nth-child(1) {
  transition-delay: 0.1s;
}

.agenda-day.active .agenda-item:nth-child(2) {
  transition-delay: 0.2s;
}

.agenda-day.active .agenda-item:nth-child(3) {
  transition-delay: 0.3s;
}

.agenda-day.active .agenda-item:nth-child(4) {
  transition-delay: 0.4s;
}

.agenda-day.active .agenda-item:nth-child(5) {
  transition-delay: 0.5s;
}

.agenda-day.active .agenda-item:nth-child(6) {
  transition-delay: 0.6s;
}

.agenda-time {
  font-size: 18px;
  font-weight: 700;
  color: var(--primary-color);
}

.agenda-title {
  font-size: 20px;
  font-weight: 400;
}

.agenda-description {
  color: var(--text-light);
  margin-bottom: 15px;
}

.session-info {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  color: var(--text-light);
}

.agenda-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 40px;
}

.share-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.btn-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--bg-light);
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  transition: var(--transition);
}

.btn-icon:hover {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.speaker-image {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  object-fit: cover;
}

.speaker-card {
  display: flex;
  gap: 20px;
  padding: 20px;
  background-color: var(--bg-light);
  border-radius: var(--radius-md);
  margin-top: 20px;
}

.speaker-info {
  flex: 1;
}

.speaker-name {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 5px;
}

.speaker-title {
  color: var(--primary-color);
  font-size: 14px;
  margin-bottom: 5px;
}

.speaker-company {
  color: var(--text-light);
  font-size: 14px;
  margin-bottom: 10px;
}

.speaker-bio {
  font-size: 14px;
  color: var(--text-color);
  margin-bottom: 10px;
}

.speaker-social {
  display: flex;
  gap: 10px;
}

.speaker-social a {
  color: var(--text-light);
  transition: color 0.3s ease;
}

.speaker-social a:hover {
  color: var(--primary-color);
}

.registration-cta {
  /* background-color: var(--bg-light); */
  padding: 60px 0;
  margin-top: 30px;
}

.cta-card {
  display: flex;
  gap: 40px;
  background-color: var(--bg-color);
  border-radius: var(--radius-lg);
  padding: 40px;
  box-shadow: var(--shadow-lg);
  align-items: center;
}

.cta-content {
  flex: 1;
}

.cta-content h2 {
  font-size: 28px;
  margin-bottom: 20px;
  color: var(--primary-color);
}

.benefits-list {
  list-style: none;
  padding: 0;
  margin: 20px 0;
}

.benefits-list li {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
  color: var(--text-color);
}

.benefits-list i {
  color: var(--primary-color);
}

.cta-actions {
  text-align: center;
  padding: 20px;
  border-left: 1px solid var(--border-color);
}

.price-info {
  margin: 20px 0;
  font-size: 18px;
  font-weight: 600;
}

.original-price {
  text-decoration: line-through;
  color: var(--text-light);
  font-size: 14px;
  margin-left: 10px;
}

.deadline {
  color: var(--text-light);
  font-size: 14px;
}

@media (max-width: 992px) {
  .preview-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 标签页动画 */
.agenda-tab.animate__animated {
  animation-duration: 0.8s;
  animation-fill-mode: both;
}

/* 其他元素动画样式 */
.agenda-item,
.speaker-card,
.cta-card,
.benefits-list li {
  opacity: 0;
  transition: all 0.3s ease;
}

.agenda-item.animate__animated,
.speaker-card.animate__animated,
.cta-card.animate__animated,
.benefits-list li.animate__animated {
  opacity: 1;
  animation-duration: 1s;
  animation-fill-mode: both;
}

/* 确保动画元素在动画完成后保持可见 */
.animate__fadeInDown,
.animate__fadeInUp,
.animate__fadeInLeft,
.animate__fadeInRight {
  animation-fill-mode: forwards !important;
}

/* 卡片动画效果 */
.agenda-item {
  transition: all 0.4s cubic-bezier(0.5, 0, 0, 1);
  transform: translateZ(0);
  will-change: transform, opacity;
}

.agenda-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
}

/* 演讲者卡片动画 */
.speaker-card {
  transition: all 0.4s cubic-bezier(0.5, 0, 0, 1);
  will-change: transform, opacity;
}

.speaker-card:hover {
  transform: translateX(5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

/* 按钮动画 */
.btn-primary.animate__pulse {
  animation-duration: 2s;
  animation-iteration-count: infinite;
}

.conference-tabs {
  display: flex;
  justify-content: center;
  gap: 30px;
  padding: 60px 20px;
  position: relative;
  z-index: 15;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);

}

.conference-tabs[v-intersection-observer-visible="true"] {
  opacity: 1;
  transform: translateY(0);
}

.conference-tab {
  padding: 20px 40px;
  background-color: var(--bg-color);
  border-radius: var(--radius-lg);
  cursor: pointer;
  font-weight: 600;
  font-size: 22px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

.conference-tab:hover {
  background-color: rgba(30, 136, 229, 0.1);
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.conference-tab.active {
  background: linear-gradient(135deg, var(--primary-color), #2980b9);
  color: var(--text-white);
  border-color: var(--primary-color);
  box-shadow: 0 8px 25px rgba(30, 136, 229, 0.3);
  transform: translateY(-3px);
}

.conference-tab.active::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.conference-tab.active:hover::before {
  left: 100%;
}
