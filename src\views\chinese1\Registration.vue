<template>
  <div class="registration-page">
    <!-- 页面头部 -->
    <section class="page-header">
      <div class="container">
        <h1>报名参会</h1>
        <p>2025年10月21-24日 · 上海浦东香格里拉大酒店</p>
      </div>
    </section>
    
    <!-- 报名表单 -->
    <section class="registration-form-section">
      <div class="container">
        <div class="form-container">
          <h2>参会报名</h2>
          <p class="form-desc">请填写以下信息完成报名，带 <span class="required">*</span> 为必填项</p>
          
          <el-form 
            ref="formRef"
            :model="form"
            :rules="rules"
            class="registration-form" 
            @submit.prevent="submitForm"
            label-position="top"
          >
            <div class="form-section">
              <h3>基本信息</h3>
              
              <div class="form-row">
                <div class="form-group">
                  <el-form-item label="姓" prop="firstName" required>
                    <el-input 
                      v-model="form.firstName" 
                      placeholder="请输入姓"
                    />
                  </el-form-item>
                </div>
                <div class="form-group">
                  <el-form-item label="名" prop="lastName" required>
                    <el-input 
                      v-model="form.lastName" 
                      placeholder="请输入名"
                    />
                  </el-form-item>
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <el-form-item label="性别" prop="sex" required>
                    <el-radio-group v-model="form.sex">
                      <el-radio label="sex_001">男</el-radio>
                      <el-radio label="sex_002">女</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </div>
                <div class="form-group">
                  <el-form-item label="国籍" prop="country" required>
                    <el-input 
                      v-model="form.country" 
                      placeholder="请输入国籍"
                    />
                  </el-form-item>
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <el-form-item label="组织" prop="organization" required>
                    <el-input 
                      v-model="form.organization" 
                      placeholder="请输入组织名称"
                    />
                  </el-form-item>
                </div>
                <div class="form-group">
                  <el-form-item label="职位" prop="position" required>
                    <el-input 
                      v-model="form.position" 
                      placeholder="请输入职位"
                    />
                  </el-form-item>
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <el-form-item label="电话号码" prop="telephone" required>
                    <el-input 
                      v-model="form.telephone" 
                      placeholder="请输入电话号码"
                    />
                  </el-form-item>
                </div>
                <div class="form-group">
                  <el-form-item label="通讯地址" prop="telAddr" required>
                    <el-input 
                      v-model="form.telAddr" 
                      placeholder="请输入通讯地址"
                    />
                  </el-form-item>
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <el-form-item label="邮箱" prop="email" required>
                    <el-input 
                      v-model="form.email" 
                      placeholder="请输入邮箱"
                    />
                  </el-form-item>
                </div>
                <div class="form-group">
                  <el-form-item label="护照号" prop="passportNum" required>
                    <el-input 
                      v-model="form.passportNum" 
                      placeholder="请输入护照号"
                    />
                  </el-form-item>
                </div>
              </div>
              <div class="form-row">
              <div class="form-group">
                <el-form-item label="是否需要签证邀请函" prop="invitePassportFlag" required>
                  <el-radio-group v-model="form.invitePassportFlag">
                    <el-radio label="invite_passport_flag_001">是</el-radio>
                    <el-radio label="invite_passport_flag_002">否</el-radio>
                  </el-radio-group>
                  <!-- <p class="form-hint" v-if="form.invitePassportFlag === 'invite_passport_flag_001'">
                    只涵盖会议期间和路上往返共五天
                  </p> -->
                </el-form-item>
              </div>
              <div class="form-group">
                <el-form-item label="是否第一次来华" prop="firstToCn" required>
                  <el-radio-group v-model="form.firstToCn">
                    <el-radio label="first_to_cn_001">是</el-radio>
                    <el-radio label="first_to_cn_002">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </div>
              </div>
            </div>
            
            <div class="form-section" v-if="form.invitePassportFlag === 'invite_passport_flag_001'">
              <h3>签证信息</h3>
              
              <div class="form-row">
                <div class="form-group">
                      <el-form-item label="First Name" prop="visaInfo.visaFirstName" required>
              
                        <el-input 
                          v-model="form.visaInfo.visaFirstName" 
                          placeholder="请输入First Name"
                        />
                      </el-form-item>
                      
                    </div>
                    <div class="form-group">
                      <el-form-item label="Last Name" prop="visaInfo.visaLastName" required>
               
                        <el-input 
                          v-model="form.visaInfo.visaLastName" 
                          placeholder="请输入Last Name"
                        />
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="性别" prop="visaInfo.visaSex" required>
                        <el-radio-group v-model="form.visaInfo.visaSex">
                          <el-radio label="sex_001">男</el-radio>
                          <el-radio label="sex_002">女</el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </div>
                <div class="form-group">
                  <el-form-item label="出生日期" prop="visaInfo.visaBirth" required>
                    <el-date-picker
                      v-model="form.visaInfo.visaBirth"
                      type="date"
                      placeholder="请选择出生日期"
                   format="YYYY-MM-DD"
                      style="width: 100%"
                    />
                  </el-form-item>
                </div>
                <div class="form-group">
                  <el-form-item label="申请签证地点" prop="visaInfo.visaApplyPassportSite" required>
                    <el-input 
                      v-model="form.visaInfo.visaApplyPassportSite" 
                      placeholder="请输入申请签证地点"
                    />
                  </el-form-item>
                </div>
                <div class="form-group">
                      <el-form-item label="国籍" prop="visaInfo.visaCountry" required>
                        <el-input 
                          v-model="form.visaInfo.visaCountry" 
                          placeholder="请输入国籍"
                        />
                      </el-form-item>
                    </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <el-form-item label="入境日期" prop="visaInfo.visaEnterDate" required>
                    <el-date-picker
                      v-model="form.visaInfo.visaEnterDate"
                      type="date"
                  format="YYYY-MM-DD"
                      placeholder="请选择入境日期"
                      style="width: 100%"
                    />
                  </el-form-item>
                </div>
                <div class="form-group">
                      <el-form-item label="离境日期" prop="visaInfo.visaLeaveDate" required>
                        <el-date-picker
                          v-model="form.visaInfo.visaLeaveDate"
                          type="date"
                          placeholder="请选择离境日期"
                          style="width: 100%"
                        />
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="紧急联系人" prop="visaInfo.visaUrgentContacter" required>
                        <el-input 
                          v-model="form.visaInfo.visaUrgentContacter" 
                          placeholder="请输入紧急联系人"
                        />
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="邀请函接收邮箱" prop="visaInfo.visaEmail" required>
                        <el-input 
                          v-model="form.visaInfo.visaEmail" 
                          placeholder="请输入紧急联系人"
                        />
                      </el-form-item>
                    </div>
                <div class="form-group">
                  <el-form-item label="访问地点" prop="visaInfo.visaVisitSite" required>
                    <el-input 
                      v-model="form.visaInfo.visaVisitSite" 
                      placeholder="请输入访问地点"
                    />
                  </el-form-item>
                </div>
              </div>

              <div class="form-group">
                <el-form-item label="组织介绍" prop="visaInfo.visaOrgIntroduce" required>
                  <el-input
                    v-model="form.visaInfo.visaOrgIntroduce"
                    type="textarea"
                    :rows="5"
                    placeholder="请用英文填写，至少150个单词"
                  />
                  <p class="word-count" v-if="form.visaInfo.visaOrgIntroduce">
                    当前字数：{{ countWords(form.visaInfo.visaOrgIntroduce) }}
                  </p>
                </el-form-item>
              </div>

              <div class="form-group">
                <el-form-item label="个人简历" prop="visaInfo.visaPersonalIntroduce" required>
                  <el-input
                    v-model="form.visaInfo.visaPersonalIntroduce"
                    type="textarea"
                    :rows="5"
                    placeholder="请用英文填写，至少150个单词"
                  />
                  <p class="word-count" v-if="form.visaInfo.visaPersonalIntroduce">
                    当前字数：{{ countWords(form.visaInfo.visaPersonalIntroduce) }}
                  </p>
                </el-form-item>
              </div>

              <div class="form-group">
                <ImageUpload
                  label="护照照片"
                  prop="visaInfo.visaImage"
                  v-model="form.visaInfo.visaImage"
                  required
                />
              </div>
              
            </div>
            
            <div class="form-section">
              <h3>参会信息</h3>
              <ConferenceRegistration v-model="form" />
            </div>
            
            <div class="form-actions">
              <button type="submit" class="btn btn-primary" :disabled="isSubmitting">
                {{ isSubmitting ? '提交中...' : '提交报名' }}
              </button>
            </div>
          </el-form>
        </div>
      </div>
    </section>
    
    <!-- 报名须知 -->
    <section class="registration-info-section">
      <div class="container">
        <SectionTitle title="报名须知" />
        
        <div class="info-grid">
          <div class="info-item">
            <div class="info-icon">
              <i class="fas fa-info-circle"></i>
            </div>
            <h3>报名流程</h3>
            <ol>
              <li>填写并提交报名表</li>
              <li>收到确认邮件</li>
              <li>完成缴费</li>
              <li>获取电子票</li>
            </ol>
          </div>
          
          <div class="info-item">
            <div class="info-icon">
              <i class="fas fa-credit-card"></i>
            </div>
            <h3>缴费方式</h3>
            <p>支持以下缴费方式：</p>
            <ul>
              <li>在线支付（微信/支付宝）</li>
              <li>银行转账</li>
              <li>公对公转账（需提供发票信息）</li>
            </ul>
          </div>
          
          <div class="info-item">
            <div class="info-icon">
              <i class="fas fa-calendar-alt"></i>
            </div>
            <h3>重要日期</h3>
            <ul>
              <li>早鸟优惠截止：2025年7月31日</li>
              <li>报名截止：2025年10月15日</li>
              <li>会议日期：2025年10月21-24日</li>
            </ul>
          </div>
          
          <div class="info-item">
            <div class="info-icon">
              <i class="fas fa-file-alt"></i>
            </div>
            <h3>退款政策</h3>
            <ul>
              <li>2025年9月15日前取消：全额退款</li>
              <li>2025年9月16日至10月10日取消：退款50%</li>
              <li>2025年10月11日后取消：不予退款</li>
            </ul>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 常见问题 -->
    <section class="faq-section">
      <div class="container">
        <SectionTitle title="常见问题" />
        
        <div class="faq-list">
          <div class="faq-item">
            <div class="faq-question" @click="toggleFaq(0)">
              <h3>如何获取发票？</h3>
              <i class="fas" :class="activeFaq === 0 ? 'fa-minus' : 'fa-plus'"></i>
            </div>
            <div class="faq-answer" :class="{ 'active': activeFaq === 0 }">
              <p>完成缴费后，您可以在用户中心申请发票。我们将在收到申请后5个工作日内开具电子发票，并发送至您的注册邮箱。</p>
            </div>
          </div>
          
          <div class="faq-item">
            <div class="faq-question" @click="toggleFaq(1)">
              <h3>是否提供住宿安排？</h3>
              <i class="fas" :class="activeFaq === 1 ? 'fa-minus' : 'fa-plus'"></i>
            </div>
            <div class="faq-answer" :class="{ 'active': activeFaq === 1 }">
              <p>会议不包含住宿费用，但我们已与会议酒店协商了优惠价格。您可以在报名成功后，通过用户中心预订酒店房间。详情请参见"住宿信息"页面。</p>
            </div>
          </div>
          
          <div class="faq-item">
            <div class="faq-question" @click="toggleFaq(2)">
              <h3>如何修改报名信息？</h3>
              <i class="fas" :class="activeFaq === 2 ? 'fa-minus' : 'fa-plus'"></i>
            </div>
            <div class="faq-answer" :class="{ 'active': activeFaq === 2 }">
              <p>报名成功后，您可以登录用户中心修改个人信息。如需修改参会类型或发票信息，请联系会议组委会：<EMAIL>。</p>
            </div>
          </div>
          
          <div class="faq-item">
            <div class="faq-question" @click="toggleFaq(3)">
              <h3>是否可以替换参会人员？</h3>
              <i class="fas" :class="activeFaq === 3 ? 'fa-minus' : 'fa-plus'"></i>
            </div>
            <div class="faq-answer" :class="{ 'active': activeFaq === 3 }">
              <p>可以。如需替换参会人员，请发送邮件至**************************，提供原参会人和新参会人的信息。替换不收取额外费用。</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 图片预览对话框 -->
    <el-dialog v-model="dialogVisible" title="图片预览">
      <img w-full :src="dialogImageUrl" alt="Preview Image" />
    </el-dialog>
  </div>
</template>

<script>
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Loading, ZoomIn, Delete } from '@element-plus/icons-vue'
import SectionTitle from '@/components/common/SectionTitle.vue'
import ImageUpload from '@/components/common/ImageUpload.vue'
// import ConferenceRegistration from '../components/registration/ConferenceRegistration.vue'
import { submitRegistration, getRegistrationInfo, updateRegistration } from '../../api/registration'
import { useUserStore } from '../../stores/user'
import { useRegistrationStore } from '../../stores/registration'
import http from '@/utils/http'
import dayjs from 'dayjs'  // 引入 dayjs 处理日期

export default {
  name: 'RegistrationPage',
  components: {
    SectionTitle,
    Plus,
    Loading,
    ZoomIn,
    Delete,
    ImageUpload,
    // ConferenceRegistration
  },
  setup() {
    const router = useRouter()
    const userStore = useUserStore()
    const registrationStore = useRegistrationStore()
    const isSubmitting = ref(false)
    const activeFaq = ref(null)
    const formRef = ref(null)
    const isEdit = ref(false)

    // 初始化表单数据
    const form = ref({
      // 基本信息
      userId: userStore.userInfo.id,
      firstName: '',
      lastName: '',
      sex: '',
      country: '',
      organization: '',
      position: '',
      telephone: '',
      telAddr: '',
      email: '',
      passportNum: '',
      firstToCn: 'first_to_cn_002',
      invitePassportFlag: 'invite_passport_flag_002',
      
      // 签证信息
      visaInfo: {
        visaFirstName: '',
        visaLastName: '',
        visaSex: '',
        visaBirth: '',
        visaCountry: '',
        visaEnterDate: '',
        visaLeaveDate: '',
        visaUrgentContacter: '',
        visaImage: '',
        visaEmail: '',
        visaOrganization: '',
        visaPosition: '',
        visaApplyPassportSite: '',
        visaVisitSite: '',
        visaOrgIntroduce: '',
        visaPersonalIntroduce: '',
        personType: 'person_type_001'
      },
      
      // 参会信息
      meetingType: [],
      forums: []
    })

    // 表单校验规则
    const rules = {
      firstName: [{ required: true, message: '请输入姓', trigger: 'blur' }],
      lastName: [{ required: true, message: '请输入名', trigger: 'blur' }],
      sex: [{ required: true, message: '请选择性别', trigger: 'change' }],
      country: [{ required: true, message: '请输入国籍', trigger: 'blur' }],
      organization: [{ required: true, message: '请输入组织名称', trigger: 'blur' }],
      position: [{ required: true, message: '请输入职位', trigger: 'blur' }],
      telephone: [{ required: true, message: '请输入电话', trigger: 'blur' }],
      telAddr: [{ required: true, message: '请输入通讯地址', trigger: 'blur' }],
      email: [
        { required: true, message: '请输入邮箱', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
      ],
      passportNum: [{ required: true, message: '请输入护照号', trigger: 'blur' }],
      invitePassportFlag: [{ required: true, message: '请选择是否需要签证邀请函', trigger: 'change' }],
      firstToCn: [{ required: true, message: '请选择是否第一次来华', trigger: 'change' }],
      'visaInfo.visaBirth': [{ required: true, message: '请选择出生日期', trigger: 'change' }],
      'visaInfo.visaApplyPassportSite': [{ required: true, message: '请输入申请签证地点', trigger: 'blur' }],
      'visaInfo.visaEnterDate': [{ required: true, message: '请选择入境日期', trigger: 'change' }],
      'visaInfo.visaVisitSite': [{ required: true, message: '请输入访问地点', trigger: 'blur' }],
      'visaInfo.visaOrgIntroduce': [
        { required: true, message: '请输入组织介绍', trigger: 'blur' },
        { validator: (rule, value, callback) => {
          if (value && countWords(value) < 150) {
            callback(new Error('组织介绍至少需要150个单词'))
          } else {
            callback()
          }
        }, trigger: 'blur' }
      ],
      'visaInfo.visaPersonalIntroduce': [
        { required: true, message: '请输入个人简历', trigger: 'blur' },
        { validator: (rule, value, callback) => {
          if (value && countWords(value) < 150) {
            callback(new Error('个人简历至少需要150个单词'))
          } else {
            callback()
          }
        }, trigger: 'blur' }
      ],
      'visaInfo.visaImage': [{ required: true, message: '请上传照片', trigger: 'change' }],
      meetingType: [
        { type: 'array', required: true, message: '请至少选择一个会议', trigger: 'change' }
      ]
    }

    // 获取并填充用户报名信息
    const loadRegistrationInfo = () => {
      try {
        const savedData = sessionStorage.getItem('registrationFormData')
        console.log('从 sessionStorage 获取的数据:', savedData)
        
        if (savedData) {
          const data = JSON.parse(savedData)
          console.log('解析后的数据:', data)
          console.log('当前表单数据:', form.value)
          
          if (data.meetingType) {
            const types = typeof data.meetingType === 'string' ? 
              data.meetingType.split(',') : 
              Array.isArray(data.meetingType) ? data.meetingType : [];
              
            const mainForums = types.filter(type => ['1', '2'].includes(type));
            const subForums = types.filter(type => !['1', '2'].includes(type));
            
            data.meetingType = mainForums;
            data.forums = subForums;
          }
          
          form.value = {
            ...form.value,
            ...data,
            meetingType: Array.isArray(data.meetingType) ? data.meetingType : [],
            forums: Array.isArray(data.forums) ? data.forums : [],
            visaInfo: {
              ...form.value.visaInfo,
              ...(data.visaInfo || {})
            }
          }
          
          isEdit.value = true
          sessionStorage.removeItem('registrationFormData')
          
          // 等待 DOM 更新后重置表单校验状态
          nextTick(() => {
            if (formRef.value) {
              formRef.value.clearValidate()
            }
          })
        }
      } catch (error) {
        console.error('加载报名信息失败', error)
        ElMessage.error('加载报名信息失败')
      }
    }

    onMounted(() => {
      loadRegistrationInfo()
    })

    // 字数统计函数
    const countWords = (text) => {
      if (!text) return 0
      return text.trim().split(/\s+/).length
    }

    const ticketOptions = [
      // {
      //   value: 'full',
      //   name: '全程参会',
      //   price: '¥2,800',
      //   description: '参加全部会议日程（10月22-24日）',
      //   features: [
      //     '参加所有主题演讲和分论坛',
      //     '会议资料包',
      //     '午餐和茶歇',
      //     '欢迎晚宴'
      //   ]
      // },
      {
        value: 'international',
        name: '国际CMC大会',
        price: '¥1,200',
        description: '会期1.5天',
        features: [
          '各国CMC交流管理咨询经验，探讨管理咨询新趋势',
          '探讨合作机会，推动企业提升管理水平',
          '颁发国际管理咨询案例奖（君士坦丁奖）'
        ]
      },
      {
        value: 'annual',
        name: 'ICMCI年会',
        price: '¥800',
        description: '会期1.5天',
        features: [
          '总结过去一年工作各成员国组织交流经验、战略执行、会员发展业务项目进展及财务决算',
          '落实新一年工作任务，重点项目推动审议财务预算，增补执委会、相关专业委员会委员和理事'
        ]
      }
    ]
    
    const forumOptions = [
    { 
        value: '3',
        name: '各国CMC交流管理咨询经验，探讨管理咨询新趋势',
        price: 500
      },
      { 
        value: '4', 
        name: '探讨合作机会，推动企业提升管理水平',
        price: 500
      },
      { 
        value: '5', 
        name: '颁发国际管理咨询案例奖（君士坦丁奖）',
        price: 500
      },
      { 
        value: '6', 
        name: '总结过去一年工作,主要包括各成员国组织交流经验、战略执行、会员发展、业务项目进展及财务决算',
        price: 300
      },
      { 
        value: '7', 
        name: '审议财务预算，增补执委会、相关专业委员会委员和理事',
        price: 300
      }
    ]
    
    const toggleFaq = (index) => {
      activeFaq.value = activeFaq.value === index ? null : index
    }
    
    // 切换主论坛选择
    const toggleMeetingType = (value) => {
      const index = form.value.meetingType.indexOf(value)
      if (index === -1) {
        form.value.meetingType.push(value)
      } else {
        form.value.meetingType.splice(index, 1)
      }
    }

    // 计算金额
    const calculateAmount = () => {
      let amount = 0
      if (form.value.meetingType.includes('1')) {
        amount += 1200
      }
      if (form.value.meetingType.includes('2')) {
        amount += 800
      }
       // 计算分论坛费用
       form.value.forums.forEach(forumValue => {
        const forum = forumOptions.find(f => f.value === forumValue)
        if (forum) {
          amount += forum.price
        }
      })
      return amount
    }

    // 格式化日期函数
    const formatDate = (date) => {
      if (!date) return null
      return dayjs(date).format('YYYY-MM-DD')
    }

    const submitForm = async () => {
 
      if (!formRef.value) return
    
      try {
        await formRef.value.validate()
        if(form.value.meetingType.length === 0 && form.value.forums.length ===0 ){
          ElMessage.warning('请选择参加的会议')
          return
        }
        isSubmitting.value = true
      //  return
        // 计算金额
        const orderAmtPayable = calculateAmount()
        
        // 准备提交数据
        const submitData = {
          ...form.value,
          status: 'meeting_status_001', // 已报名，未缴费
          orderAmtPayable,
          orderPaidAmt: 0,
          meetingType: [...form.value.meetingType, ...form.value.forums].join(','),
          // 处理签证信息中的日期
          // "invitePassportFlag":是否需要邀请函   invite_passport_flag_001 需要 invite_passport_flag_002 不需要
          // "personType": 人员类型（报名人person_type_001   同行人person_type_002）
          visaInfo: form.value.invitePassportFlag === 'invite_passport_flag_001' 
            ? {
                ...form.value.visaInfo,
                visaBirth: formatDate(form.value.visaInfo.visaBirth),
                visaEnterDate: formatDate(form.value.visaInfo.visaEnterDate)
              }
            : null
        }
        
        console.log('提交的数据:', submitData)
        
        let response
        if (isEdit.value) {
          // 如果是编辑模式，调用更新接口
          submitData.id = form.value.id;
          response = await updateRegistration(submitData)
        } else {
          // 如果是新建模式，调用创建接口
          response = await submitRegistration(submitData)
        }
        
        console.log('提交响应:', response)
        
        if(response.code !== 1000){
          ElMessage.error(response.message || '提交失败')
          return
        }
        // 新增
        if (!isEdit.value) {
          registrationStore.setRegistrationId(response.data.data.id)
         
        }
        registrationStore.setRegistrationInfo({
            email:submitData.email,
            name:submitData.firstName + ' ' + submitData.lastName,
            meetingType:[...form.value.meetingType, ...form.value.forums],
           
          })
        // 询问是否添加同行人
        try {
          const confirm = await ElMessageBox.confirm(
            '报名信息提交成功，是否添加同行人？',
            '提示',
            {
              confirmButtonText: '添加同行人',
              cancelButtonText: '直接支付',
              type: 'success',
              distinguishCancelAndClose: true,
              closeOnClickModal:false,
              closeOnPressEscape:false,
              showClose: false
            }
          )
          if (confirm === 'confirm') {
            router.push('/companion')
          } else if (confirm === 'cancel') {
            localStorage.removeItem('companionFormData')
            router.push('/payment')
          }
        } catch (error) {
          if (error === 'cancel') {
            router.push('/payment')
            localStorage.removeItem('companionFormData')
          }
          console.log(error)
        }
      } catch (error) {
        console.error('报名提交失败', error)
        // ElMessage.error('请先登录')
        // router.replace('/login')
        // 获取第一个错误表单项
        const firstErrorField = formRef.value.$el.querySelector('.is-error')
        if (firstErrorField) {
          // 滚动到错误表单项
          firstErrorField.scrollIntoView({ 
            behavior: 'smooth',
            block: 'center'
          })
        }
        ElMessage.error('请检查表单填写是否正确')
      } finally {
        isSubmitting.value = false
      }
    }
    
    const fileList = ref([])
    const dialogVisible = ref(false)
    const dialogImageUrl = ref('')


    // 自定义上传方法
  

    // 上传前的验证
    const beforeUpload = (file) => {
      const isImage = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isImage) {
        ElMessage.error('只能上传JPG/PNG格式的图片!')
        return false
      }
      if (!isLt2M) {
        ElMessage.error('图片大小不能超过2MB!')
        return false
      }
      return true
    }

    // 移除文件
    const handleRemove = () => {
      form.value.visaInfo.visaImage = ''
      fileList.value = []
      // 触发表单验证
      if (formRef.value) {
        formRef.value.validateField('visaInfo.visaImage')
      }
    }

    // 图片预览
    const handlePictureCardPreview = (file) => {
      console.log(file.url)
      dialogImageUrl.value = file.url
      dialogVisible.value = true
    }

    return {
      form,
      formRef,
      rules,
      isSubmitting,
      ticketOptions,
      forumOptions,
      activeFaq,
      toggleFaq,
      submitForm,
      countWords,
      fileList,
      beforeUpload,

      handlePictureCardPreview,
      dialogVisible,
      dialogImageUrl,
      isOrganizationIntroValid: computed(() => {
        return countWords(form.value.visaInfo.visaOrgIntroduce) >= 150
      }),
      isResumeValid: computed(() => {
        return countWords(form.value.visaInfo.visaPersonalIntroduce) >= 150
      }),
      toggleMeetingType,
      calculateAmount,
      formatDate
    }
  }
}
</script>

<style scoped>
.page-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, #1565c0 100%);
  color: var(--text-white);
  padding: 120px 0 60px;
  text-align: center;
}

.page-header h1 {
  font-size: 36px;
  margin-bottom: 10px;
}

.registration-form-section {
  padding: 80px 0;
}

.form-container {
  max-width: 800px;
  margin: 0 auto;
  background-color: var(--bg-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: 40px;
}

.form-container h2 {
  text-align: center;
  margin-bottom: 10px;
}

.form-desc {
  text-align: center;
  color: var(--text-light);
  margin-bottom: 30px;
}

.required {
  color: var(--error-color);
}

.form-section {
  margin-bottom: 40px;
  padding-bottom: 30px;
  border-bottom: 1px solid var(--border-color);
}

.form-section:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.form-section h3 {
  font-size: 20px;
  margin-bottom: 20px;
  color: var(--primary-color);
}

.ticket-options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

.ticket-option {
  border: 2px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: 20px;
  cursor: pointer;
  transition: var(--transition);
}

.ticket-option.active {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 136, 229, 0.2);
}

.ticket-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.ticket-header h4 {
  font-size: 18px;
  margin: 0;
}

.ticket-price {
  font-size: 18px;
  font-weight: 700;
  color: var(--primary-color);
}

.ticket-desc {
  color: var(--text-light);
  margin-bottom: 15px;
  font-size: 14px;
}

.ticket-features ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.ticket-features li {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
}

.ticket-features i {
  color: var(--success-color);
  margin-top: 4px;
}

.checkbox-group {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.checkbox-label, .radio-label {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
}

.radio-group {
  display: flex;
  gap: 30px;
}

.form-actions {
  text-align: center;
  margin-top: 40px;
}

.registration-info-section {
  padding: 80px 0;
  background-color: var(--bg-light);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
  margin-top: 50px;
}

.info-item {
  background-color: var(--bg-color);
  border-radius: var(--radius-md);
  padding: 30px;
  box-shadow: var(--shadow-md);
}

.info-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: rgba(30, 136, 229, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

.info-icon i {
  font-size: 24px;
  color: var(--primary-color);
}

.info-item h3 {
  font-size: 18px;
  margin-bottom: 15px;
  color: var(--primary-color);
}

.info-item ul, .info-item ol {
  padding-left: 20px;
  margin: 0;
}

.info-item li {
  margin-bottom: 8px;
  color: var(--text-light);
}

.faq-section {
  padding: 80px 0;
}

.faq-list {
  max-width: 800px;
  margin: 50px auto 0;
}

.faq-item {
  margin-bottom: 20px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.faq-question {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background-color: var(--bg-light);
  cursor: pointer;
}

.faq-question h3 {
  font-size: 18px;
  margin: 0;
}

.faq-answer {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease, padding 0.3s ease;
}

.faq-answer.active {
  max-height: 200px;
  padding: 20px;
  border-top: 1px solid var(--border-color);
}

.faq-answer p {
  margin: 0;
  color: var(--text-light);
}

@media (max-width: 992px) {
  .ticket-options {
    grid-template-columns: 1fr;
  }
  
  .info-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .form-container {
    padding: 30px 20px;
  }
  
  .checkbox-group {
    grid-template-columns: 1fr;
  }
  
  .radio-group {
    flex-direction: column;
    gap: 15px;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
}

.word-count {
  font-size: 12px;
  color: var(--text-light);
  margin-top: 5px;
}

.form-hint {
  font-size: 12px;
  color: var(--text-light);
  margin-top: 5px;
}

.radio-group {
  display: flex;
  gap: 20px;
}

.radio-label {
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
}

.radio-label input[type="radio"] {
  margin: 0;
}

.form-group {
  flex: 1;
  margin-bottom: 20px;
}

:deep(.el-form-item) {
  margin-bottom: 0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--text-color);
}

:deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--border-color) inset;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px var(--primary-color) inset;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px var(--primary-color) inset;
}

:deep(.el-radio-group) {
  display: flex;
  gap: 20px;
}

:deep(.el-radio) {
  margin-right: 0;
}

:deep(.el-textarea__inner) {
  min-height: 100px;
  resize: vertical;
}

:deep(.el-date-editor) {
  width: 100%;
}

:deep(.el-upload--picture-card) {
  width: 100px;
  height: 100px;
  line-height: 100px;
}

:deep(.el-upload__tip) {
  font-size: 12px;
  color: var(--text-light);
  margin-top: 5px;
}

:deep(.is-loading) {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

:deep(.el-upload-list__item-thumbnail) {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

:deep(.el-upload-list__item-actions) {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  cursor: default;
  text-align: center;
  color: #fff;
  opacity: 0;
  font-size: 20px;
  background-color: rgba(0, 0, 0, 0.5);
  transition: opacity .3s;
}

:deep(.el-upload-list__item:hover .el-upload-list__item-actions) {
  opacity: 1;
}

:deep(.el-upload-list__item-actions span) {
  display: inline-block;
  margin: 0 5px;
  cursor: pointer;
}

:deep(.el-dialog__body img) {
  width: 100%;
  height: auto;
  max-height: 70vh;
  object-fit: contain;
}
</style>
