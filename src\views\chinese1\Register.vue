<template>
  <div class="register-page">
    <!-- 页面头部 -->
    <section class="page-header">
      <div class="container">
        <h1>注册</h1>
        <p>创建您的ICMCI 2025账户</p>
      </div>
    </section>
    <div class="conference-overview-container"> 
    <!-- 注册表单 -->
    <section class="register-section">
      <div class="container">
        <div class="auth-container">
          <div class="auth-form">
            <h2>创建账户</h2>
            <p class="auth-desc">
              注册账户，参与ICMCI 2025上海国际管理咨询大会
            </p>

            <el-form
              :model="registerForm"
              :rules="rules"
              ref="registerFormRef"
              label-width="100px"
              class="auth-form"
            >
              <el-row :gutter="20">
                <!-- 第一列 -->
                <el-col :span="12">
                  <el-form-item label="姓" prop="firstName">
                    <el-input
                      v-model="registerForm.firstName"
                      placeholder="请输入姓"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="名" prop="lastName">
                    <el-input
                      v-model="registerForm.lastName"
                      placeholder="请输入名"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="账号" prop="account">
                    <el-input
                      v-model.trim="registerForm.account"
                      placeholder="请输入登录账号"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="密码" prop="password">
                    <el-input
                      v-model.trim="registerForm.password"
                      type="password"
                      placeholder="请输入密码"
                      show-password
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="确认密码" prop="confirmPassword">
                    <el-input
                      v-model.trim="registerForm.confirmPassword"
                      type="password"
                      show-password
                      placeholder="请再次输入密码"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="邮箱" prop="email">
                    <el-input
                      v-model.trim="registerForm.email"
                      placeholder="请输入工作邮箱"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="邮箱验证码" prop="emailCode">
                    <div class="verification-code">
                      <el-input
                        v-model.trim="registerForm.emailCode"
                        placeholder="请输入验证码"
                        style="width: 60%"
                      />
                      <el-button
                        type="primary"
                        :disabled="countdown > 0"
                        @click="handleSendCode"
                        style="width: 35%; margin-left: 5%"
                      >
                        {{
                          countdown > 0 ? `${countdown}秒后重试` : "获取验证码"
                        }}
                      </el-button>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="电话" prop="telephone">
                    <el-input
                      v-model.trim="registerForm.telephone"
                      placeholder="请输入联系电话"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <!-- <el-form-item label="地区" prop="area">
                    <el-select
                      v-model="registerForm.area"
                      placeholder="请选择所在地区"
                      style="width: 100%"
                    >
                      <el-option label="中国大陆" value="mainland" />
                      <el-option label="港澳台地区" value="hongkong" />
                      <el-option label="海外地区" value="overseas" />
                    </el-select>
                  </el-form-item> -->
                  <el-form-item
                        label="国籍"
                        prop="area"
                        required
                      >
                        <el-select
                          v-model="registerForm.area"
                          placeholder="请选择国籍"
                        >
                          <el-option
                            v-for="item in countryOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="组织机构" prop="organization">
                    <el-input
                      v-model="registerForm.organization"
                      placeholder="请输入所属机构/单位"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="职位" prop="position">
                    <el-input
                      v-model="registerForm.position"
                      placeholder="请输入当前职位"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="地址" prop="addr">
                    <el-input
                      v-model="registerForm.addr"
                      placeholder="请输入详细地址"
                    />
                  </el-form-item>
                </el-col>
                
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item prop="agreeTerms">
                    <el-checkbox v-model="registerForm.agreeTerms">
                      我已阅读并同意
                      <a @click="showTerms = true">服务条款</a> 和
                      <a @click="showPrivacy = true">隐私政策</a>
                    </el-checkbox>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item>
                <el-button
                  type="primary"
                  @click="handleRegister"
                  :loading="isLoading"
                  style="width: 100%"
                >
                  立即注册
                </el-button>
              </el-form-item>
            </el-form>

            <div class="auth-divider">
              <span>或</span>
            </div>

            <div class="auth-footer">
              <p>已有账户？ <router-link :to="getLocalePath('/login')">立即登录</router-link></p>
            </div>
          </div>

          <!-- <div class="auth-info">
            <div class="info-content">
              <h2>为什么加入我们？</h2>
              <ul class="benefits-list">
                <li>
                  <i class="fas fa-check-circle"></i>
                  <span>与全球管理咨询精英交流</span>
                </li>
                <li>
                  <i class="fas fa-check-circle"></i>
                  <span>获取行业前沿资讯和研究</span>
                </li>
                <li>
                  <i class="fas fa-check-circle"></i>
                  <span>参与高质量的专业培训</span>
                </li>
                <li>
                  <i class="fas fa-check-circle"></i>
                  <span>拓展专业人脉和合作机会</span>
                </li>
                <li>
                  <i class="fas fa-check-circle"></i>
                  <span>提升个人和企业的专业形象</span>
                </li>
              </ul>

              <div class="testimonial">
                <div class="testimonial-content">
                  <p>
                    "ICMCI大会是管理咨询行业最具影响力的盛会，每次参会都能获得新的洞见和灵感。"
                  </p>
                </div>
                <div class="testimonial-author">
                  <div class="author-avatar">
                    <img src="@/assets/images/kehupingjia1.png"  alt="张教授" >
                  </div>
                  <div class="author-info">
                    <div class="author-name">张教授</div>
                    <div class="author-title">清华大学经济管理学院</div>
                  </div>
                </div>
              </div>
            </div>
          </div> -->
        </div>
      </div>
    </section>

    <!-- 服务条款弹窗 -->
    <div class="modal" v-if="showTerms">
      <div class="modal-content">
        <div class="modal-header">
          <h3>服务条款</h3>
          <button class="modal-close" @click="showTerms = false">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <h4>1. 接受条款</h4>
          <p>
            欢迎使用ICMCI 2025上海国际管理咨询大会网站。本网站由ICMCI
            2025上海国际管理咨询大会组委会（以下简称"我们"）提供。通过访问和使用本网站，您同意受本服务条款的约束。
          </p>

          <h4>2. 用户账户</h4>
          <p>
            您需要创建账户才能使用本网站的某些功能。您有责任维护您的账户安全，并对您账户下的所有活动负责。
          </p>

          <h4>3. 用户行为</h4>
          <p>
            您同意不会使用本网站进行任何违法或未经授权的活动，包括但不限于：
          </p>
          <ul>
            <li>发布虚假、误导性或欺诈性信息</li>
            <li>侵犯他人的知识产权或隐私权</li>
            <li>传播恶意软件或病毒</li>
            <li>干扰网站的正常运行</li>
          </ul>

          <h4>4. 知识产权</h4>
          <p>
            本网站的所有内容，包括但不限于文本、图像、标志、按钮图标、软件和音频，均为我们或我们的内容提供商所有，受版权、商标和其他知识产权法律保护。
          </p>

          <h4>5. 免责声明</h4>
          <p>
            本网站按"现状"提供，不提供任何明示或暗示的保证。我们不保证网站将无错误运行或始终可用。
          </p>

          <h4>6. 责任限制</h4>
          <p>
            在任何情况下，我们对因使用或无法使用本网站而导致的任何损失或损害不承担责任。
          </p>

          <h4>7. 修改条款</h4>
          <p>
            我们保留随时修改本服务条款的权利。修改后的条款将在本网站上发布，并自发布之日起生效。
          </p>
        </div>
        <div class="modal-footer">
          <button class="btn btn-primary" @click="showTerms = false">
            我已阅读并理解
          </button>
        </div>
      </div>
    </div>

    <!-- 隐私政策弹窗 -->
    <div class="modal" v-if="showPrivacy">
      <div class="modal-content">
        <div class="modal-header">
          <h3>隐私政策</h3>
          <button class="modal-close" @click="showPrivacy = false">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <h4>1. 信息收集</h4>
          <p>
            我们收集的个人信息包括但不限于：姓名、电子邮件地址、电话号码、公司/组织名称、职位等。
          </p>

          <h4>2. 信息使用</h4>
          <p>我们使用收集的信息用于：</p>
          <ul>
            <li>处理您的会议注册和支付</li>
            <li>向您发送会议相关信息和更新</li>
            <li>改进我们的网站服务和</li>
            <li>进行市场研究和分析</li>
          </ul>

          <h4>3. 信息共享</h4>
          <p>
            我们不会出售、出租或交易您的个人信息给第三方。我们可能会在以下情况下共享您的信息：
          </p>
          <ul>
            <li>经您同意</li>
            <li>与我们的服务提供商共享，以便他们为我们提供服务</li>
            <li>遵守法律要求</li>
          </ul>

          <h4>4. 信息安全</h4>
          <p>
            我们采取适当的技术和组织措施来保护您的个人信息，防止未经授权的访问、使用或披露。
          </p>

          <h4>5. Cookie使用</h4>
          <p>
            我们使用Cookie和类似技术来改善您的浏览体验、分析网站流量和个性化内容。
          </p>

          <h4>6. 您的权利</h4>
          <p>
            您有权访问、更正、删除您的个人信息，以及限制或反对我们处理您的信息。
          </p>

          <h4>7. 政策更新</h4>
          <p>
            我们可能会不时更新本隐私政策。更新后的政策将在本网站上发布，并自发布之日起生效。
          </p>
        </div>
        <div class="modal-footer">
          <button class="btn btn-primary" @click="showPrivacy = false">
            我已阅读并理解
          </button>
        </div>
      </div>
    </div>
  </div>
  </div>
</template>

<script>
import { ref, computed, onUnmounted, onMounted, watch } from "vue";
import { useRouter } from "vue-router";
import { register, sendEmailVerification } from "../../api/auth";
import { ElMessage } from "element-plus";
import { sm2 } from "sm-crypto";
import {
    getDictCode
} from "../../api/dictCode";
import {useLanguage} from '@/hooks/useLanguage'
export default {
  name: "RegisterPage",
  setup() {
    const router = useRouter();
    const { getLocalePath} = useLanguage()
    const registerFormRef = ref(null);
    const registerForm = ref({
      firstName: "",
      lastName: "",
      account: "",
      password: "",
      confirmPassword: "",
      email: "",
      telephone: "",
      area: "",
      organization: "",
      position: "",
      addr: "",
      emailCode: "",  // 测试验证码
      agreeTerms: true
    });

    const rules = {
      firstName: [
        { required: true, message: "请输入姓", trigger: "blur" },
        { min: 1, max: 20, message: "长度在 1 到 20 个字符", trigger: "blur" },
      ],
      lastName: [
        { required: true, message: "请输入名", trigger: "blur" },
        { min: 1, max: 20, message: "长度在 1 到 20 个字符", trigger: "blur" },
      ],
      account: [
        { required: true, message: "请输入账号", trigger: "blur" },
        { min: 4, max: 20, message: "长度在 4 到 20 个字符", trigger: "blur" },
      ],
      password: [
        { required: true, message: "请输入密码", trigger: "blur" },
        { min: 8, message: "密码长度不能小于8位", trigger: "blur" },
          {
            pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*[~!@#$%^&*()_+`\-={}:";'<>?,./]).{8,}$/,
            message: "密码必须包含数字、大小写字母和特殊符号",
            trigger: "blur"
          }
      ],
      confirmPassword: [
        { required: true, message: "请确认密码", trigger: "blur" },
        {
          validator: (rule, value, callback) => {
            if (value !== registerForm.value.password) {
              callback(new Error("两次输入的密码不一致"));
            } else {
              callback();
            }
          },
          trigger: "blur",
        },
      ],
      email: [
        { required: true, message: "请输入邮箱", trigger: "blur" },
        { type: "email", message: "请输入正确的邮箱地址", trigger: "blur" },
      ],
      telephone: [
        { required: true, message: "请输入手机号", trigger: "blur" },
      
      ],
      area: [{ required: true, message: "请选择地区", trigger: "change" }],
      organization: [
        { required: true, message: "请输入组织机构", trigger: "blur" },
      ],
      position: [{ required: true, message: "请输入职位", trigger: "blur" }],
      addr: [{ required: true, message: "请输入地址", trigger: "blur" }],
      emailCode: [
        { required: true, message: "请输入验证码", trigger: "blur" },
        { len: 6, message: "验证码长度为6位", trigger: "blur" },
      ],
      agreeTerms: [
        { required: true, message: "请阅读并同意服务条款", trigger: "change" },
      ],
    };

    const isLoading = ref(false);
    const countdown = ref(0);
    const timer = ref(null);
    const showTerms = ref(false);
    const showPrivacy = ref(false);
    const publicKey = '042136fe42541b34a589a158382b5a2f65593c181af2b388f18745818d5a74350387e8bdd8d44828f4b94180cc4b5cd8143da5324ee9ba86a0fd7d75572a1f4e11';

    // 密码加密函数
    const encryptPassword = (password) => {
      try {

        console.log('加密前密码:', sm2,publicKey);
        // 使用 SM2 加密
        // const encrypted = sm2.doEncrypt(password, publicKey);
        return "04" + sm2.doEncrypt(password, publicKey);
        
      
      } catch (error) {
        console.error('密码加密失败:', error);
        ElMessage.error('密码加密失败，请稍后重试');
        throw error; // 抛出错误，让调用者处理
      }
    };

    // 发送验证码
    const handleSendCode = async () => {
      if (!registerForm.value.email) {
        ElMessage.warning("请先输入邮箱");
        return;
      }
      
      if (countdown.value > 0) return;
      
      try {
        const result = await sendEmailVerification(registerForm.value.email);
        
        if (result.code === 1000) {
          ElMessage.success(result.msg || "验证码已发送");
          countdown.value = 60; // 设置60秒倒计时
          timer.value = setInterval(() => {
            countdown.value--;
            if (countdown.value <= 0) {
              clearInterval(timer.value);
            }
          }, 1000);
        } else {
          ElMessage.error(result.msg || "发送失败，请稍后重试");
        }
      } catch (error) {
        console.error('发送验证码失败:', error);
        ElMessage.error(error.response?.data?.message || "发送失败，请稍后重试");
      }
    };

    const handleRegister = async () => {
      if (!registerFormRef.value) return;

      try {
        // 验证表单
        await registerFormRef.value.validate();
        isLoading.value = true;

        // 准备注册数据
        const registerData = {
          firstName: registerForm.value.firstName,
          lastName: registerForm.value.lastName,
          account: registerForm.value.account,
          password: encryptPassword(registerForm.value.password), // 加密密码
          email: registerForm.value.email,
          telephone: registerForm.value.telephone,
          area: registerForm.value.area,
          organization: registerForm.value.organization,
          position: registerForm.value.position,
          addr: registerForm.value.addr,
          emailCode: registerForm.value.emailCode,
        };

        console.log('注册数据:', registerData);

        // 发送注册请求
        const response = await register(registerData);

        if (response.code === 1000) {
          ElMessage.success("注册成功！请登录您的账户。");
          router.push(getLocalePath("/login"));
        } else {
          ElMessage.error(response.msg );
        }
      } catch (error) {
        // console.error("注册失败:", error);
        // ElMessage.error(error.response?.data?.message || "注册失败，请稍后重试");
      } finally {
        isLoading.value = false;
      }
    };

    // 组件卸载时清除定时器
    onUnmounted(() => {
      if (timer.value) {
        clearInterval(timer.value);
      }
    });
    const countryOptions = ref([]);
    onMounted(() => {
      getDictCode('country_code').then(res=>{
        countryOptions.value = res.data.data.map(item => ({
          value: item.dictionaryCode,
          label: item.dictionaryValue
        }));
      });
    });
    
    // 监听邮箱变化
    watch(() => registerForm.value.email, (newEmail, oldEmail) => {
      if (newEmail !== oldEmail) {
        // 清空验证码
        registerForm.value.emailCode = '';
        // 重置倒计时
        countdown.value = 0;
        if (timer.value) {
          clearInterval(timer.value);
        }
      }
    });
    
    return {
      registerFormRef,
      registerForm,
      countryOptions,
      rules,
      isLoading,
      countdown,
      showTerms,
      showPrivacy,
      handleSendCode,
      handleRegister,
      getLocalePath
    };
  },
};
</script>

<style scoped>
@import url('@/assets/css/register.css');
</style>
