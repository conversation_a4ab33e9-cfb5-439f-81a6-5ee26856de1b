<template>
  <div class="visits-page">
    <!-- 页面头部 -->
    <section class="page-header">
      <h1 class="animate__animated animate__fadeInDown">企业参观考察</h1>
      <p class="animate__animated animate__fadeInUp">探索中国优秀企业，了解创新发展</p>
    </section>

    <!-- 主要内容区域 -->
    <section class="visits-section">
      <div class="visits-container">
        <!-- 搜索区域 -->


        <!-- 企业列表 -->
        <div class="companies-section" v-intersection-observer="handleCompaniesIntersection">
          <h2 :class="{ 'animate__animated animate__fadeInLeft': companiesVisible }">推荐企业</h2>
          <el-row :gutter="20">
            <el-col 
              :span="8" 
              v-for="(company, index) in companies" 
              :key="company.id"
              :class="{ 'animate__animated animate__fadeInUp': companiesVisible }"
              :style="{ animationDelay: `${index * 0.2}s` }"
            >
              <el-card class="company-card" :body-style="{ padding: '0px' }">
                <div class="company-image-wrapper">
                  <img :src="company.image" class="company-image" />
                </div>
                <div class="company-info">
                  <h3>{{ company.name }}</h3>
                  <div class="company-tags">
                    <el-tag 
                      v-for="(tag, tagIndex) in [company.industry, company.region]" 
                      :key="tag"
                      size="small" 
                      :type="tagIndex === 0 ? 'success' : 'info'"
                      :class="{ 'animate__animated animate__fadeIn': companiesVisible }"
                      :style="{ animationDelay: `${index * 0.2 + tagIndex * 0.1}s` }"
                    >
                      {{ tag }}
                    </el-tag>
                  </div>
                  <p class="company-description">{{ company.description }}</p>
                  <div class="company-features">
                    <h4>参观亮点</h4>
                    <ul>
                      <li 
                        v-for="(feature, featureIndex) in company.features" 
                        :key="feature"
                        :class="{ 'animate__animated animate__fadeIn': companiesVisible }"
                        :style="{ animationDelay: `${index * 0.2 + featureIndex * 0.1}s` }"
                      >
                        {{ feature }}
                      </li>
                    </ul>
                  </div>
                  <div class="company-footer">
                    <span class="visit-time">参观时长：{{ company.duration }}</span>
                    <!-- <el-button type="primary" size="small" @click="handleBook(company)">预约参观</el-button> -->
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </div>
    </section>

    <!-- 预约弹窗 -->
    <el-dialog
      v-model="bookingDialogVisible"
      title="预约参观"
      width="500px"
    >
      <el-form :model="bookingForm" label-width="100px">
        <el-form-item label="参观日期">
          <el-date-picker
            v-model="bookingForm.date"
            type="date"
            placeholder="选择日期"
          />
        </el-form-item>
        <el-form-item label="参观人数">
          <el-input-number v-model="bookingForm.people" :min="1" :max="20" />
        </el-form-item>
        <el-form-item label="联系人">
          <el-input v-model="bookingForm.contact" />
        </el-form-item>
        <el-form-item label="联系电话">
          <el-input v-model="bookingForm.phone" />
        </el-form-item>
        <el-form-item label="参观目的">
          <el-input
            type="textarea"
            v-model="bookingForm.purpose"
            :rows="3"
            placeholder="请简要说明参观目的"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="bookingDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitBooking">确认预约</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import 'animate.css'

export default {
  name: 'VistisPage',
  directives: {
    intersectionObserver: {
      mounted(el, binding) {
        const observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              binding.value(true)
            } else {
              binding.value(false)
            }
          })
        }, {
          threshold: 0.1,
          rootMargin: '0px'
        })
        observer.observe(el)
      },
      updated(el, binding) {
        const observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              binding.value(true)
            } else {
              binding.value(false)
            }
          })
        }, {
          threshold: 0.1,
          rootMargin: '0px'
        })
        observer.observe(el)
      }
    }
  },
  setup() {
    // 搜索表单
    const searchForm = reactive({
      type: '',
      region: ''
    })

    // 企业数据
    const companies = ref([
      {
        id: 1,
        name: '华为技术有限公司',
        industry: '科技企业',
        region: '深圳',
        description: '全球领先的ICT（信息与通信）基础设施和智能终端提供商，致力于构建万物互联的智能世界。',
        image: 'http://gips2.baidu.com/it/u=*********,3554684702&fm=3028&app=3028&f=JPEG&fmt=auto?w=1280&h=960',
        duration: '2小时',
        features: [
          '参观华为总部园区',
          '了解5G技术发展',
          '体验智能终端产品',
          '交流创新管理经验'
        ]
      },
      {
        id: 2,
        name: '阿里巴巴集团',
        industry: '互联网',
        region: '杭州',
        description: '全球最大的电子商务平台之一，业务涵盖电商、云计算、数字媒体等多个领域。',
        image: 'http://gips2.baidu.com/it/u=*********,3554684702&fm=3028&app=3028&f=JPEG&fmt=auto?w=1280&h=960',
        duration: '3小时',
        features: [
          '参观阿里巴巴西溪园区',
          '了解电商平台运营',
          '体验新零售技术',
          '交流数字化转型经验'
        ]
      },
      {
        id: 3,
        name: '比亚迪股份有限公司',
        industry: '新能源',
        region: '深圳',
        description: '全球领先的新能源汽车制造商，在电池、电机、电控等核心技术领域具有优势。',
        image: 'http://gips2.baidu.com/it/u=*********,3554684702&fm=3028&app=3028&f=JPEG&fmt=auto?w=1280&h=960',
        duration: '2.5小时',
        features: [
          '参观新能源汽车生产线',
          '了解电池技术发展',
          '体验智能驾驶技术',
          '交流绿色制造经验'
        ]
      },
      {
        id: 4,
        name: '京东方科技集团',
        industry: '制造业',
        region: '北京',
        description: '全球领先的显示面板制造商，在LCD、OLED等领域具有核心技术优势。',
        image: 'http://gips2.baidu.com/it/u=*********,3554684702&fm=3028&app=3028&f=JPEG&fmt=auto?w=1280&h=960',
        duration: '2小时',
        features: [
          '参观显示面板生产线',
          '了解显示技术发展',
          '体验智能显示产品',
          '交流智能制造经验'
        ]
      },
      {
        id: 5,
        name: '腾讯科技',
        industry: '互联网',
        region: '深圳',
        description: '中国领先的互联网科技公司，业务涵盖社交、游戏、金融科技等多个领域。',
        image: 'http://gips2.baidu.com/it/u=*********,3554684702&fm=3028&app=3028&f=JPEG&fmt=auto?w=1280&h=960',
        duration: '2小时',
        features: [
          '参观腾讯总部大楼',
          '了解互联网产品开发',
          '体验创新科技应用',
          '交流数字文化发展'
        ]
      },
      {
        id: 6,
        name: '宁德时代新能源',
        industry: '新能源',
        region: '宁德',
        description: '全球领先的动力电池制造商，在电池技术研发和制造领域具有优势。',
        image: 'http://gips2.baidu.com/it/u=*********,3554684702&fm=3028&app=3028&f=JPEG&fmt=auto?w=1280&h=960',
        duration: '2小时',
        features: [
          '参观电池生产基地',
          '了解电池技术发展',
          '体验新能源应用',
          '交流绿色能源经验'
        ]
      }
    ])

    // 预约相关
    const bookingDialogVisible = ref(false)
    const selectedCompany = ref(null)
    const bookingForm = reactive({
      date: '',
      people: 1,
      contact: '',
      phone: '',
      purpose: ''
    })

    // 搜索处理
    const handleSearch = () => {
      // 实现搜索逻辑
      console.log('搜索条件：', searchForm)
    }

    // 预约参观
    const handleBook = (company) => {
      selectedCompany.value = company
      bookingDialogVisible.value = true
    }

    // 提交预约
    const submitBooking = () => {
      // 实现预约逻辑
      ElMessage.success('预约成功！')
      bookingDialogVisible.value = false
    }

    // 添加企业列表可见性状态
    const companiesVisible = ref(false)

    // 处理企业列表部分的交叉观察
    const handleCompaniesIntersection = (isVisible) => {
      companiesVisible.value = isVisible
    }

    return {
      searchForm,
      companies,
      bookingDialogVisible,
      selectedCompany,
      bookingForm,
      handleSearch,
      handleBook,
      submitBooking,
      companiesVisible,
      handleCompaniesIntersection
    }
  }
}
</script>

<style scoped>
.visits-page {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.page-header {
  background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('https://picsum.photos/2000/1000?random=23');
  background-size: cover;
  background-position: center;
  color: var(--text-white);
  padding: 120px 0 60px;
  text-align: center;
  position: relative;
}

.page-header h1 {
  font-size: 42px;
  margin-bottom: 15px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  animation: fadeInDown 1s ease-out;
}

.page-header p {
  font-size: 18px;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  animation: fadeInUp 1s ease-out;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.visits-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.search-section {
  margin-bottom: 40px;
}

.search-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.search-form {
  padding: 20px;
}

.companies-section {
  margin-bottom: 40px;
  opacity: 1;
  transition: opacity 0.3s ease;
}

h2 {
  font-size: 24px;
  color: var(--primary-color);
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid var(--primary-color);
}

.company-card {
  margin-bottom: 20px;
  transition: all 0.4s cubic-bezier(0.5, 0, 0, 1);
  cursor: pointer;
  transform: translateZ(0);
  will-change: transform;
}

.company-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 24px rgba(0,0,0,0.15);
}

.company-image-wrapper {
  overflow: hidden;
  position: relative;
}

.company-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: transform 0.6s cubic-bezier(0.5, 0, 0, 1);
}

.company-card:hover .company-image {
  transform: scale(1.1);
}

.company-info {
  padding: 15px;
}

.company-info h3 {
  margin: 0 0 10px;
  font-size: 18px;
  color: #333;
}

.company-tags {
  margin-bottom: 10px;
}

.company-tags .el-tag {
  margin-right: 8px;
  transition: all 0.3s ease;
}

.company-tags .el-tag:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.company-description {
  color: #666;
  font-size: 14px;
  margin-bottom: 15px;
  height: 40px;
  overflow: hidden;
}

.company-features {
  margin: 15px 0;
}

.company-features h4 {
  font-size: 16px;
  color: #333;
  margin-bottom: 10px;
}

.company-features ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.company-features li {
  color: #666;
  font-size: 14px;
  margin-bottom: 5px;
  padding-left: 15px;
  position: relative;
}

.company-features li:before {
  content: "•";
  position: absolute;
  left: 0;
  color: var(--primary-color);
}

.company-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
}

.visit-time {
  color: #666;
  font-size: 14px;
}

@media (max-width: 768px) {
  .visits-container {
    padding: 10px;
  }

  .el-col {
    width: 100%;
  }

  .search-form {
    :deep(.el-form-item) {
      margin-right: 0;
      width: 100%;
    }
  }
}

/* 添加动画相关样式 */
.companies-section h2,
.el-col,
.company-tags .el-tag,
.company-features li {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.companies-section h2.animate__animated,
.el-col.animate__animated,
.company-tags .el-tag.animate__animated,
.company-features li.animate__animated {
  opacity: 1;
}

/* 确保动画可以重复播放 */
.animate__animated {
  animation-duration: 1s;
  animation-fill-mode: both;
  animation-iteration-count: 1;
}

/* 优化卡片动画效果 */
.company-card {
  margin-bottom: 20px;
  transition: all 0.4s cubic-bezier(0.5, 0, 0, 1);
  cursor: pointer;
  transform: translateZ(0);
  will-change: transform;
}

.company-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 24px rgba(0,0,0,0.15);
}

.company-image-wrapper {
  overflow: hidden;
  position: relative;
}

.company-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: transform 0.6s cubic-bezier(0.5, 0, 0, 1);
}

.company-card:hover .company-image {
  transform: scale(1.1);
}

.company-tags .el-tag {
  margin-right: 8px;
  transition: all 0.3s ease;
}

.company-tags .el-tag:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
</style>
