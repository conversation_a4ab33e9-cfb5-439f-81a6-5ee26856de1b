<template>
  <div class="participants-page">
    <!-- 页面头部 -->
    <section class="page-header">
      <div class="container">
        <!-- <h1 class="animate__animated animate__fadeInDown">VIPs</h1>
        <p class="animate__animated animate__fadeInUp">October 21-24, 2025 · Pudong Shangri-La, Shanghai</p> -->
      </div>
    </section>
    <div class="conference-overview-container">
    <!-- 参会人员内容 -->
    <section class="participants-section">
      <div class="container">
        <div class="about-highlights bg" id="speakers" v-intersection-observer="handleSpeakersIntersection">
          <!-- <h2 class="section-title" :class="{ 'animate__animated animate__fadeInLeft': speakersVisible }" style="margin-top: 40px;">重要嘉宾</h2> -->
          <div class="speakers-grid">
            <div 
              v-for="(_, index) in 11" 
              :key="index"
              class="speaker-card"
              :class="{ 'animate__animated animate__fadeInUp': speakersVisible }"
              :style="{ animationDelay: `${index * 0.15}s` }"
            >
              <div class="speaker-image image-container image-placeholder">
                <img 
                  :src="getSpeakerImage(index)" 
                  :alt="getSpeakerName(index)"
                  loading="lazy"
                  @load="handleImageLoad"
                >
              </div>
              <div class="speaker-info">
                <p class="speaker-title">{{ getSpeakerTitle(index) }}</p>
                <p class="speaker-bio">{{ getSpeakerBio(index) }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import 'animate.css'
import nick_casual from '@/assets/images/vips/nick_casual.jpg'
import ruggero_picture from '@/assets/images/vips/ruggero_picture.jpg'
import celal from '@/assets/images/vips/celal.jpg'
import pic_of_norma_shorey from '@/assets/images/vips/pic_of_norma_shorey.jpg'
import gergana_mantarkova from '@/assets/images/vips/gergana-mantarkova-option-100x130-2.png'
import alan_blackman from '@/assets/images/vips/alan_blackman_-_photo.jpg'
import re5_9160 from '@/assets/images/vips/re5_9160.jpeg'
import img_5081_edited1 from '@/assets/images/vips/img_5081_edited1.png'
import img_7925 from '@/assets/images/vips/img_7925-1.jpg'

import zhongqilian2 from '@/assets/images/vips/zhongqilian2.png'
import zhongqilian3 from '@/assets/images/vips/zhongqilian3.png'
// import img_5081_edited1 from '@/assets/images/vips/img_5081_edited1.jpg'
export default {
  name: 'Participants',
  directives: {
    intersectionObserver: {
      mounted(el, binding) {
        const observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              binding.value(true)
            } else {
              binding.value(false)
            }
          })
        }, {
          threshold: 0.1,
          rootMargin: '0px'
        })
        observer.observe(el)
      },
      updated(el, binding) {
        const observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              binding.value(true)
            } else {
              binding.value(false)
            }
          })
        }, {
          threshold: 0.1,
          rootMargin: '0px'
        })
        observer.observe(el)
      }
    }
  },
  setup() {
    const speakersVisible = ref(false)

    const handleSpeakersIntersection = (isVisible) => {
      speakersVisible.value = isVisible
    }

    const handleImageLoad = (event) => {
      event.target.parentElement.classList.remove('image-placeholder')
    }

    // 获取嘉宾信息的辅助函数
    const getSpeakerImage = (index) => {
      const images = [
        zhongqilian2,
        zhongqilian3,
        nick_casual,
        ruggero_picture,
        celal,
        pic_of_norma_shorey,
        gergana_mantarkova,
        alan_blackman,
        re5_9160,
        img_5081_edited1,
        img_7925,
        

      ]
      return images[index % images.length]
    }

    const getSpeakerName = (index) => {
      const names = ['张教授', '李博士', '王教授']
      return names[index % names.length]
    }

    const getSpeakerTitle = (index) => {
      const titles = [
              'Zhu Hongren',
        'Li Bing',
        'Nick Warn, CMC',
        'Ruggero Huesler, CMC',
        'Celal Seckin, CMC',
        'Norma Shorey, CMC',
        'Gergana Mantarkova, CMC',
        'Alan Blackman, CMC',
        'Robert Bodenstein, CMC',
        'Reema Nasser',
        'Khuzaima Zaghlawan',
  
        
      ]
      return titles[index % titles.length]
    }

    const getSpeakerBio = (index) => {
      const bios = [
        'Executive Vice President, and Secretary General of  China Enterprise Confederation',
        'Vice President of China Enterprise Confederation',
        'United Kingdom Chair',
        'Switzerland Secretary',
        'Turkey Director',
        'Caribbean Director',
        'Bulgaria Director',
        'Australia Director',
        'Austria Immediate Past Chair',
        'Jordan Executive Director',
        'Jordan Executive Secretary',
        
        
      ]
      return bios[index % bios.length]
    }

    return {
      speakersVisible,
      handleSpeakersIntersection,
      handleImageLoad,
      getSpeakerImage,
      getSpeakerName,
      getSpeakerTitle,
      getSpeakerBio
    }
  }
}
</script>

<style scoped>
@import url('@/assets/css/participants.css');
</style> 