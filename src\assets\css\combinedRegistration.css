.conference-overview-container{
    background: url('@/assets/images/home-bg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    min-height: 100vh;
    
  }
  .total-amount {
    margin-top: 20px;
    text-align: right;
    padding: 15px;
    background-color: var(--bg-light);
    border-radius: var(--radius-sm);
  }
  
  .amount-label {
    font-size: 16px;
    color: var(--text-light);
  }
  
  .amount-value {
    font-size: 20px;
    font-weight: 700;
    color: var(--primary-color);
    margin-left: 10px;
  }
  .combined-registration-page {
    /* padding-bottom: 80px; */
  }
  
  .page-header {
    background: linear-gradient(rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.3)), url('@/assets/images/dahui.png');
    background-size: cover;
    background-position: center;
  
    height: 200px;
    text-align: center;
    margin-top: 80px;  }
  
  .page-header h1 {
    font-size: 36px;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    animation: fadeInDown 1s ease-out;
  }
  
  .page-header p {
    font-size: 18px;
    opacity: 0.9;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    animation: fadeInUp 1s ease-out;
  }
  
  @keyframes fadeInDown {
    from {
      opacity: 0;
      transform: translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .registration-form-section {
    padding: 80px 0;
  }
  
  .form-container {
    max-width: 860px;
    margin: 0 auto;
    background-color: var(--bg-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: 40px;
  }
  
  .form-container h2 {
    text-align: center;
    margin-bottom: 10px;
  }
  
  .form-desc {
    text-align: center;
    color: var(--text-light);
    margin-bottom: 30px;
  }
  
  .required {
    color: var(--error-color);
  }
  
  .form-section {
    margin-bottom: 40px;
    padding-bottom: 30px;
    border-bottom: 1px solid var(--border-color);
  }
  
  .form-section:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
  }
  
  .form-section h3 {
    font-size: 20px;
    margin-bottom: 20px;
    color: var(--primary-color);
  }
  
  .form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding-right: 20px;
    font-size: 16px;
    font-weight: 500;
  }
  
  .form-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 40px;
  }
  
  :deep(.el-collapse-item__header) {
    font-size: 16px;
    font-weight: 500;
  }
  
  :deep(.el-collapse-item__content) {
    padding: 20px;
  }
  
  :deep(.el-form-item) {
    margin-bottom: 0;
  }
  
  :deep(.el-form-item__label) {
    font-weight: 500;
    color: var(--text-color);
  }
  
  :deep(.el-input__wrapper) {
    box-shadow: 0 0 0 1px var(--border-color) inset;
  }
  
  :deep(.el-input__wrapper:hover) {
    box-shadow: 0 0 0 1px var(--primary-color) inset;
  }
  
  :deep(.el-input__wrapper.is-focus) {
    box-shadow: 0 0 0 1px var(--primary-color) inset;
  }
  
  :deep(.el-radio-group) {
    display: flex;
    gap: 20px;
  }
  
  :deep(.el-radio) {
    margin-right: 0;
  }
  
  :deep(.el-textarea__inner) {
    min-height: 100px;
    resize: vertical;
  }
  
  :deep(.el-date-editor) {
    width: 100%;
  }
  
  .form-row .form-group {
    margin-bottom: 20px;
  }
  
  @media (max-width: 768px) {
    .form-container {
      padding: 30px 20px;
    }
  
    .form-row {
      flex-direction: column;
      gap: 0;
    }
  
    .form-actions {
      flex-direction: column;
    }
  
    .btn {
      width: 100%;
    }
  }
  
  .meeting-type-group {
    display: flex;
    flex-direction: column;
    gap: 16px;
    width: 100%;
  }
  
  .meeting-type-checkbox {
    width: 100%;
    margin-right: 0 !important;
    padding: 16px 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    transition: all 0.3s ease;
  }
  
  .meeting-type-checkbox:hover {
    border-color: var(--primary-color);
    background-color: rgba(25, 118, 210, 0.04);
  }
  
  .meeting-type-checkbox.is-checked {
    border-color: var(--primary-color);
    background-color: rgba(25, 118, 210, 0.08);
  }
  
  .meeting-type-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }
  
  .meeting-type-info {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .meeting-date {
    color: var(--primary-color);
    font-weight: 500;
    font-size: 16px;
    width: 90px;
  }
  
  .meeting-label {
    font-size: 16px;
    color: #333;
  }
  
  .meeting-price {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-color);
  }
  
  :deep(.el-checkbox__input) {
    margin-right: 12px;
  }
  :deep(.el-checkbox__label) {
    width: 100%;
  }
  
  :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
  }
  
  :deep(.el-checkbox__inner) {
    width: 20px;
    height: 20px;
    border-radius: 4px;
  }
  
  :deep(.el-checkbox__inner::after) {
    height: 10px;
    width: 5px;
    left: 6px;
    top: 2px;
  }
  
  @media (max-width: 768px) {
    .meeting-type-checkbox {
      padding: 12px 16px;
    }
    
    .meeting-date {
      font-size: 14px;
      min-width: 100px;
    }
    
    .meeting-label {
      font-size: 14px;
    }
    
    .meeting-price {
      font-size: 16px;
    }
  }