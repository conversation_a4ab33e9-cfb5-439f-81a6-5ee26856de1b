<template>
  <div class="change-password">
    <h2>修改密码</h2>
    
    <el-form 
      ref="formRef"
      :model="form"
      :rules="rules"
      label-position="top"
      class="password-form"
    >
      <el-form-item label="当前密码" prop="currentPassword">
        <el-input 
          v-model="form.currentPassword" 
          type="password"
          placeholder="请输入当前密码"
          show-password
        />
      </el-form-item>
      
      <el-form-item label="新密码" prop="newPassword">
        <el-input 
          v-model="form.newPassword" 
          type="password"
          placeholder="请输入新密码"
          show-password
        />
      </el-form-item>
      
      <el-form-item label="确认新密码" prop="confirmPassword">
        <el-input 
          v-model="form.confirmPassword" 
          type="password"
          placeholder="请再次输入新密码"
          show-password
        />
      </el-form-item>
      
      <div class="form-actions">
        <el-button type="primary" @click="handleSubmit">更新密码</el-button>
        <el-button @click="handleCancel">取消</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

export default {
  name: 'ChangePassword',
  setup() {
    const formRef = ref(null)
    const form = ref({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    })

    const validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入密码'))
      } else {
        if (form.value.confirmPassword !== '') {
          formRef.value?.validateField('confirmPassword')
        }
        callback()
      }
    }

    const validatePass2 = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== form.value.newPassword) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }

    const rules = {
      currentPassword: [
        { required: true, message: '请输入当前密码', trigger: 'blur' }
      ],
      newPassword: [
        { required: true, message: '请输入新密码', trigger: 'blur' },
        { validator: validatePass, trigger: 'blur' }
      ],
      confirmPassword: [
        { required: true, message: '请再次输入新密码', trigger: 'blur' },
        { validator: validatePass2, trigger: 'blur' }
      ]
    }

    const handleSubmit = async () => {
      if (!formRef.value) return
      
      try {
        await formRef.value.validate()
        // TODO: 调用更新密码的API
        ElMessage.success('密码更新成功')
        handleCancel()
      } catch (error) {
        console.error('密码更新失败', error)
        ElMessage.error('密码更新失败，请重试')
      }
    }

    const handleCancel = () => {
      form.value = {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      }
      formRef.value?.resetFields()
    }

    return {
      formRef,
      form,
      rules,
      handleSubmit,
      handleCancel
    }
  }
}
</script>

<style scoped>
.change-password {
  max-width: 500px;
  margin: 0 auto;
}

.password-form {
  margin-top: 30px;
}

.form-actions {
  margin-top: 30px;
  text-align: center;
}
</style> 