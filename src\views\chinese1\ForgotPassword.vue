<template>
  <div class="forgot-password-page">
    <!-- 页面头部 -->
    <section class="page-header">
      <div class="container">
        <h1>重置密码</h1>
        <p>通过邮箱验证重置您的密码</p>
      </div>
    </section>
    <div class="conference-overview-container">
    <!-- 重置密码表单 -->
    <section class="reset-section">
      <div class="container">
        <div class="auth-container">
          <div class="auth-form">
            <h2>重置密码</h2>
            <p class="auth-desc">请输入您的账号和邮箱，我们将发送验证码到您的邮箱</p>
            
            <el-form
              :model="form"
              :rules="rules"
              ref="resetFormRef"
              label-width="100px"
              class="reset-form"
            >
              <el-form-item label="账号" prop="account">
                <el-input
                  v-model="form.account"
                  placeholder="请输入账号"
                />
              </el-form-item>

              <el-form-item label="新密码" prop="password">
                <el-input
                  v-model="form.password"
                  type="password"
                  placeholder="请输入新密码"
                  show-password
                />
              </el-form-item>

              <el-form-item label="邮箱" prop="email">
                <el-input
                  v-model="form.email"
                  placeholder="请输入邮箱"
                />
              </el-form-item>

              <el-form-item label="验证码" prop="emailCode">
                <div class="verification-code">
                  <el-input
                    v-model="form.emailCode"
                    placeholder="请输入验证码"
                    style="width: 60%"
                  />
                  <el-button
                    type="primary"
                    :disabled="countdown > 0"
                    @click="handleSendCode"
                    style="width: 35%; margin-left: 5%"
                  >
                    {{ countdown > 0 ? `${countdown}秒后重试` : "获取验证码" }}
                  </el-button>
                </div>
              </el-form-item>
              
              <el-form-item>
                <el-button
                  type="primary"
                  @click="handleReset"
                  :loading="isLoading"
                  style="width: 100%"
                >
                  重置密码
                </el-button>
              </el-form-item>
            </el-form>
            
            <div class="auth-footer">
              <p>想起密码了？ <router-link :to="getLocalePath('/login')">返回登录</router-link></p>
            </div>
          </div>
        </div>
      </div>
    </section>
    </div>
  </div>
</template>

<script>
import { ref, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { sendResetEmailCode, resetPassword } from '../../api/auth'
import { sm2 } from 'sm-crypto'
import { useLanguage } from '@/hooks/useLanguage'

export default {
  name: 'ForgotPasswordPage',
  setup() {
    const router = useRouter()
    const { getLocalePath } = useLanguage()
    const resetFormRef = ref(null)
    const isLoading = ref(false)
    const countdown = ref(0)
    const timer = ref(null)
    
    // SM2 公钥
    const publicKey = '042136fe42541b34a589a158382b5a2f65593c181af2b388f18745818d5a74350387e8bdd8d44828f4b94180cc4b5cd8143da5324ee9ba86a0fd7d75572a1f4e11'
    
    const form = ref({
      account: "",
      email: "",
      emailCode: "",
      password: ""
    })

    const rules = {
      account: [
        { required: true, message: '请输入账号', trigger: 'blur' },
        { min: 4, max: 20, message: '长度在 4 到 20 个字符', trigger: 'blur' }
      ],
      password: [
        { required: true, message: '请输入新密码', trigger: 'blur' },
        { min: 8, max: 20, message: '长度在 8 到 20 个字符', trigger: 'blur' }
      ],
      email: [
        { required: true, message: '请输入邮箱', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
      ],
      emailCode: [
        { required: true, message: '请输入验证码', trigger: 'blur' },
        { len: 6, message: '验证码长度为6位', trigger: 'blur' }
      ]
    }

    // 发送验证码
    const handleSendCode = async () => {
      if (!form.value.email) {
        ElMessage.warning('请先输入邮箱')
        return
      }
      
      if (countdown.value > 0) return
      
      try {
        const response = await sendResetEmailCode(form.value.email)
        
        if (response.code === 1000) {
          ElMessage.success(response.msg || '验证码已发送')
          countdown.value = 60
          timer.value = setInterval(() => {
            countdown.value--
            if (countdown.value <= 0) {
              clearInterval(timer.value)
            }
          }, 1000)
        } else {
          ElMessage.error(response.msg || '发送失败，请稍后重试')
        }
      } catch (error) {
        console.error('发送验证码失败:', error)
        ElMessage.error(error.response?.data?.message || '发送失败，请稍后重试')
      }
    }

    // 重置密码
    const handleReset = async () => {
      if (!resetFormRef.value) return

      try {
        // 验证表单
        await resetFormRef.value.validate()
        isLoading.value = true
        
        // 对密码进行加密
        const encryptedPassword = "04" + sm2.doEncrypt(form.value.password, publicKey)
        
        // 准备请求参数
        const params = {
          account: form.value.account,
          email: form.value.email,
          emailCode: form.value.emailCode,
          password: encryptedPassword
        }
        
        // 调用重置密码API
        const response = await resetPassword(params)
        
        if (response.code === 1000) {
          ElMessage.success('密码重置成功，请使用新密码登录')
          router.push(getLocalePath('/login'))
        } else {
          ElMessage.error(response.msg || '重置失败，请稍后重试')
        }
      } catch (error) {
        console.error('重置密码失败:', error)
        ElMessage.error(error.response?.data?.message || '重置失败，请稍后重试')
      } finally {
        isLoading.value = false
      }
    }

    // 组件卸载时清除定时器
    onUnmounted(() => {
      if (timer.value) {
        clearInterval(timer.value)
      }
    })
    
    return {
      form,
      rules,
      resetFormRef,
      isLoading,
      countdown,
      handleReset,
      getLocalePath,
      handleSendCode
    }
  }
}
</script>

<style scoped>
@import url('@/assets/css/forgotPassword.css');

</style> 