.conference-overview-container{
    background: url('@/assets/images/home-bg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    min-height: 100vh;
    
  }
  .home-page {
    width: 100%;
    overflow-x: hidden;
  }
  
  .hero-section {
    position: relative;
    min-height: 520px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #0a1a3a;
    overflow: hidden;
  }
  .hero-background {
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
  .hero-background .img {
    width: 100vw;
    /* min-height: 520px; */
    /* object-fit: cover; */
    /* filter: blur(2px) brightness(1); */
    pointer-events: none;
  }
  .hero-content {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: 900px;
    margin: 260px auto 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    /* padding: 60px 20px 40px 20px; */
    color: #fff;
  }
  .event-details {
    display: flex;
    gap: 40px;
    align-items: center;
    justify-content: center;
    margin-bottom: 36px;
    font-size: 20px;
    font-weight: 500;
    letter-spacing: 1px;
  }
  .event-details i {
    margin-right: 8px;
    color: #4fc3f7;
    font-size: 22px;
  }
  .event-details span {
    color: #fff;
    opacity: 0.92;
  }
  .cta-buttons {
    display: flex;
    gap: 20px;
    margin-bottom: 36px;
    justify-content: center;
  }
  .btn-primary {
    background: var(--primary-color);
    color: #fff;
    font-weight: 600;
    font-size: 20px;
    padding: 16px 48px;
    border-radius: 32px;
    border: none;
    box-shadow: 0 2px 8px rgba(25, 118, 210, 0.10);
    transition: background 0.2s, color 0.2s, box-shadow 0.2s;
  }
  .btn-primary:hover {
    background: #1565c0;
    color: #fff;
    box-shadow: 0 4px 16px rgba(25, 118, 210, 0.18);
  }
  .btn-outline {
    background: #fff;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    font-weight: 600;
    font-size: 20px;
    padding: 16px 48px;
    border-radius: 32px;
    transition: background 0.2s, color 0.2s, border 0.2s;
  }
  .btn-outline:hover {
    background: var(--primary-color);
    color: #fff;
    border-color: var(--primary-color);
  }
  /* 倒计时美化 */
  .countdown {
    display: flex;
    gap: 24px;
    margin-top: 36px;
    justify-content: center;
  }
  .countdown-card {
    background: rgba(255,255,255,0.18);
    border-radius: 18px;
    box-shadow: 0 4px 18px rgba(41,128,185,0.10);
    padding: 28px 24px 18px 24px;
    min-width: 100px;
    text-align: center;
    backdrop-filter: blur(2px);
    border: 1.5px solid rgba(255,255,255,0.18);
    position: relative;
  }
  .countdown-card strong {
    display: block;
    font-size: 38px;
    font-weight: 800;
    color: #fff;
    letter-spacing: 2px;
    margin-bottom: 6px;
    text-shadow: 0 2px 8px rgba(41,128,185,0.18);
  }
  .countdown-card span {
    font-size: 16px;
    color: #e3f2fd;
    letter-spacing: 1px;
  }
  @media (max-width: 768px) {
    .hero-content {
      padding: 40px 10px 20px 10px;
    }
    .event-details {
      flex-direction: column;
      gap: 12px;
      font-size: 16px;
    }
    .cta-buttons {
      flex-direction: column;
      gap: 16px;
    }
    .btn-primary, .btn-outline {
      width: 100%;
      font-size: 16px;
      padding: 14px 0;
    }
    .countdown {
      gap: 10px;
    }
    .countdown-card {
      min-width: 60px;
      padding: 16px 8px 10px 8px;
    }
    .countdown-card strong {
      font-size: 22px;
    }
    .countdown-card span {
      font-size: 12px;
    }
  }
  /* 防止动画元素横向溢出 */
  .overview-card,
  .theme-content h3,
  .theme-content p,
  .forum-item,
  .venue-slider,
  .venue-info,
  .register-card {
    max-width: 100%;
    box-sizing: border-box;
    overflow-x: hidden;
  }
  
  .btn-primary {
    white-space: nowrap;
    overflow: hidden;
  }
  
  /* 修改动画相关样式 */
  .overview-card,
  .theme-content h3,
  .theme-content p,
  .forum-item,
  .venue-slider,
  .venue-info
  {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.3s ease, transform 0.3s ease;
  }
  
  /* 优化动画效果 */
  .animate__animated {
    animation-duration: 1s;
    animation-fill-mode: forwards !important; /* 确保动画完成后保持最终状态 */
    animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  /* 确保动画类被移除后元素恢复初始状态 */
  .overview-card:not(.animate__animated),
  .theme-content h3:not(.animate__animated),
  .theme-content p:not(.animate__animated),
  .forum-item:not(.animate__animated),
  .venue-slider:not(.animate__animated),
  .venue-info:not(.animate__animated)
  {
    transform: translateY(20px);
  }
  
  /* 添加动画类 */
  .animate__fadeInDown,
  .animate__fadeInUp,
  .animate__fadeInLeft,
  .animate__fadeInRight {
    animation-fill-mode: forwards !important;
  }
  
  /* 会议概览样式 */
  .section-title {
    font-size: 32px;
    text-align: center;
    margin-bottom: 40px;
    color: var(--primary-color);
    position: relative;
    padding-bottom: 15px;
  }
  
  .section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: var(--primary-color);
  }
  
  .overview-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }
  
  .overview-card {
    background-color: var(--bg-color);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
    padding: 30px;
    transition: var(--transition);
  }
  
  .overview-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
  }
  
  .card-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    margin-bottom: 20px;
  }
  
  .card-icon img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .overview-card h3 {
    font-size: 22px;
    margin-bottom: 10px;
    color: var(--primary-color);
  }
  
  .overview-card p {
    font-weight: 500;
    margin-bottom: 15px;
    color: var(--text-light);
  }
  
  .overview-card ul {
    margin-bottom: 20px;
    padding-left: 20px;
  }
  
  .overview-card li {
    margin-bottom: 10px;
    color: var(--text-color);
  }
  
  .read-more {
    display: inline-block;
    color: var(--primary-color);
    font-weight: 500;
    position: relative;
  }
  
  .read-more::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
  }
  
  .read-more:hover::after {
    width: 100%;
  }
  
  /* 会场展示样式 */
  .venue-section {
    padding: 80px 0;
    /* background-color: var(--bg-light); */
  }
  
  .venue-slider {
    position: relative;
    margin-bottom: 40px;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
  }
  
  .slider-container {
    position: relative;
    height: 500px;
  }
  
  .slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s ease;
  }
  .conference-overview{
    padding:40px 0;
  }
  .slide.active {
    opacity: 1;
  }
  
  .slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .slider-controls {
    position: absolute;
    bottom: 20px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
  }
  
  .prev-btn, .next-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.7);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .prev-btn:hover, .next-btn:hover {
    background-color: var(--primary-color);
    color: var(--text-white);
  }
  
  .slider-dots {
    display: flex;
    gap: 10px;
  }
  
  .dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .dot.active {
    background-color: var(--primary-color);
  }
  
  .venue-info {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
  }
  
  .venue-info h3 {
    font-size: 24px;
    margin-bottom: 15px;
    color: var(--primary-color);
  }
  
  .venue-info p {
    margin-bottom: 30px;
    color: var(--text-light);
  }
  
  .venue-details {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 30px;
    margin-bottom: 30px;
  }
  
  .detail-item {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--text-light);
  }
  
  .detail-item i {
    color: var(--primary-color);
  }
  
  /* 快速报名样式 */
  .quick-register {
    padding: 80px 0;
  }
  
  .register-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1565c0 100%);
    border-radius: var(--radius-lg);
    padding: 60px;
    text-align: center;
    color: var(--text-white);
    box-shadow: 0 10px 30px rgba(25, 118, 210, 0.2);
    position: relative;
    overflow: hidden;
  }
  
  .register-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      45deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0) 100%
    );
    z-index: 1;
    pointer-events: none;
  }
  
  .register-card h2 {
    font-size: 32px;
    margin-bottom: 20px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 2;
  }
  
  .register-card p {
    font-size: 18px;
    margin-bottom: 40px;
    opacity: 0.9;
    position: relative;
    z-index: 2;
  }
  
  .btn-large {
    font-size: 18px;
    padding: 20px 50px;
  }
  
  .btn-content {
    display: flex;
    align-items: center;
    gap: 12px;
    position: relative;
    z-index: 1;
  }
  
  .btn-content i {
    transition: transform 0.3s ease;
  }
  
  .btn-primary:hover .btn-content i:first-child {
    transform: translateX(-5px);
  }
  
  .btn-primary:hover .btn-content i:last-child {
    transform: translateX(5px);
  }
  
  /* 主题介绍样式 */
  .theme-section {
    padding: 80px 0;
    position: relative;
    overflow: hidden;
    color: white;
  
  }
  
  .theme-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  
    z-index: 1;
  }
  
  .theme-content {
    position: relative;
    z-index: 3;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }
  
  .theme-content h3 {
    font-size: 36px;
    margin-bottom: 30px;
    color: rgba(0, 0, 0, 0.8);
    /* text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); */
    position: relative;
    display: inline-block;
    padding-bottom: 15px;
  }
  
  .theme-content h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, #3498db, #9b59b6);
    border-radius: 2px;
  }
  
  .theme-content p {
    font-size: 22px;
    line-height: 1.8;
    color: rgba(0, 0, 0, 0.8);
    margin-bottom: 50px;
    max-width: 800px;
    /* text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); */
  }
  
  .forums-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 30px;
    padding: 0 20px;
  }
  
  .forum-item {

    border-radius: 16px;
    padding: 30px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    /* box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); */
    height: auto;
    min-height: 120px;
    display: flex;
    flex-direction: column;
  }
  
  .forum-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    /* background: linear-gradient(45deg, rgba(52, 152, 219, 0.05), rgba(155, 89, 182, 0.05)); */
    z-index: 1;
  }
  
  .forum-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
  }
  
  .forum-content {
    position: relative;
    z-index: 2;
    height: 100%;
    display: flex;
    flex-direction: column;
    /* justify-content: space-between; */
    gap: 10px;
  }
  
  .forum-number {
    font-size: 32px;
    font-weight: 700;
    color: #3498db;
    margin-bottom: 15px;
    display: block;
    line-height: 1;
  }
  
  .forum-item h4 {
    font-size: 22px;
    color: #2c3e50;
    margin-bottom: 15px;
    line-height: 1.4;
    font-weight: 600;
  }
  
  .forum-item p {
    color: #34495e;
    margin-bottom: 20px;
    font-size: 15px;
    line-height: 1.6;
    flex-grow: 1;
  }
  
  .forum-price {
    background: rgba(255, 255, 255, 0.15);
    padding: 15px;
    border-radius: 12px;
    /* margin-bottom: 20px; */
    backdrop-filter: blur(5px);
  }
  
  .price-label {
    display: block;
    color: #000;
    font-size: 14px;
    margin-bottom: 5px;
    text-transform: uppercase;
    letter-spacing: 1px;
  }
  
  .price-value {
    color: #f1c40f;
    font-size: 24px;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
  
  .forum-link {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #ffffff;
    text-decoration: none;
    padding: 15px 20px;
    border-radius: 8px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.15);
    font-size: 15px;
    font-weight: 500;
    margin-top: auto;
  }
  
  .forum-link:hover {
    background: rgba(255, 255, 255, 0.25);
    color: #f1c40f;
    transform: translateY(-2px);
  }
  
  .forum-link i {
    transition: transform 0.3s ease;
  }
  
  .forum-link:hover i {
    transform: translateX(5px);
  }
  
  @media (max-width: 768px) {
    .theme-section {
      padding: 40px 0;
    }
    
    .theme-content h3 {
      font-size: 24px;
    }
    
    .theme-content p {
      font-size: 14px;
      margin-bottom: 20px;
      color: rgba(0, 0, 0, 0.8);
    }
    
    .forums-grid {
      grid-template-columns: 1fr;
    }
    
    .forum-item {
      aspect-ratio: auto;
      min-height: 280px;
    }
    
    .forum-item h4 {
      font-size: 18px;
    }
    
    .forum-item p {
      font-size: 13px;
    }
  }
  @media (max-width: 992px) {
    .overview-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  
    .slider-container {
      height: 400px;
    }
  
    .venue-details {
      flex-direction: column;
      align-items: center;
      gap: 15px;
    }
  }
  
  @media (max-width: 768px) {
    .register-card {
      padding: 30px 20px;
    }
  
    .register-card h2 {
      font-size: 24px;
    }
  }
  
  @media (max-width: 576px) {
    .overview-grid {
      grid-template-columns: 1fr;
    }
  
    .slider-container {
      height: 300px;
    }
  }
  
  /* 添加按钮 hover 样式 */
  .btn {
    transition: all 0.3s ease;
  }
  
  /* 优化报名按钮样式：白底蓝字，悬停蓝底白字 */
  .btn-primary {
    background: #fff;
    color: var(--primary-color);
    font-weight: 700;
    letter-spacing: 1px;
    text-transform: uppercase;
    padding: 16px 40px;
    border-radius: 50px;
    box-shadow: 0 4px 15px rgba(25, 118, 210, 0.15);
    border: 2px solid var(--primary-color);
    transition: all 0.3s cubic-bezier(0.4,0,0.2,1);
    position: relative;
    overflow: hidden;
    animation: buttonPulse 2s infinite;
    z-index: 1;
  }
  
  .btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--primary-color);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4,0,0.2,1);
    z-index: -1;
  }
  
  .btn-primary:hover {
    color: #fff;
    border-color: var(--primary-color);
    box-shadow: 0 8px 25px rgba(25, 118, 210, 0.25);
  }
  
  .btn-primary:hover::before {
    opacity: 1;
  }
  
  .btn-primary:active {
    transform: translateY(1px);
    box-shadow: 0 2px 10px rgba(25, 118, 210, 0.2);
  }
  
  @keyframes buttonPulse {
    0% {
      transform: scale(1);
      box-shadow: 0 4px 15px rgba(25, 118, 210, 0.3);
    }
    50% {
      transform: scale(1.05);
      box-shadow: 0 4px 20px rgba(25, 118, 210, 0.4);
    }
    100% {
      transform: scale(1);
      box-shadow: 0 4px 15px rgba(25, 118, 210, 0.3);
    }
  }
  
  .btn-outline {
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
  }
  
  .btn-outline:hover {
    background-color: var(--primary-color);
    color: #fff;
  }
  
  /* 确保链接按钮样式正确 */
  .btn.btn-primary,
  .btn.btn-outline {
    text-decoration: none;
    display: inline-block;
    text-align: center;
  }
  
  /* 优化跳动动画 */
  @keyframes pulse {
    0% {
      transform: scale(1);
      box-shadow: 0 4px 15px rgba(25, 118, 210, 0.3);
    }
    50% {
      transform: scale(1.05);
      box-shadow: 0 8px 25px rgba(25, 118, 210, 0.4);
    }
    100% {
      transform: scale(1);
      box-shadow: 0 4px 15px rgba(25, 118, 210, 0.3);
    }
  }
  
  .animate__pulse {
    animation-name: pulse !important;
    animation-duration: 2s !important;
    animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
  }
  
  .venue-link {
    display: inline-block;
    margin-top: 20px;
    padding: 12px 24px;
    background: linear-gradient(135deg, var(--primary-color) 0%, #1565c0 100%);
    color: #fff;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(25, 118, 210, 0.2);
    animation: buttonPulse 2s infinite;
    white-space: nowrap;
    transform-origin: center;
  }
  
  @keyframes buttonPulse {
    0% {
      transform: scale(1);
      box-shadow: 0 4px 15px rgba(25, 118, 210, 0.2);
    }
    50% {
      transform: scale(1.02);
      box-shadow: 0 8px 25px rgba(25, 118, 210, 0.3);
    }
    100% {
      transform: scale(1);
      box-shadow: 0 4px 15px rgba(25, 118, 210, 0.2);
    }
  }
  
  .link-content {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .link-content i {
    font-size: 16px;
    transition: transform 0.3s ease;
  }
  
  .venue-link:hover .link-content i:last-child {
    transform: translateX(4px);
  }
  
  @media (max-width: 768px) {
    .venue-link {
      padding: 10px 20px;
      font-size: 14px;
    }
    
    .link-content i {
      font-size: 14px;
    }
  }
  