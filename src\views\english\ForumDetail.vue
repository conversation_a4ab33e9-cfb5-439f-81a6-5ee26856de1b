<template>
  <div class="forum-detail-page">
    <!-- 论坛详情头部 -->
    <section class="forum-detail-header">
      <div class="container">
        <div class="forum-info">
          <div class="forum-number">{{ forum.number }}</div>
          <h1>{{ forum.title }}</h1>
          <p class="subtitle">{{ forum.subtitle }}</p>
          
          <div class="forum-meta">
            <div class="meta-item">
              <i class="fas fa-calendar"></i>
              <span>{{ forum.date }}</span>
            </div>
            <div class="meta-item">
              <i class="fas fa-map-marker-alt"></i>
              <span>{{ forum.location }}</span>
            </div>
            <div class="meta-item">
              <i class="fas fa-ticket-alt"></i>
              <span>{{ forum.price }}</span>
            </div>
          </div>
          
          <div class="forum-actions">
            <router-link to="/registration" class="btn btn-primary">立即报名</router-link>
            <button class="btn btn-outline" id="addToCalendar" @click="addToCalendar">
              <i class="fas fa-calendar-plus"></i> 添加到日历
            </button>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 论坛详情内容 -->
    <section class="forum-detail-section">
      <div class="container">
        <div class="forum-content">
          <!-- 论坛简介 -->
          <div class="content-section">
            <h2>论坛简介</h2>
            <p>{{ forum.description }}</p>
          </div>
          
          <!-- 主要议题 -->
          <div class="content-section">
            <h2>主要议题</h2>
            <ul class="topic-list">
              <li v-for="(topic, index) in forum.topics" :key="index">{{ topic }}</li>
            </ul>
          </div>
          
          <!-- 演讲嘉宾 -->
          <div class="content-section">
            <h2>演讲嘉宾</h2>
            <div class="speakers-grid">
              <div class="speaker-card" v-for="(speaker, index) in forum.speakers" :key="index">
                <div class="speaker-image image-container image-placeholder">
                  <img 
                    :src="speaker.avatar" 
                    :alt="speaker.name"
                    loading="lazy"
                    @load="$event.target.parentElement.classList.remove('image-placeholder')"
                  >
                </div>
                <div class="speaker-info">
                  <h3>{{ speaker.name }}</h3>
                  <p class="speaker-title">{{ speaker.title }}</p>
                  <p class="speaker-company">{{ speaker.company }}</p>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 参会对象 -->
          <div class="content-section">
            <h2>参会对象</h2>
            <ul class="audience-list">
              <li v-for="(audience, index) in forum.audience" :key="index">{{ audience }}</li>
            </ul>
          </div>
          
          <!-- 报名信息 -->
          <div class="content-section">
            <h2>报名信息</h2>
            <div class="registration-info">
              <div class="info-item">
                <h3>报名费用</h3>
                <p>{{ forum.price }}</p>
                <p class="note">* 费用包含会议资料、午餐和茶歇</p>
              </div>
              <div class="info-item">
                <h3>报名方式</h3>
                <p>1. 在线报名：点击"立即报名"按钮完成注册</p>
                <p>2. 电话报名：+86 10 8888 8888</p>
                <p>3. 邮件报名：<EMAIL></p>
              </div>
            </div>
            <div class="registration-cta">
              <router-link to="/registration" class="btn btn-primary">立即报名</router-link>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 相关论坛 -->
    <section class="related-forums">
      <div class="container">
        <h2 class="section-title">相关论坛</h2>
        <div class="forums-grid">
          <ForumCard v-for="relatedForum in relatedForums" :key="relatedForum.id" :forum="relatedForum" />
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'

import ForumCard from '@/components/common/ForumCard.vue'

export default {
  name: 'ForumDetailPage',
  components: {
    ForumCard
  },
  setup() {
    const route = useRoute()
    const forum = ref({
      id: '',
      number: '',
      title: '',
      subtitle: '',
      date: '',
      location: '',
      price: '',
      description: '',
      topics: [],
      speakers: [],
      audience: []
    })
    const allForums = ref([])
    
    const relatedForums = computed(() => {
      return allForums.value
        .filter(f => f.id !== forum.value.id)
        .slice(0, 2)
    })
    
    const loadForumData = async () => {
     
    }
    
    const addToCalendar = () => {
      // 从论坛日期中提取日期和时间
      const dateTimeStr = forum.value.date
      let startTime = '14:00:00'
      let endTime = '17:00:00'
      
      // 尝试从日期字符串中提取时间
      const timeMatch = dateTimeStr.match(/(\d{1,2}:\d{2})-(\d{1,2}:\d{2})/)
      if (timeMatch) {
        startTime = timeMatch[1] + ':00'
        endTime = timeMatch[2] + ':00'
      }
      
      // 提取日期
      const dateMatch = dateTimeStr.match(/(\d{4})年(\d{1,2})月(\d{1,2})日/)
      let dateStr = '2025-10-23'
      
      if (dateMatch) {
        const year = dateMatch[1]
        const month = dateMatch[2].padStart(2, '0')
        const day = dateMatch[3].padStart(2, '0')
        dateStr = `${year}-${month}-${day}`
      }
      
      // 创建日历事件
      const event = {
        title: forum.value.title,
        description: forum.value.subtitle,
        location: forum.value.location,
        startTime: `${dateStr}T${startTime}`,
        endTime: `${dateStr}T${endTime}`
      }
      
      // 生成ICS文件内容
      const icsContent = [
        'BEGIN:VCALENDAR',
        'VERSION:2.0',
        'PRODID:-//ICMCI 2025//CN',
        'BEGIN:VEVENT',
        `DTSTART:${formatDateForICS(event.startTime)}`,
        `DTEND:${formatDateForICS(event.endTime)}`,
        `SUMMARY:${event.title}`,
        `DESCRIPTION:${event.description}`,
        `LOCATION:${event.location}`,
        'END:VEVENT',
        'END:VCALENDAR'
      ].join('\r\n')
      
      // 创建下载链接
      const blob = new Blob([icsContent], { type: 'text/calendar;charset=utf-8' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = 'ICMCI-2025-Forum.ics'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
    }
    
    // 日期格式化函数
    const formatDateForICS = (dateTimeStr) => {
      const date = new Date(dateTimeStr)
      return date.getFullYear() +
        ('0' + (date.getMonth() + 1)).slice(-2) +
        ('0' + date.getDate()).slice(-2) +
        'T' +
        ('0' + date.getHours()).slice(-2) +
        ('0' + date.getMinutes()).slice(-2) +
        '00'
    }
    
    onMounted(() => {
      loadForumData()
    })
    
    return {
      forum,
      relatedForums,
      addToCalendar
    }
  }
}
</script>

<style scoped>
.forum-detail-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, #1565c0 100%);
  color: var(--text-white);
  padding: 150px 0 60px;
}

.forum-info {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
}

.forum-number {
  position: absolute;
  top: -30px;
  right: 0;
  font-size: 120px;
  font-weight: 700;
  opacity: 0.1;
  line-height: 1;
}

.forum-info h1 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
}

.subtitle {
  font-size: 1.25rem;
  opacity: 0.9;
  margin-bottom: 2rem;
}

.forum-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  margin-bottom: 2rem;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.forum-actions {
  display: flex;
  gap: 1rem;
}

.forum-detail-section {
  padding: 4rem 0;
  background: #f8f9fa;
}

.forum-content {
  max-width: 800px;
  margin: 0 auto;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  padding: 3rem;
}

.content-section {
  margin-bottom: 3rem;
}

.content-section:last-child {
  margin-bottom: 0;
}

.content-section h2 {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  position: relative;
  padding-bottom: 0.75rem;
}

.content-section h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 3px;
  background-color: var(--primary-color);
}

.topic-list, .audience-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.topic-list li, .audience-list li {
  position: relative;
  padding-left: 1.5rem;
  margin-bottom: 0.75rem;
}

.topic-list li::before, .audience-list li::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0.5rem;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--primary-color);
}

.speakers-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
}

.speaker-card {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.speaker-image {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
}

.speaker-info h3 {
  font-size: 1.1rem;
  margin-bottom: 0.25rem;
}

.speaker-title, .speaker-company {
  font-size: 0.9rem;
  color: var(--text-light);
}

.registration-info {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
}

.info-item h3 {
  font-size: 1.1rem;
  margin-bottom: 0.75rem;
  color: var(--primary-color);
}

.note {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-top: 0.5rem;
}

.registration-cta {
  text-align: center;
  margin-top: 2rem;
}

.related-forums {
  padding: 4rem 0;
}

.forums-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
  margin-top: 2rem;
}

@media (max-width: 768px) {
  .forum-detail-header {
    padding: 120px 0 40px;
  }
  
  .forum-info h1 {
    font-size: 2rem;
  }
  
  .forum-meta {
    flex-direction: column;
    gap: 1rem;
  }
  
  .forum-content {
    padding: 2rem;
  }
  
  .content-section h2 {
    font-size: 1.5rem;
  }
  
  .speakers-grid {
    grid-template-columns: 1fr;
  }
  
  .registration-info {
    grid-template-columns: 1fr;
  }
  
  .forums-grid {
    grid-template-columns: 1fr;
  }
}
</style>
