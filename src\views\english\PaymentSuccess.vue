<template>
  <div class="conference-overview-container"> 
  <div class="payment-success-page">
    <!-- 成功信息 -->
    <section class="success-section">
      <div class="container">
        <div class="success-container">
          <div class="success-icon">
            <i class="fas fa-check-circle"></i>
          </div>
          <h1>支付成功</h1>
          <p class="success-message">感谢您报名参加ICMCI 2025上海国际管理咨询大会</p>
<!--           
          <div class="order-info">
            <div class="info-item">
              <div class="info-label">订单编号</div>
              <div class="info-value">ICMCI2025-12345</div>
            </div>
            <div class="info-item">
              <div class="info-label">支付金额</div>
              <div class="info-value">¥2,800.00</div>
            </div>
            <div class="info-item">
              <div class="info-label">支付时间</div>
              <div class="info-value">2025-05-15 15:30:45</div>
            </div>
          </div> -->
          
          <div class="next-steps">
            <h2>后续步骤</h2>
            <div class="steps-grid">
              <div class="step-item">
                <div class="step-icon">
                  <i class="fas fa-envelope"></i>
                </div>
                <div class="step-content">
                  <h3>确认邮件</h3>
                  <p>我们已向您的注册邮箱发送了确认邮件，请查收。</p>
                </div>
              </div>
              <div class="step-item">
                <div class="step-icon">
                  <i class="fas fa-ticket-alt"></i>
                </div>
                <div class="step-content">
                  <h3>电子票</h3>
                  <p>您可以在用户中心下载电子票，或通过确认邮件中的链接获取。</p>
                </div>
              </div>
              <div class="step-item">
                <div class="step-icon">
                  <i class="fas fa-file-invoice"></i>
                </div>
                <div class="step-content">
                  <h3>电子发票</h3>
                  <p>我们将在3个工作日内开具电子发票，并发送至您的注册邮箱。</p>
                </div>
              </div>
              <div class="step-item">
                <div class="step-icon">
                  <i class="fas fa-hotel"></i>
                </div>
                <div class="step-content">
                  <h3>住宿安排</h3>
                  <p>您可以通过用户中心预订会议酒店，享受优惠价格。</p>
                </div>
              </div>
            </div>
          </div>
          
          <div class="action-buttons">
            
            <button class="btn btn-primary" @click="dingHotel">
              订酒店
            </button>
            <button class="btn btn-primary" @click="$router.push(getLocalePath('/dashboard'))">
              进入用户中心
            </button>
            <button class="btn btn-outline"  @click="$router.push(getLocalePath('home'))">
              返回首页
            </button>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 分享信息 -->
    <!-- <section class="share-section">
      <div class="container">
        <div class="share-container">
          <h2>分享会议信息</h2>
          <p>邀请您的同事和朋友一起参加ICMCI 2025上海国际管理咨询大会</p>
          
          <div class="share-buttons">
            <button class="share-button wechat">
              <i class="fab fa-weixin"></i>
              <span>微信</span>
            </button>
            <button class="share-button weibo">
              <i class="fab fa-weibo"></i>
              <span>微博</span>
            </button>
            <button class="share-button linkedin">
              <i class="fab fa-linkedin-in"></i>
              <span>领英</span>
            </button>
            <button class="share-button email">
              <i class="fas fa-envelope"></i>
              <span>邮件</span>
            </button>
          </div>
          
          <div class="share-link">
            <input type="text" value="https://icmci2025shanghai.org" readonly>
            <button class="copy-button">复制链接</button>
          </div>
        </div>
      </div>
    </section> -->
  </div>
  </div>
</template>

<script >
import { onMounted } from 'vue'
import { useLanguage } from '@/hooks/useLanguage'
const { getLocalePath } = useLanguage()
export default {
  name: 'PaymentSuccessPage',
  setup() {
    const { getLocalePath } = useLanguage()
    onMounted(()=>{
      localStorage.removeItem('registrationData')
    })
    const dingHotel =() =>{
      alert('订酒店')
    }
    return {
      getLocalePath,
      dingHotel
    }
  }
}
</script>

<style scoped>
@import url('@/assets/css/paymentSuccess.css');
</style>
