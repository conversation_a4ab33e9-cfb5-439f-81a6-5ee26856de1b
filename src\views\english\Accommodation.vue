<template>
  <div class="accommodation-page">
    <!-- 页面头部 -->
    <section class="page-header">
      <div class="container">
        <h1>住宿信息</h1>
        <p>为您提供舒适便捷的住宿选择</p>
      </div>
    </section>
    
    <!-- 主会场酒店 -->
    <section class="main-hotel-section">
      <div class="container">
        <SectionTitle title="主会场酒店" />
        
        <div class="hotel-card">
          <div class="hotel-image">
            <img src="https://picsum.photos/1000/600?random=26" alt="Pudong Shangri-La, Shanghai">
          </div>
          <div class="hotel-info">
            <h2>Pudong Shangri-La, Shanghai</h2>
            <div class="hotel-meta">
              <div class="meta-item">
                <i class="fas fa-map-marker-alt"></i>
                <span>上海市浦东新区陆家嘴世纪大道33号</span>
              </div>
              <div class="meta-item">
                <i class="fas fa-star"></i>
                <span>五星级</span>
              </div>
              <div class="meta-item">
                <i class="fas fa-walking"></i>
                <span>会场内</span>
              </div>
            </div>
            <p class="hotel-desc">Pudong Shangri-La, Shanghai是本次大会的主会场，位于上海金融中心陆家嘴，毗邻东方明珠电视塔和上海中心，交通便利，设施完善。酒店拥有豪华客房、多功能会议厅和各类餐厅，为参会代表提供舒适便捷的住宿和会议体验。</p>
            
            <div class="room-types">
              <h3>房型与价格</h3>
              <div class="room-grid">
                <div class="room-item">
                  <div class="room-name">豪华客房</div>
                  <div class="room-price">¥1,200/晚</div>
                  <div class="room-features">
                    <span>40平米</span>
                    <span>大床/双床</span>
                    <span>含早餐</span>
                  </div>
                </div>
                <div class="room-item">
                  <div class="room-name">行政客房</div>
                  <div class="room-price">¥1,500/晚</div>
                  <div class="room-features">
                    <span>45平米</span>
                    <span>大床/双床</span>
                    <span>含早餐</span>
                    <span>行政礼遇</span>
                  </div>
                </div>
                <div class="room-item">
                  <div class="room-name">套房</div>
                  <div class="room-price">¥2,200/晚</div>
                  <div class="room-features">
                    <span>65平米</span>
                    <span>大床</span>
                    <span>含早餐</span>
                    <span>行政礼遇</span>
                  </div>
                </div>
              </div>
              <p class="room-note">* 以上价格为会议协议价格，含税及服务费，有效期：2025年10月20-26日</p>
            </div>
            
            <div class="booking-info">
              <h3>预订方式</h3>
              <p>1. 通过会议注册系统预订（推荐）</p>
              <p>2. 发送邮件至：<EMAIL>，邮件主题注明"ICMCI2025"</p>
              <p>3. 电话预订：+86 21 6882 8888，并告知参加"ICMCI2025国际管理咨询大会"</p>
            </div>
            
            <div class="hotel-actions">
              <a href="#" class="btn btn-primary">在线预订</a>
              <a href="https://maps.google.com/?q=上海市浦东新区陆家嘴世纪大道33号" target="_blank" class="btn btn-outline">查看地图</a>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 推荐酒店 -->
    <section class="recommended-hotels-section">
      <div class="container">
        <SectionTitle title="推荐酒店" />
        
        <div class="hotels-grid">
          <div class="hotel-card-small">
            <div class="hotel-image">
              <img src="https://picsum.photos/500/300?random=27" alt="上海金茂君悦大酒店">
            </div>
            <div class="hotel-info">
              <h3>上海金茂君悦大酒店</h3>
              <div class="hotel-meta">
                <div class="meta-item">
                  <i class="fas fa-map-marker-alt"></i>
                  <span>浦东新区世纪大道88号</span>
                </div>
                <div class="meta-item">
                  <i class="fas fa-car"></i>
                  <span>距会场5分钟车程</span>
                </div>
              </div>
              <div class="hotel-price">¥1,000/晚起</div>
              <div class="hotel-actions">
                <a href="#" class="btn btn-sm btn-primary">预订</a>
                <a href="#" class="btn btn-sm btn-outline">详情</a>
              </div>
            </div>
          </div>
          
          <div class="hotel-card-small">
            <div class="hotel-image">
              <img src="https://picsum.photos/500/300?random=28" alt="上海浦东丽思卡尔顿酒店">
            </div>
            <div class="hotel-info">
              <h3>上海浦东丽思卡尔顿酒店</h3>
              <div class="hotel-meta">
                <div class="meta-item">
                  <i class="fas fa-map-marker-alt"></i>
                  <span>浦东新区陆家嘴世纪大道8号</span>
                </div>
                <div class="meta-item">
                  <i class="fas fa-car"></i>
                  <span>距会场10分钟车程</span>
                </div>
              </div>
              <div class="hotel-price">¥1,200/晚起</div>
              <div class="hotel-actions">
                <a href="#" class="btn btn-sm btn-primary">预订</a>
                <a href="#" class="btn btn-sm btn-outline">详情</a>
              </div>
            </div>
          </div>
          
          <div class="hotel-card-small">
            <div class="hotel-image">
              <img src="https://picsum.photos/500/300?random=29" alt="上海浦东四季酒店">
            </div>
            <div class="hotel-info">
              <h3>上海浦东四季酒店</h3>
              <div class="hotel-meta">
                <div class="meta-item">
                  <i class="fas fa-map-marker-alt"></i>
                  <span>浦东新区世纪大道210号</span>
                </div>
                <div class="meta-item">
                  <i class="fas fa-car"></i>
                  <span>距会场15分钟车程</span>
                </div>
              </div>
              <div class="hotel-price">¥1,300/晚起</div>
              <div class="hotel-actions">
                <a href="#" class="btn btn-sm btn-primary">预订</a>
                <a href="#" class="btn btn-sm btn-outline">详情</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 交通信息 -->
    <section class="transportation-section">
      <div class="container">
        <SectionTitle title="交通信息" />
        
        <div class="transportation-grid">
          <div class="transportation-item">
            <div class="transportation-icon">
              <i class="fas fa-plane"></i>
            </div>
            <h3>机场交通</h3>
            <div class="transportation-content">
              <h4>浦东国际机场</h4>
              <p>距离会场约40公里，乘坐出租车约40分钟，费用约150元。</p>
              <p>乘坐地铁2号线至陆家嘴站下车，步行10分钟可达会场。</p>
              
              <h4>虹桥国际机场</h4>
              <p>距离会场约20公里，乘坐出租车约30分钟，费用约100元。</p>
              <p>乘坐地铁2号线至陆家嘴站下车，步行10分钟可达会场。</p>
            </div>
          </div>
          
          <div class="transportation-item">
            <div class="transportation-icon">
              <i class="fas fa-train"></i>
            </div>
            <h3>火车站交通</h3>
            <div class="transportation-content">
              <h4>上海虹桥火车站</h4>
              <p>距离会场约20公里，乘坐出租车约30分钟，费用约100元。</p>
              <p>乘坐地铁2号线至陆家嘴站下车，步行10分钟可达会场。</p>
              
              <h4>上海火车站</h4>
              <p>距离会场约15公里，乘坐出租车约25分钟，费用约80元。</p>
              <p>乘坐地铁1号线至人民广场站换乘2号线至陆家嘴站下车，步行10分钟可达会场。</p>
            </div>
          </div>
          
          <div class="transportation-item">
            <div class="transportation-icon">
              <i class="fas fa-subway"></i>
            </div>
            <h3>公共交通</h3>
            <div class="transportation-content">
              <h4>地铁</h4>
              <p>乘坐地铁2号线至陆家嘴站下车，从4号出口出站，步行10分钟可达会场。</p>
              
              <h4>公交</h4>
              <p>乘坐81路、82路、85路、陆家嘴环线至陆家嘴站下车，步行5分钟可达会场。</p>
            </div>
          </div>
          
          <div class="transportation-item">
            <div class="transportation-icon">
              <i class="fas fa-car"></i>
            </div>
            <h3>自驾信息</h3>
            <div class="transportation-content">
              <p>导航至"Pudong Shangri-La, Shanghai"，地址：上海市浦东新区陆家嘴世纪大道33号。</p>
              <p>酒店设有地下停车场，收费标准：20元/小时，最高150元/天。</p>
              <p>建议提前预约酒店停车位，电话：+86 21 6882 8888。</p>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 周边信息 -->
    <section class="surroundings-section">
      <div class="container">
        <SectionTitle title="周边信息" />
        
        <div class="surroundings-tabs">
          <div 
            class="tab-item" 
            :class="{ 'active': activeTab === 'attractions' }"
            @click="activeTab = 'attractions'"
          >
            景点
          </div>
          <div 
            class="tab-item" 
            :class="{ 'active': activeTab === 'dining' }"
            @click="activeTab = 'dining'"
          >
            餐饮
          </div>
          <div 
            class="tab-item" 
            :class="{ 'active': activeTab === 'shopping' }"
            @click="activeTab = 'shopping'"
          >
            购物
          </div>
        </div>
        
        <div class="surroundings-content">
          <div v-if="activeTab === 'attractions'" class="tab-content">
            <div class="surroundings-grid">
              <div class="surroundings-item">
                <div class="surroundings-image">
                  <img src="https://picsum.photos/500/300?random=30" alt="东方明珠">
                </div>
                <div class="surroundings-info">
                  <h3>东方明珠电视塔</h3>
                  <p>距离会场步行10分钟</p>
                  <p>上海地标性建筑，提供城市全景观赏。</p>
                </div>
              </div>
              
              <div class="surroundings-item">
                <div class="surroundings-image">
                  <img src="https://picsum.photos/500/300?random=31" alt="上海中心">
                </div>
                <div class="surroundings-info">
                  <h3>上海中心</h3>
                  <p>距离会场步行15分钟</p>
                  <p>中国第一高楼，118层观光厅可俯瞰全城。</p>
                </div>
              </div>
              
              <div class="surroundings-item">
                <div class="surroundings-image">
                  <img src="https://picsum.photos/500/300?random=32" alt="上海科技馆">
                </div>
                <div class="surroundings-info">
                  <h3>上海科技馆</h3>
                  <p>距离会场15分钟车程</p>
                  <p>亚洲最大的科技馆之一，展示科技发展历程。</p>
                </div>
              </div>
            </div>
          </div>
          
          <div v-if="activeTab === 'dining'" class="tab-content">
            <div class="surroundings-grid">
              <div class="surroundings-item">
                <div class="surroundings-image">
                  <img src="https://picsum.photos/500/300?random=33" alt="南外滩餐厅">
                </div>
                <div class="surroundings-info">
                  <h3>南外滩餐厅</h3>
                  <p>距离会场步行5分钟</p>
                  <p>提供精致中西融合料理，可欣赏黄浦江美景。</p>
                </div>
              </div>
              
              <div class="surroundings-item">
                <div class="surroundings-image">
                  <img src="https://picsum.photos/500/300?random=34" alt="陆家嘴食堂">
                </div>
                <div class="surroundings-info">
                  <h3>陆家嘴食堂</h3>
                  <p>距离会场步行10分钟</p>
                  <p>提供各地特色中餐，价格亲民，环境舒适。</p>
                </div>
              </div>
              
              <div class="surroundings-item">
                <div class="surroundings-image">
                  <img src="https://picsum.photos/500/300?random=35" alt="意大利餐厅">
                </div>
                <div class="surroundings-info">
                  <h3>Isola意大利餐厅</h3>
                  <p>距离会场步行15分钟</p>
                  <p>正宗意大利美食，环境优雅，适合商务宴请。</p>
                </div>
              </div>
            </div>
          </div>
          
          <div v-if="activeTab === 'shopping'" class="tab-content">
            <div class="surroundings-grid">
              <div class="surroundings-item">
                <div class="surroundings-image">
                  <img src="https://picsum.photos/500/300?random=36" alt="国金中心">
                </div>
                <div class="surroundings-info">
                  <h3>国金中心商场</h3>
                  <p>距离会场步行10分钟</p>
                  <p>高端购物中心，汇集国际奢侈品牌。</p>
                </div>
              </div>
              
              <div class="surroundings-item">
                <div class="surroundings-image">
                  <img src="https://picsum.photos/500/300?random=37" alt="正大广场">
                </div>
                <div class="surroundings-info">
                  <h3>正大广场</h3>
                  <p>距离会场步行15分钟</p>
                  <p>综合性购物中心，涵盖购物、餐饮、娱乐。</p>
                </div>
              </div>
              
              <div class="surroundings-item">
                <div class="surroundings-image">
                  <img src="https://picsum.photos/500/300?random=38" alt="世纪汇广场">
                </div>
                <div class="surroundings-info">
                  <h3>世纪汇广场</h3>
                  <p>距离会场10分钟车程</p>
                  <p>时尚购物中心，集合国际品牌和特色餐饮。</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { ref } from 'vue'
import SectionTitle from '@/components/common/SectionTitle.vue'

export default {
  name: 'AccommodationPage',
  components: {
    SectionTitle
  },
  setup() {
    const activeTab = ref('attractions')
    
    return {
      activeTab
    }
  }
}
</script>

<style scoped>
.page-header {
  background: linear-gradient(rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.3)), url('@/assets/images/dahui.png');
  background-size: cover;
  background-position: center;

  height: 200px;
  text-align: center;
  margin-top: 80px;}

.page-header h1 {
  font-size: 42px;
  margin-bottom: 15px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  animation: fadeInDown 1s ease-out;
}

.page-header p {
  font-size: 18px;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  animation: fadeInUp 1s ease-out;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.main-hotel-section {
  padding: 80px 0;
}

.hotel-card {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 30px;
  background-color: var(--bg-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
}

.hotel-image {
  height: 100%;
  overflow: hidden;
}

.hotel-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.hotel-card:hover .hotel-image img {
  transform: scale(1.05);
}

.hotel-info {
  padding: 30px;
}

.hotel-info h2 {
  font-size: 24px;
  margin-bottom: 15px;
  color: var(--primary-color);
}

.hotel-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-light);
}

.hotel-desc {
  margin-bottom: 30px;
  line-height: 1.6;
}

.room-types {
  margin-bottom: 30px;
}

.room-types h3 {
  font-size: 18px;
  margin-bottom: 15px;
  color: var(--primary-color);
}

.room-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.room-item {
  background-color: var(--bg-light);
  border-radius: var(--radius-md);
  padding: 20px;
}

.room-name {
  font-weight: 500;
  margin-bottom: 5px;
}

.room-price {
  font-size: 18px;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 10px;
}

.room-features {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.room-features span {
  font-size: 14px;
  color: var(--text-light);
  background-color: rgba(30, 136, 229, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
}

.room-note {
  font-size: 14px;
  color: var(--text-light);
  margin-top: 15px;
  font-style: italic;
}

.booking-info {
  margin-bottom: 30px;
}

.booking-info h3 {
  font-size: 18px;
  margin-bottom: 15px;
  color: var(--primary-color);
}

.booking-info p {
  margin-bottom: 10px;
}

.hotel-actions {
  display: flex;
  gap: 15px;
}

.recommended-hotels-section {
  padding: 80px 0;
  background-color: var(--bg-light);
}

.hotels-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  margin-top: 50px;
}

.hotel-card-small {
  background-color: var(--bg-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: var(--transition);
}

.hotel-card-small:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-lg);
}

.hotel-card-small .hotel-image {
  height: 200px;
}

.hotel-card-small .hotel-info {
  padding: 20px;
}

.hotel-card-small h3 {
  font-size: 18px;
  margin-bottom: 10px;
}

.hotel-card-small .hotel-meta {
  margin-bottom: 15px;
}

.hotel-price {
  font-size: 16px;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 15px;
}

.btn-sm {
  padding: 8px 15px;
  font-size: 14px;
}

.transportation-section {
  padding: 80px 0;
}

.transportation-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
  margin-top: 50px;
}

.transportation-item {
  background-color: var(--bg-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  padding: 30px;
  transition: var(--transition);
}

.transportation-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.transportation-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: rgba(30, 136, 229, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

.transportation-icon i {
  font-size: 24px;
  color: var(--primary-color);
}

.transportation-item h3 {
  font-size: 20px;
  margin-bottom: 20px;
  color: var(--primary-color);
}

.transportation-content h4 {
  font-size: 16px;
  margin-bottom: 10px;
  margin-top: 20px;
}

.transportation-content h4:first-child {
  margin-top: 0;
}

.transportation-content p {
  color: var(--text-light);
  margin-bottom: 10px;
}

.surroundings-section {
  padding: 80px 0;
  background-color: var(--bg-light);
}

.surroundings-tabs {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 40px;
}

.tab-item {
  padding: 12px 30px;
  background-color: var(--bg-color);
  border-radius: 30px;
  cursor: pointer;
  transition: var(--transition);
  font-weight: 500;
}

.tab-item:hover {
  background-color: rgba(30, 136, 229, 0.1);
}

.tab-item.active {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.surroundings-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
}

.surroundings-item {
  background-color: var(--bg-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: var(--transition);
}

.surroundings-item:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-lg);
}

.surroundings-image {
  height: 200px;
  overflow: hidden;
}

.surroundings-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.surroundings-item:hover .surroundings-image img {
  transform: scale(1.1);
}

.surroundings-info {
  padding: 20px;
}

.surroundings-info h3 {
  font-size: 18px;
  margin-bottom: 10px;
}

.surroundings-info p {
  color: var(--text-light);
  margin-bottom: 5px;
}

@media (max-width: 992px) {
  .hotel-card {
    grid-template-columns: 1fr;
  }
  
  .hotel-image {
    height: 300px;
  }
  
  .room-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .hotels-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .transportation-grid {
    grid-template-columns: 1fr;
  }
  
  .surroundings-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .hotels-grid {
    grid-template-columns: 1fr;
  }
  
  .surroundings-tabs {
    flex-direction: column;
    gap: 10px;
  }
  
  .surroundings-grid {
    grid-template-columns: 1fr;
  }
}
</style>
