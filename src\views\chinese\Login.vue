<template>
  <div class="login-page">
    <!-- 页面头部 -->
    <section class="page-header">
      <div class="container">
        <h1>登录</h1>
        <p>登录您的ICMCI 2025账户</p>
      </div>
    </section>
    
    <!-- 登录表单 -->
     <div class="conference-overview-container">
    <section class="login-section">
      <div class="container">
        <div class="auth-container">
          <div class="auth-form">
            <h2>账户登录</h2>
            <p class="auth-desc">登录您的账户，管理报名信息和查看会议资料</p>
            
            <el-form
              :model="form"
              :rules="rules"
              ref="loginFormRef"
              label-width="100px"
              class="login-form"
            >
              <el-form-item label="账号" prop="account">
                <el-input
                  v-model="form.account"
                  placeholder="请输入账号"
                />
              </el-form-item>

              <el-form-item label="密码" prop="password">
                <el-input
                  v-model="form.password"
                  type="password"
                  placeholder="请输入密码"
                  show-password
                />
              </el-form-item>

              <!-- <el-form-item label="邮箱" prop="email">
                <el-input
                  v-model="form.email"
                  placeholder="请输入邮箱"
                />
              </el-form-item>

              <el-form-item label="验证码" prop="emailCode">
                <div class="verification-code">
                  <el-input
                    v-model="form.emailCode"
                    placeholder="请输入验证码"
                    style="width: 60%"
                  />
                  <el-button
                    type="primary"
                    :disabled="countdown > 0"
                    @click="handleSendCode"
                    style="width: 35%; margin-left: 5%"
                  >
                    {{ countdown > 0 ? `${countdown}秒后重试` : "获取验证码" }}
                  </el-button>
                </div>
              </el-form-item> -->
              
              <el-form-item>
                <el-button
                  type="primary"
                  @click="handleLogin"
                  :loading="isLoading"
                  style="width: 100%"
                >
                  登录
                </el-button>
              </el-form-item>
            </el-form>
            
            <div class="auth-divider">
              <span>或</span>
            </div>
            
            <div class="auth-footer">
              <p>还没有账户？ <router-link :to="getLocalePath('/register')">立即注册</router-link></p>
              <p class="forgot-password">
                <router-link :to="getLocalePath('/forgot-password')">忘记密码？</router-link>
              </p>
            </div>
          </div>
<!--           
          <div class="auth-info">
            <div class="info-content">
              <h2>会员福利</h2>
              <ul class="benefits-list">
                <li>
                  <i class="fas fa-check-circle"></i>
                  <span>优先获取会议资料和演讲视频</span>
                </li>
                <li>
                  <i class="fas fa-check-circle"></i>
                  <span>参与专属会员活动和交流</span>
                </li>
                <li>
                  <i class="fas fa-check-circle"></i>
                  <span>获取行业研究报告和白皮书</span>
                </li>
                <li>
                  <i class="fas fa-check-circle"></i>
                  <span>与行业专家直接交流的机会</span>
                </li>
                <li>
                  <i class="fas fa-check-circle"></i>
                  <span>参与ICMCI全球管理咨询师网络</span>
                </li>
              </ul>
              
              <div class="info-cta">
                <router-link :to="getLocalePath('/register')" class="btn btn-outline-white">立即注册</router-link>
              </div>
            </div>
          </div> -->
        </div>
      </div>
    </section>
  </div>
  </div>
</template>

<script>
import { ref, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { sendEmailCode } from '../../api/auth'
import { useUserStore } from '../../stores/user'
import {useLanguage} from '@/hooks/useLanguage'
export default {
  name: 'LoginPage',
  setup() {
    const router = useRouter()
    const route = useRoute()
    const userStore = useUserStore()
    const loginFormRef = ref(null)
    const isLoading = ref(false)
    const countdown = ref(0)
    const timer = ref(null)
    const {getLocalePath} = useLanguage()

    const form = ref({
      account: '',
      password: '',
      // email: '',
      // emailCode: ''
    })

    const rules = {
      account: [
        { required: true, message: '请输入账号', trigger: 'blur' },
        { min: 4, max: 20, message: '长度在 4 到 20 个字符', trigger: 'blur' }
      ],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 8, max: 20, message: '长度在 8 到 20 个字符', trigger: 'blur' }
      ],
      // email: [
      //   { required: true, message: '请输入邮箱', trigger: 'blur' },
      //   { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
      // ],
      // emailCode: [
      //   { required: true, message: '请输入验证码', trigger: 'blur' },
      //   { len: 6, message: '验证码长度为6位', trigger: 'blur' }
      // ]
    }

    // 发送验证码
    const handleSendCode = async () => {
      if (!form.value.email) {
        ElMessage.warning('请先输入邮箱')
        return
      }
      
      if (countdown.value > 0) return
      
      try {
        const response = await sendEmailCode(form.value.email)
        
        if (response.code === 1000) {
          ElMessage.success(response.msg || '验证码已发送')
          countdown.value = 60
          timer.value = setInterval(() => {
            countdown.value--
            if (countdown.value <= 0) {
              clearInterval(timer.value)
            }
          }, 1000)
        } else {
          ElMessage.error(response.msg || '发送失败，请稍后重试')
        }
      } catch (error) {
        console.error('发送验证码失败:', error)
        ElMessage.error(error.response?.data?.message || '发送失败，请稍后重试')
      }
    }

    const handleLogin = async () => {
      if (!loginFormRef.value) return

      try {
        // 验证表单
        await loginFormRef.value.validate()
        isLoading.value = true
        
        // 调用登录API
        const success = await userStore.loginUser(form.value)
        
        if (success) {
          const redirect = route.query.redirect || '/home'
          router.push(getLocalePath(redirect))
        }
      } catch (error) {
        console.error('登录失败:', error)
        ElMessage.error(error.response?.data?.message || '登录失败，请稍后重试')
      } finally {
        isLoading.value = false
      }
    }

    // 组件卸载时清除定时器
    onUnmounted(() => {
      if (timer.value) {
        clearInterval(timer.value)
      }
    })
    
    return {
      form,
      rules,
      loginFormRef,
      isLoading,
      countdown,
      handleLogin,
      handleSendCode,
      getLocalePath
    }
  }
}
</script>

<style scoped>
.conference-overview-container{
  background: url('@/assets/images/home-bg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  min-height: 100vh;
  
}
.page-header {
  background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('@/assets/images/hu.png');
  color: var(--text-white);
  background-size: cover;
  background-position: center;
  padding: 120px 0 60px;
  text-align: center;
}

.page-header h1 {
  font-size: 36px;
  margin-bottom: 10px;
}

.login-section {
  padding: 80px 0;
  /* background-color: var(--bg-light); */
}

.auth-container {
  display: grid;
  grid-template-columns: 1fr ;
  max-width: 600px;
  margin: 0 auto;
  background-color: var(--bg-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
}

.auth-form {
  padding: 50px;
}

.auth-form h2 {
  font-size: 24px;
  margin-bottom: 10px;
  color: var(--primary-color);
}

.auth-desc {
  color: var(--text-light);
  margin-bottom: 30px;
}

.password-input {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-light);
  cursor: pointer;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.forgot-password {
  color: var(--primary-color);
  font-size: 14px;
}

.btn-block {
  width: 100%;
}

.auth-divider {
  position: relative;
  text-align: center;
  margin: 30px 0;
}

.auth-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background-color: var(--border-color);
}

.auth-divider span {
  position: relative;
  background-color: var(--bg-color);
  padding: 0 15px;
  color: var(--text-light);
}

.social-login {
  margin-bottom: 30px;
}

.social-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.auth-footer {
  text-align: center;
  color: var(--text-light);
}

.auth-footer a {
  color: var(--primary-color);
  font-weight: 500;
}

.forgot-password {
  margin-top: 10px;
}

.auth-info {
  background: linear-gradient(135deg, var(--primary-color) 0%, #1565c0 100%);
  color: var(--text-white);
  padding: 50px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.info-content h2 {
  font-size: 24px;
  margin-bottom: 30px;
}

.benefits-list {
  list-style: none;
  padding: 0;
  margin: 0 0 40px;
}

.benefits-list li {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  margin-bottom: 20px;
}

.benefits-list i {
  color: #4caf50;
  font-size: 20px;
  margin-top: 2px;
}

.info-cta {
  text-align: center;
}

@media (max-width: 992px) {
  .auth-container {
    grid-template-columns: 1fr;
  }
  
  .auth-info {
    display: none;
  }
}

@media (max-width: 576px) {
  .auth-form {
    padding: 30px 20px;
  }
  
  .form-options {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}

.verification-code {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.login-form {
  margin-top: 20px;
}
</style>
