import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '../stores/user'
import { checkPasswordExpire } from '@/api/auth'
import { ElMessage } from "element-plus";
// 导入页面组件
const Home = () => import('../views/chinese1/Home.vue')
const About = () => import('../views/chinese1/About.vue')
const Agenda = () => import('../views/chinese1/Agenda.vue')
const Registration = () => import('../views/chinese1/Registration.vue')
const CombinedRegistration = () => import('../views/chinese1/CombinedRegistration.vue')
const CompanionRegistration = () => import('../views/chinese1/CompanionRegistration.vue')
const Payment = () => import('../views/chinese1/Payment.vue')
const PaymentSuccess = () => import('../views/chinese1/PaymentSuccess.vue')
const Accommodation = () => import('../views/chinese1/Accommodation.vue')
const Contact = () => import('../views/chinese1/Contact.vue')
const Login = () => import('../views/chinese1/Login.vue')
const Register = () => import('../views/chinese1/Register.vue')
const ForgotPassword = () => import('../views/chinese1/ForgotPassword.vue')
const Dashboard = () => import('../views/chinese1/Dashboard.vue')
const ForumDetail = () => import('../views/chinese1/ForumDetail.vue')
const Profile = () => import('../views/chinese1/Profile.vue')
const Participants = () => import('../views/chinese1/Participants.vue')
const Travel = () => import('../views/chinese1/Travel.vue')
const Vistis = () => import('../views/chinese1/Vistis.vue')
const AskQuestion = () => import('../views/chinese1/AskQuestion.vue')

// 导入英文页面组件
const EnHome = () => import('../views/english/Home.vue')
const EnAbout = () => import('../views/english/About.vue')
const EnAgenda = () => import('../views/english/Agenda.vue')
const EnRegistration = () => import('../views/english/Registration.vue')
const EnCombinedRegistration = () => import('../views/english/CombinedRegistration.vue')
const EnCompanionRegistration = () => import('../views/english/CompanionRegistration.vue')
const EnPayment = () => import('../views/english/Payment.vue')
const EnPaymentSuccess = () => import('../views/english/PaymentSuccess.vue')
const EnAccommodation = () => import('../views/english/Accommodation.vue')
const EnContact = () => import('../views/english/Contact.vue')
const EnLogin = () => import('../views/english/Login.vue')
const EnRegister = () => import('../views/english/Register.vue')
const EnForgotPassword = () => import('../views/english/ForgotPassword.vue')
const EnDashboard = () => import('../views/english/Dashboard.vue')
const EnForumDetail = () => import('../views/english/ForumDetail.vue')
const EnProfile = () => import('../views/english/Profile.vue')
const EnParticipants = () => import('../views/english/Participants.vue')
const EnTravel = () => import('../views/english/Travel.vue')
const EnVistis = () => import('../views/english/Vistis.vue')
const EnAskQuestion = () => import('../views/english/AskQuestion.vue')

// 定义路由
const routes = [
  {
    path: '/chinese1/home',
    name: 'Home',
    component: Home,
    meta: { title: 'CMC 2025 中国·上海 - 国际注册管理咨询师大会' }
  },
  {
    path: '/chinese1/about',
    name: 'About',
    component: About,
    meta: { title: '关于会议 - ICMCI 2025上海国际管理咨询大会' }
  },
  {
    path: '/chinese1/agenda',
    name: 'Agenda',
    component: Agenda,
    meta: { title: '会议日程 - ICMCI 2025上海国际管理咨询大会' }
  },
  {
    path: '/chinese1/registration',
    name: 'Registration',
    component: CombinedRegistration,
    meta: {
      requiresAuth: true,
      title: '报名参会 - ICMCI 2025上海国际管理咨询大会'
    }
  },
  {
    path: '/chinese1/companion',
    name: 'Companion',
    component: CombinedRegistration,
    meta: {
      requiresAuth: true,
      title: '同行人报名 - ICMCI 2025上海国际管理咨询大会'
    }
  },
  {
    path: '/chinese1/payment',
    name: 'Payment',
    component: Payment,
    meta: { 
      title: '缴费 - ICMCI 2025上海国际管理咨询大会',
      requiresAuth: true
    }
  },
  {
    path: '/chinese1/payment-success',
    name: 'PaymentSuccess',
    component: PaymentSuccess,
    meta: { title: '支付成功 - ICMCI 2025上海国际管理咨询大会' }
  },
  {
    path: '/chinese1/accommodation',
    name: 'Accommodation',
    component: Accommodation,
    meta: { title: '住宿信息 - ICMCI 2025上海国际管理咨询大会' }
  },
  {
    path: '/chinese1/travel',
    name: 'Travel',
    component: Travel,
    meta: { title: '旅游信息 - ICMCI 2025上海国际管理咨询大会' }
  },
  {
    path: '/chinese1/Visits',
    name: 'Vistis',
    component: Vistis,
    meta: { title: '参观考察 - ICMCI 2025上海国际管理咨询大会' }
  },
  {
    path: '/chinese1/AskQuestion',
    name: 'AskQuestion',
    component: AskQuestion,
    meta: { title: '常见问题 - ICMCI 2025上海国际管理咨询大会' }
  },
  {
    path: '/chinese1/contact',
    name: 'Contact',
    component: Contact,
    meta: { title: '联系我们 - ICMCI 2025上海国际管理咨询大会' }
  },
  {
    path: '/chinese1/login',
    name: 'Login',
    component: Login,
    meta: {
      requiresAuth: false,
      title: '登录 - ICMCI 2025上海国际管理咨询大会'
    }
  },
  {
    path: '/chinese1/register',
    name: 'Register',
    component: Register,
    meta: {
      requiresAuth: false,
      title: '注册 - ICMCI 2025上海国际管理咨询大会'
    }
  },
  {
    path: '/chinese1/forgot-password',
    name: 'ForgotPassword',
    component: ForgotPassword,
    meta: {
      requiresAuth: false,
      title: '忘记密码 - ICMCI 2025上海国际管理咨询大会'
    }
  },
  {
    path: '/chinese1/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: { 
      title: '用户中心 - ICMCI 2025上海国际管理咨询大会',
      requiresAuth: true 
    }
  },
  {
    path: '/chinese1/forum/:id',
    name: 'ForumDetail',
    component: ForumDetail,
    meta: { title: '论坛详情 - ICMCI 2025上海国际管理咨询大会' }
  },
  {
    path: '/chinese1/profile',
    name: 'Profile',
    component: Profile,
    meta: {
      requiresAuth: true,
      title: '个人资料 - ICMCI 2025上海国际管理咨询大会'
    }
  },
  {
    path: '/chinese1/participants',
    name: 'Participants',
    component: Participants,
    meta: { title: '重要嘉宾 - ICMCI 2025上海国际管理咨询大会' }
  },

  // 英文路由
  {
    path: '/english/home',
    name: 'EnHome',
    component: EnHome,
    meta: { title: 'CMC 2025 Shanghai China - International Management Consulting Conference' }
  },
  {
    path: '/english/about',
    name: 'EnAbout',
    component: EnAbout,
    meta: { title: 'About - ICMCI 2025 Shanghai Conference' }
  },
  {
    path: '/english/agenda',
    name: 'EnAgenda',
    component: EnAgenda,
    meta: { title: 'Agenda - ICMCI 2025 Shanghai Conference' }
  },
  {
    path: '/english/registration',
    name: 'EnRegistration',
    component: EnCombinedRegistration,
    meta: {
      requiresAuth: true,
      title: 'Registration - ICMCI 2025 Shanghai Conference'
    }
  },
  {
    path: '/english/companion',
    name: 'EnCompanion',
    component: EnCombinedRegistration,
    meta: {
      requiresAuth: true,
      title: 'Companion Registration - ICMCI 2025 Shanghai Conference'
    }
  },
  {
    path: '/english/payment',
    name: 'EnPayment',
    component: EnPayment,
    meta: { 
      title: 'Payment - ICMCI 2025 Shanghai Conference',
      requiresAuth: true
    }
  },
  {
    path: '/english/payment-success',
    name: 'EnPaymentSuccess',
    component: EnPaymentSuccess,
    meta: { title: 'Payment Success - ICMCI 2025 Shanghai Conference' }
  },
  {
    path: '/english/accommodation',
    name: 'EnAccommodation',
    component: EnAccommodation,
    meta: { title: 'Accommodation - ICMCI 2025 Shanghai Conference' }
  },
  {
    path: '/english/travel',
    name: 'EnTravel',
    component: EnTravel,
    meta: { title: 'Travel Info - ICMCI 2025 Shanghai Conference' }
  },
  {
    path: '/english/Visits',
    name: 'EnVistis',
    component: EnVistis,
    meta: { title: 'Site Visits - ICMCI 2025 Shanghai Conference' }
  },
  {
    path: '/english/AskQuestion',
    name: 'EnAskQuestion',
    component: EnAskQuestion,
    meta: { title: 'FAQ - ICMCI 2025 Shanghai Conference' }
  },
  {
    path: '/english/contact',
    name: 'EnContact',
    component: EnContact,
    meta: { title: 'Contact Us - ICMCI 2025 Shanghai Conference' }
  },
  {
    path: '/english/login',
    name: 'EnLogin',
    component: EnLogin,
    meta: {
      requiresAuth: false,
      title: 'Login - ICMCI 2025 Shanghai Conference'
    }
  },
  {
    path: '/english/register',
    name: 'EnRegister',
    component: EnRegister,
    meta: {
      requiresAuth: false,
      title: 'Register - ICMCI 2025 Shanghai Conference'
    }
  },
  {
    path: '/english/forgot-password',
    name: 'EnForgotPassword',
    component: EnForgotPassword,
    meta: {
      requiresAuth: false,
      title: 'Forgot Password - ICMCI 2025 Shanghai Conference'
    }
  },
  {
    path: '/english/dashboard',
    name: 'EnDashboard',
    component: EnDashboard,
    meta: { 
      title: 'Dashboard - ICMCI 2025 Shanghai Conference',
      requiresAuth: true 
    }
  },
  {
    path: '/english/forum/:id',
    name: 'EnForumDetail',
    component: EnForumDetail,
    meta: { title: 'Forum Detail - ICMCI 2025 Shanghai Conference' }
  },
  {
    path: '/english/profile',
    name: 'EnProfile',
    component: EnProfile,
    meta: {
      requiresAuth: true,
      title: 'Profile - ICMCI 2025 Shanghai Conference'
    }
  },
  {
    path: '/english/participants',
    name: 'EnParticipants',
    component: EnParticipants,
    meta: { title: 'Key Participants - ICMCI 2025 Shanghai Conference' }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory('/'),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({ top: 0, behavior: 'smooth' })
        }, 100)
      })
    }
  }
})
const checkPasswordExpiration = async (account) => {
  try {
    const res = await checkPasswordExpire(account)
    if (res.code === 1000 && res.data.data === false) {
      sessionStorage.setItem('passwordExpired', 'true')
      return true
    } else {
      sessionStorage.setItem('passwordExpired', 'false')
      return false
    }
  } catch (e) {
    console.error('检查密码有效期失败', e)
    return false
  }
}

// const isExpired =  checkPasswordExpiration(userStore.userInfo.account)

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  document.title = to.meta.title || 'ICMCI 2025上海国际管理咨询大会'
 
  const userStore = useUserStore()
  // 将字符串转换为布尔值
  const passwordExpired = sessionStorage.getItem('passwordExpired') === 'true'
  // const passwordExpired = true
// console.log(passwordExpired)
  // 需要登录的页面列表
  const authRequiredPages = ['Registration', 'CompanionRegistration', 'Payment', 'Dashboard','EnRegistration', 'EnCompanionRegistration', 'EnPayment', 'EnDashboard','EnPaymentSuccess','PaymentSuccess']
  // console.log(to.name)
  // 1. 检查是否需要登录
  if (authRequiredPages.includes(to.name) && !userStore.isLoggedIn) {
    if (to.path.startsWith('/english/')) {
      next({ name: 'EnLogin', query: { redirect: to.fullPath } })
    } else {
      next({ name: 'Login', query: { redirect: to.fullPath } })
    }
    return
  }
  
  // 2. 已登录用户访问登录/注册页，重定向到首页
  if (to.meta.requiresAuth === false && userStore.isLoggedIn) {
    next({ name: 'Home' })
    return
  }

  // 3. 检查密码是否过期（仅对已登录用户）
  if (userStore.isLoggedIn && userStore.userInfo.account) {
    // 如果本地没有存储密码状态，则调用接口检查
    if (passwordExpired === null) {
      const isExpired = await checkPasswordExpiration(userStore.userInfo.account)
      if (isExpired) {
        // 密码已过期，跳转到 Dashboard
        ElMessage.warning('密码即将过期请修改密码')
        next({ 
          name: 'Dashboard',
          query: { 
            passwordExpired: 'true'
          }
        })
        return
      }
    } else if (passwordExpired && to.name !== 'Dashboard') {
      // 如果本地存储显示密码已过期，且不是已经在 Dashboard 页面，才进行跳转
      ElMessage.warning('密码即将过期请修改密码')
      next({ 
        name: 'Dashboard',
        query: { 
          passwordExpired: 'true',
        }
      })
      return
    }
  }

  // 4. 其他情况正常放行
  next()
})

// 添加导航守卫
router.beforeEach((to, from, next) => {
  // 获取保存的语言设置
  const savedLang = localStorage.getItem('preferredLanguage')
  
  // 如果路径不包含语言前缀，且存在保存的语言设置
  if (!to.path.startsWith('/chinese/') && !to.path.startsWith('/english/') && savedLang) {
    // 重定向到对应语言的路径
    next(`/${savedLang}${to.path}`)
  } else {
    next()
  }
})

export default router
