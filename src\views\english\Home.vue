<template>
  <div class="home-page">
    <!-- 英雄区域 -->
    <section class="hero-section">
      <div class="hero-background">
        <img src="@/assets/images/banner.jpg" alt="ICMCI 2025 Shanghai" class="img">
       
      </div>
      <div class="hero-content"> 
        <!-- <h1 class="main-title">CMC 2025 中国·上海</h1>
        <p class="subtitle">国际注册管理咨询师大会</p> -->
        <div class="event-details">
          <div>
            <i class="fas fa-calendar"></i>
            <span>October 21-24,2025</span>
          </div>
          <div>
            <i class="fas fa-map-marker-alt"></i>
            <span>Pudong Shangri-La, Shanghai</span>
          </div>
        </div>
        <div class="cta-buttons">
          <router-link :to="getLocalePath('registration')" class="btn btn-primary">
            REGISTER
          </router-link>
          <router-link :to="getLocalePath('agenda')" class="btn btn-outline">
            AGENDA
          </router-link>
        </div>
        <Countdown lang="EN"  targetDate="2025-10-21T18:30:00" />
      </div>
    </section>

    <!-- 会议概览 -->
     <div class="conference-overview-container">
    <section class="conference-overview" v-intersection-observer="handleOverviewIntersection">
      <div class="container">
        <h2 class="section-title" :class="{ 'animate__animated animate__fadeInDown': overviewVisible }">Overview</h2>
        <div class="overview-grid">
          <div 
            class="overview-card"
            :class="{ 'animate__animated animate__fadeInLeft': overviewVisible }"
            :style="{ animationDelay: '0.2s' }"
          >
            <div class="card-icon">
              <img src="../../assets/images/logo.png" alt="国际CMC大会">
            </div>
            <div>

         
            <h3>International CMC Conference</h3>
            <p>2025.10.21-2025.10.23 for international CMC Conference</p>
          </div>
            <!-- <router-link to="/about#cmc" class="read-more">了解更多</router-link> -->
          </div>
          <div 
            class="overview-card"
            :class="{ 'animate__animated animate__fadeInRight': overviewVisible }"
            :style="{ animationDelay: '0.4s' }"
          >
            <div class="card-icon">
              <img src="../../assets/images/logo.png" alt="ICMCI年会">
            </div>
            <div>
            <h3>ICMCI annual meeting</h3>
            <p>2025.10.23-2025.10.24 for ICMCI Annual Meeting of Delegates</p>
          </div>
            <!-- <router-link to="/about#icmci" class="read-more">了解更多</router-link> -->
          </div>
        </div>
      </div>
    </section>

    <!-- 主题介绍 -->
    <section class="theme-section" v-intersection-observer="handleThemeIntersection">
      <div class="container">
        <h2 class="section-title" :class="{ 'animate__animated animate__fadeInDown': themeVisible }">Theme</h2>
        <div class="theme-content">
          <h3 :class="{ 'animate__animated animate__fadeInLeft': themeVisible }">Digital intelligence leads the future,consulting promotes management innovation and sustainable development</h3>
          <p  :class="{ 'animate__animated animate__fadeInUp': themeVisible }" :style="{ animationDelay: '0.3s' }">Digitization will have a profound impact not only on industry, but also on business and society, and Artificial Intelligence (AI) has undoubtedly become one of the areas with the most transformative power. How can enterprises cope with the new round of challenges? How can management consulting quickly integrate into the new wave, innovate management concepts, develop new tools, and help companies realize high-quality sustainable development.</p>
          
          <div class="forums-grid">
            <div 
              v-for="(forum, index) in 1" 
              :key="index"
              class="forum-item"
              :class="{ 'animate__animated animate__fadeInUp': themeVisible }"
              :style="{ animationDelay: `${0.4 + index * 0.1}s` }"
            >
              <div class="forum-content">
                <span class="forum-number">{{ index + 1 }}</span>
                <h4>CMC  exchanges  management  consulting experiences among countries and explores new trends in management consulting</h4>
                <!-- <p>对企业管理的影响及挑战</p> -->
                <!-- <div class="forum-price">
                  <span class="price-label">分论坛价格</span>
                  <span class="price-value">¥1,200</span>
                </div> -->
              </div>
              <!-- <a href="forum-detail.html?id=ai-industry" class="forum-link">
                <span>查看详情</span>
                <i class="fas fa-arrow-right"></i>
              </a> -->
            </div>
            <div 
              v-for="(forum, index) in 1" 
              :key="index + 1"
              class="forum-item"
              :class="{ 'animate__animated animate__fadeInUp': themeVisible }"
              :style="{ animationDelay: `${0.4 + index * 0.1}s` }"
            >
              <div class="forum-content">
                <span class="forum-number">{{ index + 2 }}</span>
                <h4>Explore cooperation opportunities  and promote the improvement of management level in enterprises</h4>
                <!-- <p>跨组织跨领域跨学科协同</p> -->
                <!-- <div class="forum-price">
                  <span class="price-label">分论坛价格</span>
                  <span class="price-value">¥1,200</span>
                </div> -->
              </div>
              <!-- <a href="forum-detail.html?id=management-governance" class="forum-link">
                <span>查看详情</span>
                <i class="fas fa-arrow-right"></i>
              </a> -->
            </div>
            <div 
              v-for="(forum, index) in 1" 
              :key="index + 10"
              class="forum-item"
              :class="{ 'animate__animated animate__fadeInUp': themeVisible }"
              :style="{ animationDelay: `${0.4 + index * 0.1}s` }"
            >
              <div class="forum-content">
                <span class="forum-number">{{ index + 3 }}</span>
                <h4>Presentation of the International Management Consulting Case Award (Constantine Award)</h4>
                <!-- <p>技术与咨询的深度融合</p> -->
                <!-- <div class="forum-price">
                  <span class="price-label">分论坛价格</span>
                  <span class="price-value">¥1,200</span>
                </div> -->
              </div>
              <!-- <a href="forum-detail.html?id=ai-consulting" class="forum-link">
                <span>查看详情</span>
                <i class="fas fa-arrow-right"></i>
              </a> -->
            </div>
           
          </div>
        </div>
      </div>
    </section>

    <!-- 会场展示 -->
    <section class="venue-section" v-intersection-observer="handleVenueIntersection">
      <div class="container">
        <h2 class="section-title" :class="{ 'animate__animated animate__fadeInDown': venueVisible }">Venue</h2>
        <div 
          class="venue-slider"
          :class="{ 'animate__animated animate__fadeInUp': venueVisible }"
          :style="{ animationDelay: '0.3s' }"
        >
          <div class="slider-container" ref="sliderContainer">
            <div class="slide" v-for="(slide, index) in venueSlides" :key="index" :class="{ 'active': currentSlide === index }">
              <img :src="slide.image" :alt="slide.alt">
            </div>
          </div>
          <div class="slider-controls">
            <button class="prev-btn" @click="prevSlide"><i class="fas fa-chevron-left"></i></button>
            <div class="slider-dots">
              <span
                v-for="(slide, index) in venueSlides"
                :key="index"
                class="dot"
                :class="{ 'active': currentSlide === index }"
                @click="goToSlide(index)"
              ></span>
            </div>
            <button class="next-btn" @click="nextSlide"><i class="fas fa-chevron-right"></i></button>
          </div>
        </div>
        <div 
          class="venue-info"
          :class="{ 'animate__animated animate__fadeInUp': venueVisible }"
          :style="{ animationDelay: '0.5s' }"
        >
          <h3>Pudong Shangri-La, Shanghai</h3>
          <p>It is located in the lively Lujiazui financial district, offering stunning views of the famous Huangpu River and the historic Bund. The hotel is walking distance to the Oriental Pearl TV Tower and IFC Mall. Guests can choose from 8 restaurants and bars that the hotel offers. They include the Chinese restaurant Gui Hua Lou, Jade on 36, a fine dining French restaurant Bund view, and the buffet restaurant YICAFE.</p>
          <div class="venue-details">
            <div class="detail-item">
              <i class="fas fa-map-marker-alt"></i>
              <span>Pudong Shangri-La, Shanghai</span>
            </div>
            <div class="detail-item">
              <i class="fas fa-phone"></i>
              <span>+86 21 6882 8888</span>
            </div>
            <div class="detail-item">
              <i class="fas fa-envelope"></i>
              <span><EMAIL></span>
            </div>
          </div>
          
          <a class="venue-link" href="https://www.shangri-la.com/shanghai/pudongshangrila?lang=en" target="_blank">
            <span class="link-content">
              <i class="fas fa-hotel"></i>
              Official Website
              <i class="fas fa-external-link-alt"></i>
            </span>
          </a>
          <!-- <router-link :to="getLocalePath('accommodation')" class="btn btn-secondary">住宿详情</router-link> -->
        </div>
      </div>
    </section>

    <!-- 快速报名 -->
    <section class="quick-register">
      <div class="container">
        <div class="register-card">
          <h2>Register for the conference Now</h2>
          <p>To explore the future of the industry with global management consulting experts</p>
          <router-link :to="getLocalePath('registration')" class="btn btn-primary btn-large">
            <span class="btn-content">
              <i class="fas fa-arrow-right"></i>
              register
              <i class="fas fa-arrow-right"></i>
            </span>
          </router-link>
       </div>
      </div>
    </section>
    
</div>
  </div>
</template>

<script>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import Countdown from '../../components/common/Countdown.vue'
import { useLanguage } from '@/hooks/useLanguage'
import 'animate.css'
import shanghai01 from '@/assets/images/shanghai01.jpg'
import shanghai02 from '@/assets/images/shanghai02.jpg'
import shanghai03 from '@/assets/images/shanghai03.jpg'
import shanghai04 from '@/assets/images/shanghai04.jpg'

export default {
  name: 'HomePage',
  components: {
    Countdown
  },
  directives: {
    intersectionObserver: {
      mounted(el, binding) {
        let lastScrollY = window.scrollY
        let hasAnimated = false

        const observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            const currentScrollY = window.scrollY
            const isScrollingDown = currentScrollY > lastScrollY

            if (entry.isIntersecting) {
              // 向下滚动时触发动画
              if (isScrollingDown) {
                binding.value(true)
                hasAnimated = true
              }
            } else if (isScrollingDown) {
              // 向下滚动时保持可见
              binding.value(true)
            } else {
              // 向上滚动时重置状态
              binding.value(false)
              hasAnimated = false
            }

            lastScrollY = currentScrollY
          })
        }, {
          threshold: 0.1,
          rootMargin: '0px'
        })

        observer.observe(el)
      }
    }
  },
  setup() {
    const { getLocalePath } = useLanguage()
    const currentSlide = ref(0)
    let slideInterval = null

    // 添加动画状态控制
    const overviewVisible = ref(false)
    const themeVisible = ref(false)
    const venueVisible = ref(false)
    const registerVisible = ref(false)

    // 处理各个部分的可见性
    const handleOverviewIntersection = (isVisible) => {
      overviewVisible.value = isVisible
    }

    const handleThemeIntersection = (isVisible) => {
      themeVisible.value = isVisible
    }

    const handleVenueIntersection = (isVisible) => {
      venueVisible.value = isVisible
    }

    const handleRegisterIntersection = (isVisible) => {
      registerVisible.value = isVisible
    }

    // 会场轮播图数据
    const venueSlides = [
      {
        image: shanghai01,
        alt: '上海浦东香格里拉大酒店'
      },
      {
        image: shanghai02,
        alt: '上海浦东香格里拉大酒店'
      },
      {
        image: shanghai03,
        alt: '上海浦东香格里拉大酒店'
      },
      {
        image: shanghai04,
        alt: '上海浦东香格里拉大酒店'
      }
    ]

    // 轮播图控制函数
    const nextSlide = () => {
      currentSlide.value = (currentSlide.value + 1) % venueSlides.length
    }

    const prevSlide = () => {
      currentSlide.value = (currentSlide.value - 1 + venueSlides.length) % venueSlides.length
    }

    const goToSlide = (index) => {
      currentSlide.value = index
    }

    // 自动轮播
    const startSlideShow = () => {
      slideInterval = setInterval(() => {
        nextSlide()
      }, 5000)
    }

    onMounted(() => {
      startSlideShow()
    })

    onBeforeUnmount(() => {
      if (slideInterval) {
        clearInterval(slideInterval)
      }
    })

    return {
      venueSlides,
      currentSlide,
      nextSlide,
      prevSlide,
      goToSlide,
      getLocalePath,
      overviewVisible,
      themeVisible,
      venueVisible,
      registerVisible,
      handleOverviewIntersection,
      handleThemeIntersection,
      handleVenueIntersection,
      handleRegisterIntersection
    }
  }
}
</script>

<style scoped>
@import url('@/assets/css/home.css');
</style>
