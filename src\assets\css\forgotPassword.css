.conference-overview-container{
    background: url('@/assets/images/home-bg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    min-height: 100vh;
    
  }
.page-header {
    background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('@/assets/images/zhuce.png');
    background-size: cover;
    background-position: center;
    color: var(--text-white);
    padding: 120px 0 60px;
    text-align: center;
    position: relative;
  }
  
  .page-header h1 {
    font-size: 36px;
    margin-bottom: 10px;
  }
  
  .reset-section {
    padding: 80px 0;
    /* background-color: var(--bg-light); */
  }
  
  .auth-container {
    max-width: 600px;
    margin: 0 auto;
    background-color: var(--bg-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
  }
  
  .auth-form {
    padding: 50px;
  }
  
  .auth-form h2 {
    font-size: 24px;
    margin-bottom: 10px;
    color: var(--primary-color);
  }
  
  .auth-desc {
    color: var(--text-light);
    margin-bottom: 30px;
  }
  
  .verification-code {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  .reset-form {
    margin-top: 20px;
  }
  
  .auth-footer {
    text-align: center;
    margin-top: 20px;
    color: var(--text-light);
  }
  
  .auth-footer a {
    color: var(--primary-color);
    font-weight: 500;
  }
  
  @media (max-width: 576px) {
    .auth-form {
      padding: 30px 20px;
    }
  }