.profile-page {
    min-height: 100vh;
  }
  
  .page-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1565c0 100%);
    color: var(--text-white);
    padding: 120px 0 60px;
    text-align: center;
  }
  
  .page-header h1 {
    font-size: 36px;
    margin-bottom: 10px;
  }
  
  .profile-container {
    max-width: 1200px;
    margin: 0 auto;
    background-color: var(--bg-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
  }
  
  .profile-sidebar {
    width: 200px;
    background-color: var(--bg-light);
    border-right: 1px solid var(--border-color);
  }
  
  .profile-content {
    flex: 1;
    padding: 20px;
  }
  
  .profile-tabs {
    height: 100%;
  }
  
  .tab-label {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .profile-form h2,
  .password-form h2 {
    font-size: 24px;
    margin-bottom: 30px;
    color: var(--primary-color);
  }
  
  @media (max-width: 768px) {
    .profile-container {
      flex-direction: column;
    }
  
    .profile-sidebar {
      width: 100%;
      border-right: none;
      border-bottom: 1px solid var(--border-color);
    }
  
    .profile-content {
      padding: 20px;
    }
  }
  
  .registration-info h3,
  .companions-info h3,
  .payment-info h3 {
    font-size: 18px;
    margin-bottom: 20px;
    color: var(--primary-color);
  }
  
  .visa-info {
    margin-top: 20px;
  }
  
  .visa-info h4 {
    font-size: 16px;
    margin-bottom: 15px;
    color: var(--primary-color);
  }
  
  .payment-actions {
    margin-top: 20px;
    text-align: center;
  }
  
  :deep(.el-descriptions) {
    margin-bottom: 20px;
  }
  
  :deep(.el-descriptions__label) {
    width: 120px;
  }
  
  :deep(.el-collapse-item__header) {
    font-size: 16px;
    font-weight: 500;
  }
  
  :deep(.el-collapse-item__content) {
    padding: 20px;
  }
  
  .registration-form {
    padding: 20px;
  }
  
  .form-row {
    display: flex;
    gap: 20px;
  }
  
  .form-group {
    flex: 1;
  }
  
  .form-section {
    margin-bottom: 30px;
  }
  
  .form-section h3 {
    font-size: 18px;
    margin-bottom: 20px;
    color: var(--primary-color);
  }
  
  .form-actions {
    text-align: center;
    margin-top: 30px;
  }
  
  .word-count {
    font-size: 12px;
    color: var(--text-light);
    margin-top: 5px;
  }
  
  .form-hint {
    font-size: 12px;
    color: var(--text-light);
    margin-top: 5px;
  }
  
  :deep(.el-form-item) {
    margin-bottom: 20px;
  }
  
  :deep(.el-form-item__label) {
    font-weight: 500;
  }
  
  :deep(.el-input__wrapper) {
    box-shadow: 0 0 0 1px var(--border-color) inset;
  }
  
  :deep(.el-input__wrapper:hover) {
    box-shadow: 0 0 0 1px var(--primary-color) inset;
  }
  
  :deep(.el-input__wrapper.is-focus) {
    box-shadow: 0 0 0 1px var(--primary-color) inset;
  }
  
  :deep(.el-radio-group) {
    display: flex;
    gap: 20px;
  }
  
  :deep(.el-radio) {
    margin-right: 0;
  }
  
  :deep(.el-textarea__inner) {
    min-height: 100px;
    resize: vertical;
  }
  
  :deep(.el-date-editor) {
    width: 100%;
  }
  
  .companion-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .companion-form {
    padding: 20px;
  }
  
  .detail-content {
    padding: 20px;
  }
  
  .detail-section {
    margin-bottom: 30px;
  }
  
  .detail-section h3 {
    font-size: 18px;
    margin-bottom: 20px;
    color: var(--primary-color);
  }
  
  .visa-info {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
  }
  
  .visa-info h3 {
    font-size: 16px;
    margin-bottom: 15px;
  }
  
  :deep(.el-descriptions) {
    margin-bottom: 20px;
  }
  
  :deep(.el-descriptions__label) {
    width: 120px;
    background-color: var(--bg-light);
  }
  
  :deep(.el-collapse-item__header) {
    font-size: 16px;
    font-weight: 500;
  }
  
  :deep(.el-collapse-item__content) {
    padding: 20px;
  }
  
  .detail-dialog {
    :deep(.el-dialog) {
      display: flex;
      flex-direction: column;
      max-height: 80vh;
    }
  
    .el-dialog__body {
      overflow-y: auto;
      padding: 0;
    }
  }
  
  .detail-content {
    padding: 20px;
    height: 100%;
  }
  
  .el-dropdown-link {
    cursor: pointer;
    color: var(--el-color-primary);
    /* display: flex;
    align-items: center; */
    margin-top: 4px;
    margin-left: 10px;
  }
  
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
  
  .el-image {
    width: 200px;
  }