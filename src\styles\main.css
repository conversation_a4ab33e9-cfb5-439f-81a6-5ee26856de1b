/* 主样式文件 */
@import './variables.css';
@import './reset.css';

/* 容器样式 */
.container {
  max-width: var(--container-width);
  margin: 0 auto;
  padding: 0 20px;
}

/* 按钮样式 */
.btn {
  display: inline-block;
  padding: 10px 20px;
  border-radius: var(--radius-sm);
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  transition: var(--transition);
  border: none;
  outline: none;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.btn-primary:hover {
  background-color: #1565c0;
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: var(--text-white);
}

.btn-secondary:hover {
  background-color: #00897b;
}

.btn-accent {
  background-color: var(--accent-color);
  color: var(--text-white);
}

.btn-accent:hover {
  background-color: #f57c00;
}

.btn-outline {
  background-color: transparent;
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
}

.btn-outline:hover {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.btn-outline-white {
  background-color: transparent;
  border: 2px solid var(--text-white);
  color: var(--text-white);
}

.btn-outline-white:hover {
  background-color: var(--text-white);
  color: var(--primary-color);
}

/* 标题样式 */
.section-title {
  text-align: center;
  font-size: 32px;
  margin-bottom: 50px;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background-color: var(--primary-color);
}

/* 表单样式 */


.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background-color: var(--bg-color);
  transition: var(--transition);
}

.form-control:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 3px rgba(30, 136, 229, 0.2);
}

.form-row {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  /* gap: 20px; */
  column-gap: 20px;
}

/* 响应式样式 */
@media (max-width: 1200px) {
  :root {
    --container-width: 960px;
  }
}

@media (max-width: 992px) {
  :root {
    --container-width: 720px;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  :root {
    --container-width: 540px;
    --header-height: 70px;
  }
  
  .section-title {
    font-size: 28px;
  }
}

@media (max-width: 576px) {
  :root {
    --container-width: 100%;
    --header-height: 60px;
  }
  
  .container {
    padding: 0 15px;
  }
  
  .section-title {
    font-size: 24px;
  }
}
