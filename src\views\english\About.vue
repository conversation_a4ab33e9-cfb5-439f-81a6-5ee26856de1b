<template>
  <div class="about-page">
    <!-- 页面头部 -->
    <section class="page-header about-header">
      <!-- <div class="container">
        <h1 class="animate__animated animate__fadeInDown">About</h1>
        <p class="animate__animated animate__fadeInUp">October 21-24, 2025 · Pudong Shangri-La, Shanghai</p>
      </div> -->
    </section>
    <div class="conference-overview-container">
    <!-- 会议简介 -->
    <section class="about-intro" id="intro" v-intersection-observer="handleIntroIntersection">
      <div class="container">
        <h2 class="section-title" :class="{ 'animate__animated animate__fadeInDown': introVisible }">Introduction</h2>
        <div class="intro-grid">
          <div class="intro-content" :class="{ 'animate__animated animate__fadeInLeft': introVisible }">
            <!-- <h2>CMC&ICMCI 2025上海国际管理咨询大会</h2> -->
            <p class="lead">The International CMC Conference is an annual event and considered the preeminent conference for Management Consultants worldwide. Through this conference, CMC-Global seeks to network top consultants across countries/regions and disperse knowledge of leading consulting practices & concepts world-wide.</p>
            <p class="lead">The 2025 conference is hosted and managed by IMC China, with the support of CMC-Global; and is attended by management consultants across all sub-specialties. This includes those who are new to or students who want to join the profession, as well as individual CMCs that are advanced in the profession and want to contribute to their professional development, plus leaders of CMC-Firms or Accredited Consulting Practices.</p>
            <div class="key-info">
              <div class="info-item" :class="{ 'animate__animated animate__fadeInUp': introVisible }" :style="{ animationDelay: '0.2s' }">
                <i class="fas fa-calendar-alt"></i>
                <div class="info-content">
                  <h4>Meeting time</h4>
                  <p>October 21-24,2025</p>
                </div>
              </div>
              <div class="info-item" :class="{ 'animate__animated animate__fadeInUp': introVisible }" :style="{ animationDelay: '0.3s' }">
                <i class="fas fa-map-marker-alt"></i>
                <div class="info-content">
                  <h4>Venue</h4>
                  <p>Pudong Shangri-La, Shanghai</p>
                </div>
              </div>
            </div>
          </div>
          <div class="intro-image" :class="{ 'animate__animated animate__fadeInRight': introVisible }">
            <img src="@/assets/images/about1.jpg" alt="会议场景">
          </div>
        </div>
      </div>
    </section>

    <!-- 会议主题 -->
    <section class="about-theme bg" id="theme" v-intersection-observer="handleThemeIntersection">
      <div class="container">
        <h2 class="section-title" :class="{ 'animate__animated animate__fadeInDown': themeVisible }">Theme</h2>
        <div class="theme-content">
          <p class="theme-desc" :class="{ 'animate__animated animate__fadeIn': themeVisible }">Digital intelligence leads the future,consulting promotes management innovation and sustainable development</p>
          <!-- <div class="theme-grid">
            <div class="theme-item" v-for="(item, index) in 4" :key="index"
              :class="{ 'animate__animated animate__fadeInUp': themeVisible }"
              :style="{ animationDelay: `${index * 0.2}s` }">
              <div class="theme-icon">
                <i class="fas fa-robot"></i>
              </div>
              <h3>人工智能与管理咨询</h3>
              <p>探讨AI技术如何重塑管理咨询行业，以及咨询顾问如何利用AI工具提升服务价值。</p>
            </div>
          </div> -->
        </div>
      </div>
    </section>

    <!-- 会议亮点 -->
    <section class="about-highlights" id="highlights" v-intersection-observer="handleHighlightsIntersection">
      <div class="container">
        <h2 class="section-title" :class="{ 'animate__animated animate__fadeInDown': highlightsVisible }">Highlights</h2>
        <div class="highlights-grid">
          <div class="highlight-card" v-for="(item, index) in highLightsList" :key="index"
            :class="{ 'animate__animated animate__fadeInUp': highlightsVisible }"
            :style="{ animationDelay: `${index * 0.2}s` }">
            <div class="highlight-icon">
              <i class="fas fa-globe"></i>
            </div>
            <h3>{{ item.label }}</h3>
            <p>{{ item.desc }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 重要嘉宾 -->
    <div class="about-highlights bg" id="speakers" v-intersection-observer="handleSpeakersIntersection">
      <h2 class="section-title" :class="{ 'animate__animated animate__fadeInDown': speakersVisible }">VIPs</h2>
      <div class="more-speakers" @click="getLocalePath($router.push('participants'))">more</div>
      <div class="speakers-grid">
        <div class="speaker-card" v-for="(item, index) in 6" :key="index"
          :class="{ 'animate__animated animate__fadeInUp': speakersVisible }"
          :style="{ animationDelay: `${index * 0.2}s` }">
          <!-- <div class="speaker-image image-container image-placeholder"> -->
            <!-- <img src="@/assets/images/speaker.jpg" 
                 alt="张教授"
                 loading="lazy"
                 onload="this.parentElement.classList.remove('image-placeholder')">
          </div>
          <div class="speaker-info">
            <h3>张教授</h3>
            <p class="speaker-title">清华大学经济管理学院教授</p>
            <p class="speaker-bio">专注于数字化转型与管理创新研究，发表多篇高水平学术论文。</p>
          </div> -->

             <div class="speaker-image image-container image-placeholder">
                <img 
                  :src="getSpeakerImage(index)" 
                 
                  loading="lazy"
                  @load="handleImageLoad"
                >
              </div>
              <div class="speaker-info">
                <p class="speaker-title">{{ getSpeakerTitle(index) }}</p>
                <p class="speaker-bio">{{ getSpeakerBio(index) }}</p>
              </div>
        </div>
      </div>
    </div>

    <!-- 分论坛内容 -->
    <!--<section class="forum-section" id="forum" v-intersection-observer="handleForumIntersection">
      <h2 class="section-title" :class="{ 'animate__animated animate__fadeInDown': forumVisible }">Sub-forum Meeting</h2>
      <div class="container">
        <div class="forum-grid">
          <div class="forum-card" v-for="(item, index) in 5" :key="index"
            :class="{ 'animate__animated animate__fadeInUp': forumVisible }"
            :style="{ animationDelay: `${index * 0.2}s` }">
            <div class="forum-image">
              <img src="https://picsum.photos/800/400?random=1" alt="AI重塑产业格局">
            </div>
            <div class="forum-content">
              <h2>各国CMC交流管理咨询经验，探讨管理咨询新趋势</h2>
              <p>探讨人工智能技术如何改变传统产业格局，分析对企业管理带来的机遇与挑战，分享前沿实践案例。</p>
              <div class="forum-details">
                <div class="detail-item">
                  <i class="fas fa-calendar"></i>
                  <span>2024年10月22日 14:00-16:00</span>
                </div>
                <div class="detail-item">
                  <i class="fas fa-map-marker-alt"></i>
                  <span>上海国际会议中心 - 分会场A</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>-->
 <!-- 合作伙伴 -->
 <section class="about-theme bg" id="partners" v-intersection-observer="handlePartnersIntersection">
      <div class="container">
        <h2 class="section-title" :class="{ 'animate__animated animate__fadeInDown': partnersVisible }">Sponser</h2>
        <div class="theme-content">
          <!-- <p class="theme-desc" :class="{ 'animate__animated animate__fadeIn': partnersVisible }">
            The China Chamber of International Commerce (CCOIC) is China's largest trade and investment promotion organization, with over 200,000 member enterprises covering all sectors of the national economy. As a direct institution of the China Council for the Promotion of International Trade (CCPIT), CCOIC is dedicated to promoting international economic and trade exchanges and cooperation.
          </p> -->
          <div class="theme-grid">
            <div class="theme-item" v-for="(item, index) in partnerItems" :key="index"
              :class="{ 'animate__animated animate__fadeInUp': partnersVisible }"
              :style="{ animationDelay: `${index * 0.2}s` }">
              <div class="theme-icon">
                <!-- <i :class="item.icon"></i> -->
                 <img :src="item.img" alt="">
              </div>
              <h3>{{ item.title }}</h3>
              <p>{{ item.desc }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- 媒体支持 -->
    <!-- <section class="media-support bg" id="media" v-intersection-observer="handleMediaIntersection">
      
      <div class="container">
        <h2 class="section-title" :class="{ 'animate__animated animate__fadeInDown': mediaVisible }">Media</h2>
        <div class="media-grid">
          <div class="media-card" v-for="(item, index) in MediaList" :key="index"
            :class="{ 'animate__animated animate__fadeInUp': mediaVisible }"
            :style="{ animationDelay: `${index * 0.2}s` }">
              <img :src="item.img" alt="">
            <div class="media-info">
            </div>
          </div>
        </div>
      </div>
    </section> -->

    <!-- 住宿信息模块 :class="{ 'animate__animated animate__fadeInDown': forumVisible }"-->
    <section class="about-hotel " id="hotel">
      <div class="container">
        <h2 class="section-title" style="opacity: 1;" :class="{ 'animate__animated animate__fadeInDown': forumVisible }">Accommodation</h2>
        <div class="media-desc" :class="{ 'animate__animated animate__fadeIn': mediaVisible }">Only by clicking on this link to book a hotel can you enjoy discounted conference prices.</div>
        <div class="hotel-grid">
          <div class="hotel-card">
            <div class="hotel-image">
              <img src="@/assets/images/shanghaipudong.jpg" alt="香格里拉大酒店">
            </div>
            <div class="hotel-info">
              <h3>Pudong Shangri-La, Shanghai</h3>
              <!-- <p class="hotel-address">地址：上海市浦东新区富城路33号</p> -->
              <p class="hotel-desc">￥1,300/Per night including one breakfast</p>
              <p class="hotel-desc">￥1,450/Per night including two breakfasts</p>
              <div style="display: flex;justify-content: space-between;">

                <a class="hotel-link" href="https://www.shangri-la.com/shanghai/pudongshangrila/" target="_blank">Hotel Details</a>
                <a class="hotel-link" v-if="hotelMoney" href="https://www.paypal.com/ncp/payment/PQXNEDU8XDPZS" target="_blank">Online booking</a>
              </div>
              <!-- <el-button type="primary" v-if="hotelMoney" >在线预订</el-button> -->
            </div>
          </div>
          <div class="hotel-card">
            <div class="hotel-image">
              <img src="@/assets/images/yangguangdajiudian.jpg" alt="阳光大酒店">
            </div>
            <div class="hotel-info">
              <h3>Grand Soluxe Zhongyou Hotel</h3>
              <!-- <p class="hotel-address">地址：上海市静安区延安中路789号</p> -->
              <p class="hotel-desc">￥730/ Per night including two breakfasts</p>
               <div style="display: flex;justify-content: space-between;">
              <a class="hotel-link" :href="hotelPdfUrl" target="_blank">Hotel Details</a>
              <!-- <el-button type="primary" v-if="hotelMoney" @click="dialogVisible = true" >在线预订</el-button> -->
               <a class="hotel-link" style="cursor: pointer; " v-if="hotelMoney"  @click="dialogVisible = true">Online booking</a></div>
            </div>
          </div>
        </div>
      </div>
    </section>
    </div>
    <el-dialog
      title="Hotel Reservation Request"
      v-model="dialogVisible"
      width="600px"
      :before-close="handleClose"
      custom-class="reservation-dialog"
    >
      <!-- 弹框内容 -->
      <div class="dialog-content">
        <!-- 酒店信息头部 -->
        <div class="hotel-header">
          <div class="hotel-logo">
            <i class="el-icon-building"></i>
          </div>
          <div class="hotel-info">
            <h3 class="hotel-name">Grand Soluxe Zhongyou Hotel Shanghai</h3>
            <p class="conference-info">2025 CMC Conference Accommodation</p>
          </div>
        </div>
        
        <!-- 主要内容区域 -->
        <div class="reservation-message" style="padding:0 24px;">
          <p class="greeting">Dear Grand Soluxe Zhongyou Hotel Shanghai,</p>
          <p class="intro">I will participate in the 2025 CMC conference, and would like to reserve hotel guest rooms with the following details:</p>
          
          <ul class="requirements">
            <li class="requirement-item">
              <span class="item-number">1) </span>
              <span class="item-text">Number of rooms (king size bed or twin bed room)</span>
            </li>
            <li class="requirement-item">
              <span class="item-number">2) </span>
              <span class="item-text">Check-in date and check-out date</span>
            </li>
            <li class="requirement-item">
              <span class="item-number">3)  </span>
              <span class="item-text">Stay period must be guaranteed. Please fill in the attached form.</span>
            </li>
            <li class="requirement-item">
              <span class="item-number">4)  </span>
              <span class="item-text">Please send an email and attach a credit card authorization letter when booking a hotel, otherwise the reservation will be invalid</span>
            </li>
          </ul>
        </div>
        <div style="padding: 12px 24px;">
          <span>Booking Email：</span>
          <a href="mailto:<EMAIL>"><EMAIL></a> or <a href="mailto:<EMAIL>"><EMAIL></a>
        </div>
        <a  :href="Authorization" download="AuthorizationForm.docx" style="font-size: 18px;padding-left:24px;">Authorization form</a>        
        <div class="decorative-line"></div>
      </div>


    </el-dialog>
  </div>
</template>

<script setup>
import { onMounted, watch, ref } from 'vue'
import { useRoute } from 'vue-router'
import { getHotelInfo } from '@/api/agenda'
import { useUserStore } from "../../stores/user";
import { useLanguage } from '@/hooks/useLanguage'
import hotelPdf from '@/assets/downLoads/HotelDetails.pdf';
import AuthorizationForm from '@/assets/downLoads/AuthorizationForm.doc'

import nick_casual from '@/assets/images/vips/nick_casual.jpg'
import ruggero_picture from '@/assets/images/vips/ruggero_picture.jpg'
import celal from '@/assets/images/vips/celal.jpg'
import pic_of_norma_shorey from '@/assets/images/vips/pic_of_norma_shorey.jpg'
import gergana_mantarkova from '@/assets/images/vips/gergana-mantarkova-option-100x130-2.png'
import alan_blackman from '@/assets/images/vips/alan_blackman_-_photo.jpg'
import re5_9160 from '@/assets/images/vips/re5_9160.jpeg'
import img_5081_edited1 from '@/assets/images/vips/img_5081_edited1.png'
import img_7925 from '@/assets/images/vips/img_7925-1.jpg'

import zhongqilian2 from '@/assets/images/vips/zhongqilian2.png'
import zhongqilian3 from '@/assets/images/vips/zhongqilian3.png'
import media1 from '@/assets/images/media1.png'
import media2 from '@/assets/images/media2.png'
import junzhi from '@/assets/images/junzhi.png'
import liantong from '@/assets/images/liantong.png'
const route = useRoute()
const userStore = useUserStore();
const {getLocalePath} = useLanguage()

// 添加动画状态控制
const introVisible = ref(false)
const themeVisible = ref(false)
const highlightsVisible = ref(false)
const speakersVisible = ref(false)
const forumVisible = ref(false)
const mediaVisible = ref(false)
const partnersVisible = ref(false)
const hotelPdfUrl = ref(hotelPdf)
const Authorization = ref(AuthorizationForm)
const dialogVisible = ref(false)
// 处理各个部分的可见性
const handleIntroIntersection = (isVisible) => {
  if (isVisible && !introVisible.value) {
    introVisible.value = true
  }
}

const handleThemeIntersection = (isVisible) => {
  if (isVisible && !themeVisible.value) {
    themeVisible.value = true
  }
}

const handleHighlightsIntersection = (isVisible) => {
  if (isVisible && !highlightsVisible.value) {
    highlightsVisible.value = true
  }
}

const handleSpeakersIntersection = (isVisible) => {
  if (isVisible && !speakersVisible.value) {
    speakersVisible.value = true
  }
}

const handleForumIntersection = (isVisible) => {
  if (isVisible && !forumVisible.value) {
    forumVisible.value = true
  }
}

const handleMediaIntersection = (isVisible) => {
  if (isVisible && !mediaVisible.value) {
    mediaVisible.value = true
  }
}

const handlePartnersIntersection = (isVisible) => {
  if (isVisible && !partnersVisible.value) {
    partnersVisible.value = true
  }
}

// 添加自定义指令
const vIntersectionObserver = {
  mounted(el, binding) {
    if (typeof binding.value !== 'function') {
      console.warn('v-intersection-observer 指令需要一个函数作为值')
      return
    }

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          try {
            binding.value(true)
          } catch (error) {
            console.error('执行 intersection observer 回调时出错:', error)
          }
        }
      })
    }, {
      threshold: 0.1,
      rootMargin: '0px'
    })

    try {
      observer.observe(el)
    } catch (error) {
      console.error('观察元素时出错:', error)
    }
  },
  unmounted(el) {
    // 清理 observer
    if (el._observer) {
      el._observer.disconnect()
    }
  }
}

function scrollToAnchor() {
  const anchor = route.query.anchor
  if (anchor) {
    setTimeout(() => {
      const el = document.getElementById(anchor)
      if (el) {
        el.scrollIntoView({ behavior: 'smooth' })
      }
    }, 300)
  }
}
 const getSpeakerImage = (index) => {
      const images = [
      zhongqilian2,
      zhongqilian3,
        nick_casual,
        ruggero_picture,
        celal,
        pic_of_norma_shorey,
        gergana_mantarkova,
        alan_blackman,
        re5_9160,
        img_5081_edited1,
        img_7925,
      

      ]
      return images[index % images.length]
    }
const hotelMoney = ref(false)

const partnerItems = [
  {
    icon: 'fas fa-handshake',
    img:junzhi,
    title: 'Kmind Strategic Consulting',
    desc: 'Kmind Strategic Consulting, with offices in Shanghai and Shenzhen, aids enterprises in market leadership. It has served nearly 100 firms, with 20+ long-term clients. Nine cases entered Harvard’s library; it’s a top Asia-Pacific innovative consultant.'
  },
  {
    icon: 'fas fa-globe-asia',
    img:liantong,
    title: 'China Unicom',
    desc: "China United Network Communications Group Co., Ltd. (China Unicom) was established in 2009 by merging former China Netcom and China Unicom. Listed in New York, Hong Kong and Shanghai, it serves globally. With diverse services like 5G, broadband, it's a top global telecom operator in the Fortune 500. "
  },
  // {
  //   icon: 'fas fa-building',
  //   title: 'Foreign Investment Facilitation',
  //   desc: 'Providing market access and investment consulting services for foreign enterprises'
  // },
  // {
  //   icon: 'fas fa-chart-line',
  //   title: 'Professional Trade Services',
  //   desc: 'Offering professional trade promotion and commercial legal services'
  // }
]
const highLightsList = [
  {
    label:'Promote international exchanges and cooperation',
    desc:'It provides a high-end platform for industry experts, scholars and enterprises from all over the world to communicate face-to-face. This direct communication helps to break the geographical and cultural boundaries and promote the collision and integration of global management wisdom. Participants can have in-depth discussions on the latest management concepts, technological innovations, market trends and other topics, and work together to find solutions to global challenges, thereby strengthening international cooperation and partnership.'

  },
  {
    label:'Deepen market insights and explore opportunities',
    desc:"As one of the most dynamic markets in the world, China's large consumer base, rapid technological iterations, and escalating consumer demand provide a broad space for international companies to develop. We hope to provide participants with a closer look at the Chinese market and an in-depth understanding of its unique business environment, consumer behavior and policy orientation, so that they can accurately grasp the pulse of the market and explore potential business opportunities."
  },
  {
    label:'Promote cultural integration and understanding',
    desc:"The event is also a grand cultural exchange event. Participants from different countries and regions will bring their unique cultural backgrounds and ways of thinking while discussing management wisdom together. Such cross-cultural exchanges help to enhance mutual understanding and respect, and promote global cultural diversity and inclusiveness."
  }

]
const MediaList = ref([
  {
    title:'企业管理杂志社',
    img:media2,
    desc:''
  },
  {
    title:'企业管理出版社',
     img:media1,
    desc:''
  }
])
  const getSpeakerTitle = (index) => {
      const titles = [
      'Zhu Hongren',
      'Li Bing',
        'Nick Warn, CMC',
        'Ruggero Huesler, CMC',
        'Celal Seckin, CMC',
        'Norma Shorey, CMC',
        'Gergana Mantarkova, CMC',
        'Alan Blackman, CMC',
        'Robert Bodenstein, CMC',
        'Reema Nasser',
        'Khuzaima Zaghlawan',
   
      ]
      return titles[index % titles.length]
    }

    const getSpeakerBio = (index) => {
      const bios = [
        'Executive Vice President, and Secretary General of  China Enterprise Confederation',
        'Vice President of China Enterprise Confederation',
        'United Kingdom Chair',
        'Switzerland Secretary',
        'Turkey Director',
        'Caribbean Director',
        'Bulgaria Director',
        'Australia Director',
        'Austria Immediate Past Chair',
        'Jordan Executive Director',
        'Jordan Executive Secretary',
        '领导',
        '朱宏任',
        '李冰'
      ]
      return bios[index % bios.length]
    }
onMounted(() => {
  scrollToAnchor()
  // debugger
  if(userStore.userInfo.account){
    getHotelInfo(userStore.userInfo.account).then(res=>{
      console.log(res)
      hotelMoney.value = res.data.data;
    })
  }
})

watch(() => route.query.anchor, (newAnchor) => {
  if (newAnchor) {
    scrollToAnchor()
  }
})

</script>

<style scoped>
.conference-overview-container{
  background: url('@/assets/images/home-bg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  min-height: 100vh;
  
}
/* 添加基础样式确保不会出现横向滚动 */
.about-page {
  width: 100%;
  overflow-x: hidden;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  width: 100%;
  box-sizing: border-box;
}

/* 修改网格布局，确保响应式布局不会溢出 */
.intro-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 50px;
  align-items: flex-start;
  width: 100%;
}

.theme-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
  width: 100%;
}

.highlights-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  margin-top: 50px;
  width: 100%;
}

.speakers-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  padding: 10px 0;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.forum-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  width: 100%;
}

.media-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* 确保图片不会导致溢出 */
img {
  max-width: 100%;
  height: auto;
}

/* 修改响应式布局断点 */
@media (max-width: 1200px) {
  .container {
    padding: 0 15px;
  }
  
  .theme-grid,
  .highlights-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 992px) {
  .intro-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .speakers-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .media-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .theme-grid,
  .highlights-grid,
  .speakers-grid,
  .media-grid {
    grid-template-columns: 1fr;
  }
  
  .hotel-card {
    flex-direction: column;
  }
  
  .hotel-image {
    width: 100%;
    height: 200px;
  }
}

/* 确保所有元素都使用border-box盒模型 */
* {
  box-sizing: border-box;
}

.more-speakers{
  text-align: right;
  cursor: pointer;
  max-width: 1200px;
  margin: 0 auto;
  font-size: 14px;
  color: var(--primary-color);
}
.bg{
  /* background-color: var(--bg-light); */
}
.page-header.about-header {
  background: linear-gradient(rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.3)), url('@/assets/images/dahui.png');
  background-size: cover;
  background-position: center;

  height: 200px;
  text-align: center;
  margin-top: 80px;
}

.page-header h1 {
  font-size: 36px;
  margin-bottom: 10px;
}

.breadcrumbs {
  font-size: 16px;
  opacity: 0.8;
}

.breadcrumbs a {
  color: var(--text-white);
  text-decoration: none;
}

.breadcrumbs a:hover {
  text-decoration: underline;
}

.about-intro {
  padding: 60px 0;
  margin-top: 0;
}

.intro-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 50px;
   align-items: flex-start;
}

.intro-content h2 {
  font-size: 32px;
  margin-bottom: 10px;
  color: var(--primary-color);
}

.intro-content .lead {
  font-size: 18px;
  margin-bottom: 10px;
  color: var(--text-light);
}

.intro-content p {
  margin-bottom: 30px;
}

.key-info {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
}

.info-item i {
  font-size: 24px;
  color: var(--primary-color);
  margin-top: 5px;
}

.info-content h4 {
  font-size: 18px;
  margin-bottom: 5px;
}

.intro-image img {
  width: 100%;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
}

.about-theme {
  padding: 60px 0;
  margin-top: 0;
}

.theme-desc {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 50px;
  color: var(--text-light);
   font-size: 24px;
}

.theme-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
}

.theme-item {

  border-radius: var(--radius-md);
  padding: 30px;
  /* box-shadow: var(--shadow-md); */
  text-align: left;
  transition: var(--transition);
}

.theme-item:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-lg);
}

.theme-icon {
   /*width: 70px;*/
  height: 70px;
  border-radius: 50%;
  /* background-color: rgba(30, 136, 229, 0.1);*/
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
}

.theme-icon i {
  font-size: 30px;
  color: var(--primary-color);
}

.theme-item h3 {
  font-size: 20px;
  margin-bottom: 15px;
}

.theme-item p {
  color: var(--text-light);
}

.about-organization {
  padding: 60px 0;
  margin-top: 0;
}

/* 会议亮点样式 */
.about-highlights {
  padding: 60px 0;
  margin-top: 0;
}

.highlights-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  margin-top: 50px;
}

.highlight-card {
  background-color: var(--bg-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  padding: 30px;
  text-align: center;
  transition: var(--transition);
}

.highlight-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-lg);
}

.highlight-icon {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background-color: rgba(30, 136, 229, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
}

.highlight-icon i {
  font-size: 30px;
  color: var(--primary-color);
}

.highlight-card h3 {
  font-size: 20px;
  margin-bottom: 15px;
  color: var(--primary-color);
}

.highlight-card p {
  color: var(--text-light);
  line-height: 1.6;
  text-align: left;
}

/* 组织机构样式 */
.organization-section {
  padding: 60px 0;
  margin-top: 0;
}

.section-title {
  font-size: 32px;
  text-align: center;
  margin-bottom: 40px;
  color: var(--primary-color);
  position: relative;
  padding-bottom: 15px;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background-color: var(--primary-color);
}

.org-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
}

.org-card {
  background-color: var(--bg-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  padding: 30px;
}

.org-card h3 {
  font-size: 20px;
  margin-bottom: 20px;
  color: var(--primary-color);
  text-align: center;
  position: relative;
  padding-bottom: 10px;
}

.org-card h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 2px;
  background-color: var(--primary-color);
}

.org-card ul {
  list-style: none;
  padding: 0;
}

.org-card li {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  gap: 15px;
}

.org-card li:last-child {
  margin-bottom: 0;
}

.org-card img {
  width: 80px;
  height: auto;
  border-radius: var(--radius-sm);
  transition: var(--transition);
}

.org-card span {
  flex: 1;
  font-size: 16px;
  color: var(--text-color);
}

.about-history {
  padding: 60px 0;
  margin-top: 0;
}

.history-timeline {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
}

.history-timeline::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 120px;
  width: 2px;
  background-color: var(--border-color);
}

.timeline-item {
  display: flex;
  margin-bottom: 50px;
  position: relative;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-year {
  width: 100px;
  font-size: 24px;
  font-weight: 700;
  color: var(--primary-color);
  text-align: right;
  padding-right: 30px;
  flex-shrink: 0;
}

.timeline-content {
  position: relative;
  background-color: var(--bg-color);
  border-radius: var(--radius-md);
  padding: 30px;
  box-shadow: var(--shadow-md);
  margin-left: 40px;
}

.timeline-content::before {
  content: '';
  position: absolute;
  left: -40px;
  top: 20px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: var(--primary-color);
  border: 4px solid var(--bg-color);
  box-shadow: var(--shadow-sm);
}

.timeline-content h3 {
  font-size: 20px;
  margin-bottom: 10px;
}

.timeline-content p {
  color: var(--text-light);
  margin-bottom: 5px;
}

@media (max-width: 992px) {
  .theme-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .org-grid {
    grid-template-columns: 1fr;
  }

  .highlights-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .intro-grid {
    grid-template-columns: 1fr;
  }

  .key-info {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .theme-grid {
    grid-template-columns: 1fr;
  }

  .history-timeline::before {
    left: 20px;
  }

  .timeline-year {
    width: 60px;
    font-size: 18px;
    padding-right: 20px;
  }

  .timeline-content {
    margin-left: 20px;
  }

  .timeline-content::before {
    left: -30px;
    width: 16px;
    height: 16px;
  }
}

@media (max-width: 768px) {
  .contact-grid {
    grid-template-columns: 1fr;
  }

  .highlights-grid {
    grid-template-columns: 1fr;
  }
}

.speakers-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  padding: 10px 0;
  max-width: 1200px;
  margin: 0 auto;
}

.speaker-card {
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.speaker-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.15);
}

.speaker-image {
  position: relative;
  width: 100%;
  overflow: hidden;
  height: 470px;
}

.speaker-image img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.speaker-card:hover .speaker-image img {
  transform: scale(1.1);
}

.image-container.image-placeholder {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

.speaker-info {
  padding: 24px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.speaker-info h3 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.speaker-title {
  font-size: 16px;
  color: var(--primary-color);
  margin-bottom: 12px;
  font-weight: 500;
}

.speaker-bio {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
  flex-grow: 1;
}

.speaker-social {
  display: flex;
  gap: 12px;
}

.speaker-social a {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  transition: all 0.3s ease;
}

.speaker-social a:hover {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}
.footer-image-item{
  margin:0 8px;
}
@media (max-width: 1024px) {
  .speakers-grid {
    grid-template-columns: repeat(2, 1fr);
    padding: 30px 20px;
  }
}

@media (max-width: 768px) {
  .speakers-grid {
    grid-template-columns: 1fr;
    padding: 20px;
  }

  .speaker-info h3 {
    font-size: 20px;
  }

  .speaker-title {
    font-size: 14px;
  }

  .speaker-bio {
    font-size: 13px;
  }
}


.forum-page {
  min-height: 100vh;
  
}

/* .page-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, #1565c0 100%);
  color: var(--text-white);
  padding: 120px 0 60px;
  text-align: center;
} */

.page-header h1 {
  font-size: 36px;
  margin-bottom: 10px;
}

.forum-section {
  padding: 80px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.forum-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.forum-card {
  background: var(--bg-color);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: transform 0.3s ease;
}

.forum-card:hover {
  transform: translateY(-5px);
}

.forum-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.forum-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.forum-content {
  padding: 20px;
}

.forum-content h2 {
  font-size: 20px;
  margin-bottom: 15px;
  color: var(--primary-color);
}

.forum-content p {
  color: var(--text-light);
  margin-bottom: 20px;
  line-height: 1.6;
}

.forum-details {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--text-light);
}

.detail-item i {
  color: var(--primary-color);
}

@media (max-width: 768px) {
  .forum-grid {
    grid-template-columns: 1fr;
  }
  
  .page-header {
    padding: 40px 0 40px;
  }
  
  .page-header h1 {
    font-size: 28px;
  }
}

.about-hotel {
  padding: 60px 0;
  margin-top: 0;
}
.hotel-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
}
.hotel-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.08);
  overflow: hidden;
  display: flex;
  flex-direction: row;
}
.hotel-image {
  width: 240px;
  height: 100%;
  flex-shrink: 0;
  overflow: hidden;
}
.hotel-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.hotel-info {
  padding: 24px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.hotel-info h3 {
  font-size: 22px;
  margin-bottom: 10px;
  color: var(--primary-color);
}
.hotel-address {
  font-size: 15px;
  color: #888;
  margin-bottom: 10px;
}
.hotel-desc {
  font-size: 15px;
  color: #555;
  margin-bottom: 12px;
}
.hotel-link {
  color: var(--primary-color);
  text-decoration: underline;
  font-size: 15px;
  margin-top: 8px;
}
@media (max-width: 900px) {
  .hotel-grid {
    grid-template-columns: 1fr;
  }
  .hotel-card {
    flex-direction: column;
  }
  .hotel-image {
    width: 100%;
    height: 180px;
  }
}

.media-support {
  padding: 60px 0;
  margin-top: 0;
}

.media-desc {
  text-align: center;
  color: #666;
  margin-bottom: 40px;
  font-size: 22px;
}

.media-grid {
  display: grid;
  grid-template-columns: repeat(2, 400px);
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
  justify-content: center;
}

.media-card {
  /* background: #fff; */
  border-radius: 12px;
  padding: 30px;
  text-align: center;
  transition: all 0.3s ease;
  /* border: 1px solid #eee; */
}

.media-card:hover {
  transform: translateY(-5px);
  /* box-shadow: 0 10px 20px rgba(0,0,0,0.1); */
}

.media-icon {
  width: 60px;
  height: 60px;
  margin: 0 auto 20px;
  background: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.media-icon i {
  font-size: 24px;
  color: #fff;
}

.media-info h3 {
  font-size: 20px;
  color: #333;
  margin-bottom: 10px;
}

.media-info p {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
}

.media-tags {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.media-tags span {
  padding: 4px 12px;
  background: rgba(30, 136, 229, 0.1);
  color: var(--primary-color);
  border-radius: 20px;
  font-size: 12px;
}

@media (max-width: 1024px) {
  .media-grid {
    grid-template-columns: repeat(2, 1fr);
    padding: 0 20px;
  }
}

@media (max-width: 640px) {
  .media-grid {
    grid-template-columns: 1fr;
  }
}

/* 修改动画相关样式 */
.animate__animated {
  animation-duration: 1s;
  animation-fill-mode: both;
}

.animate__animated.animate__fadeInDown {
  animation-name: fadeInDown;
}

.animate__animated.animate__fadeInUp {
  animation-name: fadeInUp;
}

.animate__animated.animate__fadeInLeft {
  animation-name: fadeInLeft;
}

.animate__animated.animate__fadeInRight {
  animation-name: fadeInRight;
}

.animate__animated.animate__fadeIn {
  animation-name: fadeIn;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translate3d(0, -100%, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 100%, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translate3d(-100%, 0, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translate3d(100%, 0, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 确保元素在动画前是隐藏的 */
.section-title:not(.animate__animated),
.intro-content:not(.animate__animated),
.intro-image:not(.animate__animated),
.theme-item:not(.animate__animated),
.highlight-card:not(.animate__animated),
.speaker-card:not(.animate__animated),
.forum-card:not(.animate__animated),
.media-card:not(.animate__animated) {
  opacity: 0;
}

/* 确保元素在动画后保持可见 */
.animate__animated {
  opacity: 1 !important;
}

/* 为锚点目标添加负margin */
[id] {
  scroll-margin-top: 80px;
}

/* 其他现有样式保持不变 */
</style>
