<template>
  <el-form-item :label="label" :prop="prop" :required="required">
    <el-upload
      class="upload-demo"
      :http-request="customUpload"
      :before-upload="beforeUpload"
      :show-file-list="true"
      :file-list="fileList"
      :on-remove="handleRemove"
      :limit="5"
      :on-exceed="handleExceed"
      list-type="picture-card"
      :disabled="uploadLoading"
    >
      <el-icon v-if="!uploadLoading"><Plus /></el-icon>
      <el-icon v-else class="is-loading"><Loading /></el-icon>
      <template #tip>
        <div class="el-upload__tip">
          {{ currentLanguage === 'chinese' ? '请上传JPG/PNG格式的照片，大小不超过2MB，最多上传5张' : 'Please upload photos in JPG/PNG format, with a size not exceeding 2MB and a maximum of 5 images' }}
          
        </div>
      </template>
      <template #file="{ file }">
        <div>
          <img class="el-upload-list__item-thumbnail" :src="file.url" :alt="file.name" />
          <span class="el-upload-list__item-actions">
            <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
              <el-icon><ZoomIn /></el-icon>
            </span>
            <span class="el-upload-list__item-delete" @click="handleRemove(file)">
              <el-icon><Delete /></el-icon>
            </span>
          </span>
        </div>
      </template>
    </el-upload>

    <!-- 图片预览对话框 -->
    <el-dialog v-model="dialogVisible" title="图片预览">
      <img w-full :src="dialogImageUrl" alt="Preview Image" />
    </el-dialog>
  </el-form-item>
</template>

<script>
import { ref, computed ,watch} from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Loading, ZoomIn, Delete } from '@element-plus/icons-vue'
import http from '@/utils/http'
import { useLanguage } from '@/hooks/useLanguage.js'
export default {
  name: 'ImageUpload',
  components: {
    Plus,
    Loading,
    ZoomIn,
    Delete
  },
  props: {
    label: {
      type: String,
      default: '护照照片'
    },
    prop: {
      type: String,
      required: true
    },
    required: {
      type: Boolean,
      default: false
    },
    modelValue: {
      type: String,
      default: ''
    }
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const {  currentLanguage } = useLanguage()
    const fileList = ref([])
    const uploadLoading = ref(false)
    const dialogVisible = ref(false)
    const dialogImageUrl = ref('')

    // 处理超出文件数量限制
    const handleExceed = (files) => {
      ElMessage.warning('最多只能上传5张照片')
    }

    // 获取完整的图片URL
    const getFullImageUrl = (path) => {
      if (!path) return ''
      // 如果是线上环境，使用当前地址栏的地址
      if (import.meta.env.PROD) {
        const baseUrl = window.location.origin
        return `${baseUrl}${path}`
      }
      // 开发环境使用环境变量配置的地址
      return `${import.meta.env.VITE_APP_imgUrl}${path}`
    }

    // 自定义上传方法
    const customUpload = async (options) => {
      const { file } = options
      const formData = new FormData()
      formData.append('file', file)

      try {
        uploadLoading.value = true
        const response = await http.post('/cms/article/upload', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })

        if (response.code === 1000) {
          const newFile = {
            name: file.name,
            url: response.data.data
          }
          fileList.value.push(newFile)
          // 只发送图片URL数组
          const urls = fileList.value.map(file => file.url)
          emit('update:modelValue', urls)
          ElMessage.success('照片上传成功')
        } else {
          ElMessage.error(response.message || '上传失败')
        }
      } catch (error) {
        console.error('上传失败:', error)
        ElMessage.error('照片上传失败，请重试')
      } finally {
        uploadLoading.value = false
      }
    }

    // 上传前的验证
    const beforeUpload = (file) => {
      const isImage = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isImage) {
        ElMessage.error('只能上传JPG/PNG格式的图片!')
        return false
      }
      if (!isLt2M) {
        ElMessage.error('图片大小不能超过2MB!')
        return false
      }
      return true
    }

    // 移除文件
    const handleRemove = (file) => {
      const index = fileList.value.findIndex(item => item.url === file.url)
      if (index !== -1) {
        fileList.value.splice(index, 1)
        // 只发送图片URL数组
        const urls = fileList.value.map(file => file.url)
        emit('update:modelValue', urls)
      }
    }

    // 图片预览
    const handlePictureCardPreview = (file) => {
      dialogImageUrl.value = file.url
      dialogVisible.value = true
    }

    // 监听 modelValue 变化，更新 fileList
    watch(() => props.modelValue, (newVal) => {
      if (newVal && Array.isArray(newVal)) {
        fileList.value = newVal.map(url => ({
          name: '已上传图片',
          url: url
        }))
      } else {
        fileList.value = []
      }
    }, { immediate: true })

    return {
      fileList,
      uploadLoading,
      dialogVisible,
      dialogImageUrl,
      customUpload,
      beforeUpload,
      handleRemove,
      handlePictureCardPreview,
      handleExceed,
      currentLanguage
    }
  }
}
</script>

<style scoped>
:deep(.el-upload--picture-card) {
  width: 100px;
  height: 100px;
  line-height: 100px;
}

:deep(.el-upload__tip) {
  font-size: 12px;
  color: var(--text-light);
  margin-top: 5px;
}

:deep(.is-loading) {
  animation: rotating 2s linear infinite;
}

:deep(.el-upload-list__item-thumbnail) {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

:deep(.el-upload-list__item-actions) {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  cursor: default;
  text-align: center;
  color: #fff;
  opacity: 0;
  font-size: 20px;
  background-color: rgba(0, 0, 0, 0.5);
  transition: opacity .3s;
}

:deep(.el-upload-list__item:hover .el-upload-list__item-actions) {
  opacity: 1;
}

:deep(.el-upload-list__item-actions span) {
  display: inline-block;
  margin: 0 5px;
  cursor: pointer;
}

:deep(.el-dialog__body img) {
  width: 100%;
  height: auto;
  max-height: 70vh;
  object-fit: contain;
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style> 