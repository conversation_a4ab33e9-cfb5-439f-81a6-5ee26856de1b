import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useRegistrationStore = defineStore('registration', () => {
  const registrationId = ref(null)
  const registrationInfo = ref(null)
  const companionId = ref(null)

  const setRegistrationId = (id) => {
    console.log('Setting registrationId:', id)
    registrationId.value = id
  }
  const setCompanionId = (id) => {
    console.log('Setting registrationId:', id)
    companionId.value = id
  }
  const setRegistrationInfo = (item) =>{
    registrationInfo.value = item;
  }
  const clearRegistrationId = () => {
    console.log('Clearing registrationId')
    registrationId.value = null
  }

  return {
    registrationId,
    setRegistrationId,
    clearRegistrationId,
    setRegistrationInfo,
    registrationInfo,
    setCompanionId,
    companionId
  }
}, {
  persist: true
}) 