<template>
  <div class="dashboard-page">
    <!-- 页面头部 -->
    <!-- <section class="page-header">
      <div class="container">
        <h1>用户中心</h1>
        <p>管理您的会议信息和资料</p>
      </div>
    </section> -->

    <!-- 仪表盘内容 -->
     <div class="conference-overview-container">
    <section class="dashboard-section" style="margin-top: 20px;">
      <div class="container">
        <div class="dashboard-container">
          <!-- 侧边栏 -->
          <div class="dashboard-sidebar">
            <div class="user-profile">
              <!-- <div class="user-avatar">
                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=crop&w=100&q=80" alt="用户护照照片">
              </div> -->
              <div class="user-info">
                <h3>{{ user.name }}</h3>
                <p>{{ user.email }}</p>
              </div>
            </div>

            <nav class="dashboard-nav">
              <ul>
                <li v-for="(item, index) in navItems" :key="index">
                  <a href="#" :class="{ 'active': activeTab === item.id }" @click.prevent="activeTab = item.id">
                    <i :class="item.icon"></i>
                    <span>{{ item.name }}</span>
                  </a>
                </li>
              </ul>
            </nav>

            <div class="sidebar-footer">
              <button class="btn btn-outline btn-block" @click="handleLogout">
                <i class="fas fa-sign-out-alt"></i> 退出登录
              </button>
            </div>
          </div>

          <!-- 主内容区 -->
          <div class="dashboard-content">
            <!-- 概览 -->
            <div v-if="activeTab === 'overview'" class="dashboard-tab-content">
              <h2>概览</h2>

              <div class="overview-cards">
                <div class="overview-card">
                  <div class="card-icon">
                    <i class="fas fa-calendar-check"></i>
                  </div>
                  <div class="card-content">
                    <h3>报名状态</h3>
                    <p class="status-confirmed">已确认</p>
                  </div>
                </div>

                <div class="overview-card">
                  <div class="card-icon">
                    <i class="fas fa-credit-card"></i>
                  </div>
                  <div class="card-content">
                    <h3>支付状态</h3>
                    <p class="status-confirmed">已支付</p>
                  </div>
                </div>

                <div class="overview-card">
                  <div class="card-icon">
                    <i class="fas fa-ticket-alt"></i>
                  </div>
                  <div class="card-content">
                    <h3>电子票</h3>
                    <p>可下载</p>
                    <a href="#" class="card-action">下载电子票</a>
                  </div>
                </div>

                <div class="overview-card">
                  <div class="card-icon">
                    <i class="fas fa-file-invoice"></i>
                  </div>
                  <div class="card-content">
                    <h3>电子发票</h3>
                    <p>已开具</p>
                    <a href="#" class="card-action">下载发票</a>
                  </div>
                </div>
              </div>

              <div class="upcoming-events">
                <h3>即将到来的日程</h3>
                <div class="event-timeline">
                  <div class="timeline-item">
                    <div class="timeline-date">
                      <div class="date-day">22</div>
                      <div class="date-month">10月</div>
                    </div>
                    <div class="timeline-content">
                      <h4>开幕式及主旨演讲</h4>
                      <p>09:00 - 10:30 | 上海国际会议中心 - 主会场</p>
                    </div>
                  </div>

                  <div class="timeline-item">
                    <div class="timeline-date">
                      <div class="date-day">22</div>
                      <div class="date-month">10月</div>
                    </div>
                    <div class="timeline-content">
                      <h4>数字化转型与管理咨询的未来</h4>
                      <p>11:00 - 12:30 | 上海国际会议中心 - 主会场</p>
                    </div>
                  </div>

                  <div class="timeline-item">
                    <div class="timeline-date">
                      <div class="date-day">23</div>
                      <div class="date-month">10月</div>
                    </div>
                    <div class="timeline-content">
                      <h4>全球管理咨询趋势报告发布</h4>
                      <p>09:00 - 10:30 | 上海国际会议中心 - 主会场</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 个人信息 -->
            <div v-if="activeTab === 'profile'" class="dashboard-tab-content">
              <h2>个人信息</h2>
              <el-form ref="profileFormRef" :model="profileForm" :rules="profileRules" label-width="100px">
                <el-form-item label="姓" prop="firstName" required>
                  <el-input v-model="profileForm.firstName" />
                </el-form-item>
                <el-form-item label="名" prop="lastName" required>
                  <el-input v-model="profileForm.lastName" />
                </el-form-item>

                <el-form-item label="邮箱" prop="email" required>
                  <el-input v-model="profileForm.email" disabled />
                </el-form-item>
                <el-form-item label="国籍" prop="area" required>
                  <el-select v-model="profileForm.area" placeholder="请选择国籍">
                    <el-option v-for="item in countryOptions" :key="item.value" :label="item.label"
                      :value="item.value" />
                  </el-select>
                </el-form-item>
                <el-form-item label="手机号" prop="telephone" required>
                  <el-input v-model="profileForm.telephone" />
                </el-form-item>
                <el-form-item label="组织" prop="organization" required>
                  <el-input v-model="profileForm.organization" />
                </el-form-item>
                <el-form-item label="职位" prop="position" required>
                  <el-input v-model="profileForm.position" />
                </el-form-item>
                <el-form-item label="地址" prop="addr" required>
                  <el-input v-model="profileForm.addr" type="textarea" :rows="3" />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="handleUpdateProfile" :loading="isLoading">
                    保存修改
                  </el-button>
                </el-form-item>
              </el-form>
            </div>
            <!-- 修改密码 -->
            <div v-if="activeTab === 'password'" class="dashboard-tab-content">
              <h2>修改密码</h2>
              <el-form ref="passwordFormRef" :model="passwordForm" :rules="passwordRules" label-width="100px">
                <el-form-item label="当前密码" prop="currentPassword">
                  <el-input v-model.trim="passwordForm.currentPassword" type="password" show-password />
                </el-form-item>
                <el-form-item label="新密码" prop="newPassword">
                  <el-input v-model.trim="passwordForm.newPassword" type="password" show-password />
                </el-form-item>
                <el-form-item label="确认密码" prop="confirmPassword">
                  <el-input v-model.trim="passwordForm.confirmPassword" type="password" show-password />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="handleChangePassword" :loading="isLoading"
                    >
                    修改密码
                  </el-button>
                </el-form-item>
              </el-form>
              
            </div>
            <!-- 我的报名 -->
            <div v-if="activeTab === 'registrations'" class="dashboard-tab-content">
              <Profile></Profile>
            </div>

            <!-- <div v-if="activeTab === 'payments'" class="dashboard-tab-content">
              <h2>支付记录</h2>
              <p>这里将显示您的支付记录。</p>
            </div>
            
            <div v-if="activeTab === 'materials'" class="dashboard-tab-content">
              <h2>会议资料</h2>
              <p>这里将显示会议资料和下载链接。</p>
            </div>
            
            <div v-if="activeTab === 'settings'" class="dashboard-tab-content">
              <h2>账户设置</h2>
              <p>这里将显示账户设置选项。</p>
            </div> -->
          </div>
        </div>
      </div>
    </section>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { logout } from '../../api/auth'
import { getRegistrationInfo } from '../../api/registration'
import { useUserStore } from '../../stores/user'
import { ElMessage } from 'element-plus'
import ChangePassword from '@/components/user/ChangePassword.vue'
import RegistrationInfo from '@/components/registration/RegistrationInfo.vue'
import Profile from './Profile.vue'
import { getDictCode } from '@/api/dictCode'
import { useLanguage } from '@/hooks/useLanguage'
export default {
  name: 'DashboardPage',
  components: {
    ChangePassword,
    RegistrationInfo,
    Profile
  },
  setup() {
    const router = useRouter()
    const activeTab = ref('profile')
    const userStore = useUserStore()
    const { getLocalePath }= useLanguage()
    // console.log(userStore.userInfo,'34')
    const user = ref({
      name: userStore.userInfo.firstName + ' ' + userStore.userInfo.lastName,
      email: userStore.userInfo.email
    })

    const profileForm = ref({
      name: '',
      email: '',
      phone: '',
      company: '',
      position: '',
      area: ''
    })

    const passwordForm = ref({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    })
    const passwordFormRef = ref(null)
    const profileFormRef = ref(null)
    const registrationActiveTab = ref('main')
    const activeCompanions = ref([])
    const isPasswordFormValid = ref(false)

    const profileRules = {
      firstName: [
        { required: true, message: '请输入姓', trigger: 'blur' }
      ],
      lastName: [
        { required: true, message: '请输入名', trigger: 'blur' }
      ],
      email: [
        { required: true, message: '请输入邮箱', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
      ],
      telephone: [
        { required: true, message: '请输入手机号', trigger: 'blur' },

      ],
      organization: [
        { required: true, message: '请输入组织', trigger: 'blur' }
      ],
      position: [
        { required: true, message: '请输入职位', trigger: 'blur' }
      ],
      area: [
        { required: true, message: '请选择国籍', trigger: 'change' }
      ],
      addr: [
        { required: true, message: '请输入地址', trigger: 'blur' }
      ]
    }

    const passwordRules = {
      currentPassword: [
        { required: true, message: '请输入当前密码', trigger: 'blur' }
      ],
      newPassword: [
        { required: true, message: '请输入新密码', trigger: 'blur' },
        { min: 8, message: "密码长度不能小于8位", trigger: "blur" },
        {
          pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*[~!@#$%^&*()_+`\-={}:";'<>?,./]).{8,}$/,
          message: "密码必须包含数字、大小写字母和特殊符号",
          trigger: "blur"
        }
      ],
      confirmPassword: [
        { required: true, message: '请确认新密码', trigger: 'blur' },
        {
          validator: (rule, value, callback) => {
            if (value !== passwordForm.value.newPassword) {
              callback(new Error('两次输入的密码不一致'))
            } else {
              callback()
            }
          },
          trigger: 'blur'
        }
      ]
    }

    const registrationInfo = ref({
      name: '',
      email: '',
      phone: '',
      company: '',
      position: '',
      meetingType: '',
      status: '',
      createTime: ''
    })
    const isLoading = ref(false)
    const companionInfo = ref([])

    const paymentInfo = ref({
      orderNo: '',
      orderAmtPayable: 0,
      orderPaidAmt: 0,
      paymentStatus: '',
      createTime: '',
      paymentTime: ''
    })

    const navItems = [
      // { id: 'overview', name: '概览', icon: 'fas fa-home' },
      { id: 'profile', name: '个人信息', icon: 'fas fa-user' },
      { id: 'password', name: '修改密码', icon: 'fas fa-user' },
      { id: 'registrations', name: '我的报名', icon: 'fas fa-calendar-check' },
      // { id: 'payments', name: '支付记录', icon: 'fas fa-credit-card' },
      // { id: 'materials', name: '会议资料', icon: 'fas fa-file-alt' },
      // { id: 'settings', name: '账户设置', icon: 'fas fa-cog' }
    ]

    const countryOptions = ref([])

    // 获取用户信息
    const getUserInfo = () => {
      Object.assign(profileForm.value, userStore.userInfo)
      // console.log(userStore.userInfo)
    }
    const updateProfile = async () => {
      // 模拟更新个人信息
      alert('个人信息更新成功！')
    }

    const resetProfile = () => {
      // 重置表单
      profileForm.value = { ...user.value }
    }

    const updatePassword = () => {
      // 模拟更新密码
      if (passwordForm.value.newPassword !== passwordForm.value.confirmPassword) {
        alert('两次输入的密码不一致')
        return
      }


      // 清空密码表单
      passwordForm.value = {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      }
    }

    const handleLogout = async () => {
      await userStore.logoutUser()
      // closeMobileMenu()
      router.push(getLocalePath('home'))
    }

    const getMeetingTypeName = (type) => {
      const types = {
        'full': '全程参会',
        'single': '单日参会',
        'student': '学生票'
      }
      return types[type] || type
    }

    const getStatusName = (status) => {
      const statuses = {
        'meeting_status_001': '已报名，未缴费',
        'meeting_status_002': '已报名，已缴费',
        'meeting_status_003': '已取消'
      }
      return statuses[status] || status
    }

    const getPaymentStatusName = (status) => {
      const statuses = {
        'payment_status_001': '未支付',
        'payment_status_002': '已支付',
        'payment_status_003': '已退款'
      }
      return statuses[status] || status
    }

    const goToPayment = () => {
      router.push(getLocalePath('/payment'))
    }

    const loadRegistrationInfo = async () => {
      try {
        const response = await getRegistrationInfo(userStore.userInfo.id)
        if (response.code === 1000) {
          const data = response.data.data[0]
          registrationInfo.value = {
            ...data,
            name: `${data.signUpLastName}${data.signUpFirstName}`,
            meetingType: data.meetingType,
            status: data.status,
            createTime: data.createTime
          }



          // 加载支付信息
          paymentInfo.value = {
            orderNo: data.orderNo,
            orderAmtPayable: data.orderAmtPayable,
            orderPaidAmt: data.orderPaidAmt,
            paymentStatus: data.paymentStatus,
            createTime: data.createTime,
            paymentTime: data.paymentTime
          }
        }
      } catch (error) {
        console.error('加载报名信息失败', error)
        ElMessage.error('加载报名信息失败')
      }
    }
    // 修改密码
    const handleChangePassword = async () => {
   
      if (!passwordFormRef.value) return

      try {
        await passwordFormRef.value.validate()
        isLoading.value = true

        const success = await userStore.changeUserPassword({
          currentPassword: passwordForm.value.currentPassword,
          newPassword: passwordForm.value.newPassword
        })
        // console.log(success)
        if (success.code === 1000) {
          router.push(getLocalePath('/login'))
          // 清空表单
          passwordForm.value = {
            currentPassword: '',
            newPassword: '',
            confirmPassword: ''
          }
        }
      } catch (error) {
        console.error('修改密码失败:', error)
        ElMessage.error(error.response?.data?.message || '修改失败，请稍后重试')
      } finally {
        isLoading.value = false
      }
    }

    // 监听密码表单验证状态
    const watchPasswordForm = () => {
      if (passwordFormRef.value) {
        passwordFormRef.value.validate((valid) => {
          isPasswordFormValid.value = valid
        })
      }
    }

    // 监听密码表单变化
    watch(() => passwordForm.value, () => {
      watchPasswordForm()
    }, { deep: true })

    // 更新个人资料
    const handleUpdateProfile = async () => {
      if (!profileFormRef.value) return

      try {
        await profileFormRef.value.validate()
        isLoading.value = true

        const success = await userStore.updateUserProfile(profileForm.value)
        if (success) {
          // 表单数据已经通过 store 更新，无需额外操作
        }
      } catch (error) {
        console.error('更新个人资料失败:', error)
        ElMessage.error(error.response?.data?.message || '更新失败，请稍后重试')
      } finally {
        isLoading.value = false
      }
    }

    onMounted(() => {
      const tab = router.currentRoute.value.query.tab
      if (tab) {
        activeTab.value = tab
      }
      getUserInfo()
      // loadRegistrationInfo()
      getDictCode('country_code').then(res => {
        countryOptions.value = res.data.data.map(item => ({
          value: item.dictionaryCode,
          label: item.dictionaryValue
        }))
      })
    })

    return {
      passwordFormRef,
      handleUpdateProfile,
      isLoading,
      handleChangePassword,
      activeTab,
      user,
      profileForm,
      passwordForm,
      navItems,
      updateProfile,
      resetProfile,
      updatePassword,
      handleLogout,
      profileFormRef,
      registrationActiveTab,
      activeCompanions,
      profileRules,
      passwordRules,
      registrationInfo,
      companionInfo,
      paymentInfo,
      getMeetingTypeName,
      getStatusName,
      getPaymentStatusName,
      goToPayment,
      isPasswordFormValid,
      countryOptions
    }
  }
}
</script>
<style scoped>
@import url('@/assets/css/dashboard.css');
</style>