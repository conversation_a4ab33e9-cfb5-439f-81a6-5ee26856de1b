<template>
  <div class="contact-page">
    <!-- 页面头部 -->
    <section class="page-header">
      <div class="container">
        <h1>联系我们</h1>
        <p>我们随时为您提供帮助和支持</p>
      </div>
    </section>
    
    <!-- 联系信息 -->
    <section class="contact-info-section">
      <div class="container">
        <div class="contact-grid">
          <div class="contact-info">
            <h2>联系方式</h2>
            
            <div class="info-items">
              <div class="info-item">
                <div class="info-icon">
                  <i class="fas fa-envelope"></i>
                </div>
                <div class="info-content">
                  <h3>电子邮件</h3>
                  <p>一般咨询：<EMAIL></p>
                  <p>报名咨询：<EMAIL></p>
                  <p>赞助合作：<EMAIL></p>
                </div>
              </div>
              
              <div class="info-item">
                <div class="info-icon">
                  <i class="fas fa-phone-alt"></i>
                </div>
                <div class="info-content">
                  <h3>电话</h3>
                  <p>+86 10 8888 8888（工作日 9:00-17:00）</p>
                  <p>+86 10 6666 6666（紧急联系）</p>
                </div>
              </div>
              
              <div class="info-item">
                <div class="info-icon">
                  <i class="fas fa-map-marker-alt"></i>
                </div>
                <div class="info-content">
                  <h3>地址</h3>
                  <p>上海市浦东新区陆家嘴世纪大道33号</p>
                  <p>Pudong Shangri-La, Shanghai</p>
                  <p>邮编：200120</p>
                </div>
              </div>
              
              <div class="info-item">
                <div class="info-icon">
                  <i class="fas fa-clock"></i>
                </div>
                <div class="info-content">
                  <h3>工作时间</h3>
                  <p>周一至周五：9:00 - 17:00</p>
                  <p>周六、周日及法定节假日休息</p>
                </div>
              </div>
            </div>
            
            <div class="social-links">
              <h3>关注我们</h3>
              <div class="social-icons">
                <a href="#" class="social-icon">
                  <i class="fab fa-weixin"></i>
                </a>
                <a href="#" class="social-icon">
                  <i class="fab fa-weibo"></i>
                </a>
                <a href="#" class="social-icon">
                  <i class="fab fa-linkedin-in"></i>
                </a>
                <a href="#" class="social-icon">
                  <i class="fab fa-twitter"></i>
                </a>
              </div>
            </div>
          </div>
          
          <div class="contact-form">
            <h2>发送消息</h2>
            <p>如有任何问题或建议，请填写以下表单，我们将尽快回复您。</p>
            
            <form @submit.prevent="submitForm">
              <div class="form-group">
                <label for="name" class="form-label">姓名 <span class="required">*</span></label>
                <input 
                  type="text" 
                  id="name" 
                  v-model="form.name" 
                  class="form-control" 
                  required
                >
              </div>
              
              <div class="form-group">
                <label for="email" class="form-label">电子邮箱 <span class="required">*</span></label>
                <input 
                  type="email" 
                  id="email" 
                  v-model="form.email" 
                  class="form-control" 
                  required
                >
              </div>
              
              <div class="form-group">
                <label for="subject" class="form-label">主题 <span class="required">*</span></label>
                <select 
                  id="subject" 
                  v-model="form.subject" 
                  class="form-control" 
                  required
                >
                  <option value="">请选择</option>
                  <option value="general">一般咨询</option>
                  <option value="registration">报名咨询</option>
                  <option value="payment">支付问题</option>
                  <option value="accommodation">住宿咨询</option>
                  <option value="sponsor">赞助合作</option>
                  <option value="other">其他</option>
                </select>
              </div>
              
              <div class="form-group">
                <label for="message" class="form-label">消息内容 <span class="required">*</span></label>
                <textarea 
                  id="message" 
                  v-model="form.message" 
                  class="form-control" 
                  rows="5" 
                  required
                ></textarea>
              </div>
              
              <div class="form-actions">
                <button type="submit" class="btn btn-primary" :disabled="isSubmitting">
                  {{ isSubmitting ? '发送中...' : '发送消息' }}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 地图 -->
    <section class="map-section">
      <div class="container">
        <SectionTitle title="会场位置" />
        
        <div class="map-container">
          <div class="map-placeholder">
            <img src="https://via.placeholder.com/1200x500?text=Map+of+Shanghai+Pudong+Shangri-La+Hotel" alt="Pudong Shangri-La, Shanghai地图">
          </div>
          <div class="map-overlay">
            <div class="map-card">
              <h3>Pudong Shangri-La, Shanghai</h3>
              <p><i class="fas fa-map-marker-alt"></i> 上海市浦东新区陆家嘴世纪大道33号</p>
              <p><i class="fas fa-subway"></i> 地铁2号线陆家嘴站4号出口步行10分钟</p>
              <a href="https://maps.google.com/?q=上海市浦东新区陆家嘴世纪大道33号" target="_blank" class="btn btn-primary">
                <i class="fas fa-directions"></i> 获取导航
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 常见问题 -->
    <section class="faq-section">
      <div class="container">
        <SectionTitle title="常见问题" />
        
        <div class="faq-list">
          <div class="faq-item">
            <div class="faq-question" @click="toggleFaq(0)">
              <h3>如何注册参加会议？</h3>
              <i class="fas" :class="activeFaq === 0 ? 'fa-minus' : 'fa-plus'"></i>
            </div>
            <div class="faq-answer" :class="{ 'active': activeFaq === 0 }">
              <p>您可以通过官网的"报名参会"页面完成注册。填写必要信息并提交后，您将收到确认邮件。完成缴费后，您的报名将被确认。</p>
            </div>
          </div>
          
          <div class="faq-item">
            <div class="faq-question" @click="toggleFaq(1)">
              <h3>会议提供同声传译服务吗？</h3>
              <i class="fas" :class="activeFaq === 1 ? 'fa-minus' : 'fa-plus'"></i>
            </div>
            <div class="faq-answer" :class="{ 'active': activeFaq === 1 }">
              <p>是的，会议将提供中英文同声传译服务。主会场和所有分论坛都配备专业翻译设备，确保国际代表能够顺畅参与讨论。</p>
            </div>
          </div>
          
          <div class="faq-item">
            <div class="faq-question" @click="toggleFaq(2)">
              <h3>如何申请成为会议演讲嘉宾？</h3>
              <i class="fas" :class="activeFaq === 2 ? 'fa-minus' : 'fa-plus'"></i>
            </div>
            <div class="faq-answer" :class="{ 'active': activeFaq === 2 }">
              <p>如果您希望成为会议演讲嘉宾，请发送您的个人简介、演讲主题和摘要至*****************************。组委会将在收到申请后的两周内回复您。</p>
            </div>
          </div>
          
          <div class="faq-item">
            <div class="faq-question" @click="toggleFaq(3)">
              <h3>会议有哪些赞助机会？</h3>
              <i class="fas" :class="activeFaq === 3 ? 'fa-minus' : 'fa-plus'"></i>
            </div>
            <div class="faq-answer" :class="{ 'active': activeFaq === 3 }">
              <p>我们提供多种赞助方案，包括钻石赞助商、金牌赞助商、银牌赞助商以及特定项目赞助。每个级别都有不同的权益和曝光机会。详情请联系*****************************获取赞助方案。</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { ref } from 'vue'
import SectionTitle from '@/components/common/SectionTitle.vue'

export default {
  name: 'ContactPage',
  components: {
    SectionTitle
  },
  setup() {
    const isSubmitting = ref(false)
    const activeFaq = ref(null)
    
    const form = ref({
      name: '',
      email: '',
      subject: '',
      message: ''
    })
    
    const toggleFaq = (index) => {
      activeFaq.value = activeFaq.value === index ? null : index
    }
    
    const submitForm = async () => {
      try {
        isSubmitting.value = true
        
        // 模拟表单提交
        await new Promise(resolve => setTimeout(resolve, 1500))
        
        // 显示成功消息
        alert('消息发送成功！我们将尽快回复您。')
        
        // 重置表单
        form.value = {
          name: '',
          email: '',
          subject: '',
          message: ''
        }
      } catch (error) {
        console.error('表单提交失败', error)
        alert('消息发送失败，请稍后重试')
      } finally {
        isSubmitting.value = false
      }
    }
    
    return {
      form,
      isSubmitting,
      activeFaq,
      toggleFaq,
      submitForm
    }
  }
}
</script>

<style scoped>
.page-header {
  background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('https://picsum.photos/2000/1000?random=25');
  background-size: cover;
  background-position: center;
  color: var(--text-white);
  padding: 120px 0 60px;
  text-align: center;
  position: relative;
}

.page-header h1 {
  font-size: 42px;
  margin-bottom: 15px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  animation: fadeInDown 1s ease-out;
}

.page-header p {
  font-size: 18px;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  animation: fadeInUp 1s ease-out;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.contact-info-section {
  padding: 80px 0;
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 50px;
}

.contact-info h2, .contact-form h2 {
  font-size: 24px;
  margin-bottom: 30px;
  color: var(--primary-color);
}

.contact-form p {
  margin-bottom: 30px;
  color: var(--text-light);
}

.info-items {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
  margin-bottom: 40px;
}

.info-item {
  display: flex;
  gap: 15px;
}

.info-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: rgba(30, 136, 229, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: var(--primary-color);
  flex-shrink: 0;
}

.info-content h3 {
  font-size: 18px;
  margin-bottom: 10px;
}

.info-content p {
  color: var(--text-light);
  margin-bottom: 5px;
}

.social-links h3 {
  font-size: 18px;
  margin-bottom: 20px;
}

.social-icons {
  display: flex;
  gap: 15px;
}

.social-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--bg-light);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  transition: var(--transition);
}

.social-icon:hover {
  background-color: var(--primary-color);
  color: var(--text-white);
  transform: translateY(-5px);
}

.contact-form {
  background-color: var(--bg-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: 40px;
}

.required {
  color: var(--error-color);
}

.form-actions {
  margin-top: 30px;
}

.map-section {
  padding: 80px 0;
  background-color: var(--bg-light);
}

.map-container {
  position: relative;
  margin-top: 50px;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.map-placeholder img {
  width: 100%;
  display: block;
}

.map-overlay {
  position: absolute;
  top: 30px;
  right: 30px;
}

.map-card {
  background-color: var(--bg-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  padding: 20px;
  width: 300px;
}

.map-card h3 {
  font-size: 18px;
  margin-bottom: 15px;
  color: var(--primary-color);
}

.map-card p {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
  color: var(--text-light);
}

.map-card .btn {
  margin-top: 15px;
}

.faq-section {
  padding: 80px 0;
}

.faq-list {
  max-width: 800px;
  margin: 50px auto 0;
}

.faq-item {
  margin-bottom: 20px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.faq-question {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background-color: var(--bg-light);
  cursor: pointer;
}

.faq-question h3 {
  font-size: 18px;
  margin: 0;
}

.faq-answer {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease, padding 0.3s ease;
}

.faq-answer.active {
  max-height: 200px;
  padding: 20px;
  border-top: 1px solid var(--border-color);
}

.faq-answer p {
  margin: 0;
  color: var(--text-light);
}

@media (max-width: 992px) {
  .contact-grid {
    grid-template-columns: 1fr;
  }
  
  .info-items {
    grid-template-columns: 1fr;
  }
  
  .map-overlay {
    position: relative;
    top: auto;
    right: auto;
    padding: 20px;
  }
  
  .map-card {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .contact-form {
    padding: 30px 20px;
  }
}
</style>
