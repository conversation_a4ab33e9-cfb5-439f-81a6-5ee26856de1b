.conference-overview-container{
    background: url('@/assets/images/home-bg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    min-height: 100vh;
    
  }
  .page-header{
    background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('@/assets/images/gugong.png');
    background-size: cover;
    background-position: center;
    color: var(--text-white);
    padding: 120px 0 60px;
    text-align: center;
  }
  
  .page-header h1 {
    font-size: 36px;
    margin-bottom: 10px;
  }
  /* 会议亮点样式 */
  .about-highlights {
    padding: 80px 0;
    
  }
  
  .highlights-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    margin-top: 50px;
  }
  
  .highlight-card {
    background-color: var(--bg-color);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
    padding: 30px;
    text-align: center;
    transition: var(--transition);
  }
  
  .highlight-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
  }
  
  .highlight-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background-color: rgba(30, 136, 229, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
  }
  
  .highlight-icon i {
    font-size: 30px;
    color: var(--primary-color);
  }
  
  .highlight-card h3 {
    font-size: 20px;
    margin-bottom: 15px;
    color: var(--primary-color);
  }
  
  .highlight-card p {
    color: var(--text-light);
    line-height: 1.6;
  }
  .speakers-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    padding: 40px 0;
    max-width: 1200px;
    margin: 0 auto;
    opacity: 1;
    transition: opacity 0.3s ease;
  }
  
  .speaker-card {
    background: #ffffff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.5, 0, 0, 1);
    transform: translateZ(0);
    will-change: transform;
  }
  
  .speaker-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 24px rgba(0,0,0,0.15);
  }
  
  .speaker-image {
    position: relative;
    width: 100%;
    overflow: hidden;
    height: 400px;
  }
  
  .speaker-image img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.6s cubic-bezier(0.5, 0, 0, 1);
  }
  
  .speaker-card:hover .speaker-image img {
    transform: scale(1.1);
  }
  
  .speaker-info {
    padding: 24px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
  }
  
  .speaker-info h3 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
  }
  
  .speaker-title {
    font-size: 16px;
    color: var(--primary-color);
    margin-bottom: 12px;
    font-weight: 500;
  }
  
  .speaker-bio {
    font-size: 14px;
    color: #666;
    line-height: 1.6;
    margin-bottom: 20px;
    flex-grow: 1;
  }
  
  .speaker-social {
    display: flex;
    gap: 12px;
  }
  
  .speaker-social a {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    transition: all 0.3s ease;
  }
  
  .speaker-social a:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
  }
  .footer-image-item{
    margin:0 8px;
  }
  
  /* 添加动画相关样式 */
  .speaker-card.animate__animated {
    opacity: 1;
  }
  
  /* 确保动画可以重复播放 */
  .animate__animated {
    animation-duration: 1s;
    animation-fill-mode: both;
    animation-iteration-count: 1;
  }
  
  /* 图片加载占位符样式 */
  .image-placeholder {
    background: linear-gradient(110deg, #ececec 8%, #f5f5f5 18%, #ececec 33%);
    background-size: 200% 100%;
    animation: shimmer 1.5s linear infinite;
  }
  
  @keyframes shimmer {
    to {
      background-position: -200% 0;
    }
  }