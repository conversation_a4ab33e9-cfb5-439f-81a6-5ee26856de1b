import { defineStore } from 'pinia'
import { ref } from 'vue'
import { login, logout, updateProfile, changePassword } from '../api/auth'
import { ElMessage } from 'element-plus'
import { sm2 } from 'sm-crypto'

export const useUserStore = defineStore('user', () => {
  // 状态
  const isLoggedIn = ref(false)
  const userInfo = ref({})
  const token = ref('')

  // SM2 公钥
  const publicKey = '042136fe42541b34a589a158382b5a2f65593c181af2b388f18745818d5a74350387e8bdd8d44828f4b94180cc4b5cd8143da5324ee9ba86a0fd7d75572a1f4e11'

  // 密码加密函数
  const encryptPassword = (password) => {
    try {
      return "04" + sm2.doEncrypt(password, publicKey)
    } catch (error) {
      console.error('密码加密失败:', error)
      ElMessage.error('密码加密失败，请稍后重试')
      throw error
    }
  }

  // 登录
  const loginUser = async (loginData) => {
    try {
      const response = await login({
        ...loginData,
        password: encryptPassword(loginData.password)
      })
      
      if (response.code === 1000) {
        isLoggedIn.value = true
        userInfo.value = response.data.data.userInfo
        token.value = response.data.data.token
        return true
      } else {
        ElMessage.error(response.msg || '登录失败')
        return false
      }
    } catch (error) {
      console.error('登录失败:', error)
      ElMessage.error(error.response?.data?.message || '登录失败，请稍后重试')
      return false
    }
  }

  // 登出
  const logoutUser = async () => {
    try {
      await logout()
      isLoggedIn.value = false
      userInfo.value = {}
      token.value = ''
      ElMessage.success('登出成功')
    } catch (error) {
      console.error('登出失败:', error)
      ElMessage.error('登出失败，请稍后重试')
    }
  }

  // 更新个人资料
  const updateUserProfile = async (profileData) => {
    try {
      const response = await updateProfile(profileData)
      if (response.code === 1000) {
        userInfo.value = { ...userInfo.value, ...profileData }
        ElMessage.success('个人资料更新成功')
        return true
      } else {
        ElMessage.error(response.msg || '更新失败')
        return false
      }
    } catch (error) {
      console.error('更新个人资料失败:', error)
      ElMessage.error(error.response?.data?.message || '更新失败，请稍后重试')
      return false
    }
  }

  // 修改密码
  const changeUserPassword = async (passwordData) => {
    try {
      const response = await changePassword({
        id: userInfo.value.id,
        oldPassword: encryptPassword(passwordData.currentPassword),
        newPassword: encryptPassword(passwordData.newPassword)
      })
      
      if (response.code === 1000) {
        ElMessage.success('密码修改成功')
        return true
      } else {
        ElMessage.error(response.msg || '修改失败')
        return false
      }
    } catch (error) {
      console.error('修改密码失败:', error)
      ElMessage.error(error.response?.data?.message || '修改失败，请稍后重试')
      return false
    }
  }

  // 清除用户信息和token
  const clearToken = () => {
    isLoggedIn.value = false
    userInfo.value = {}
    token.value = ''
    // 清除本地存储
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
    sessionStorage.clear()
  }

  return {
    isLoggedIn,
    userInfo,
    token,
    loginUser,
    logoutUser,
    updateUserProfile,
    changeUserPassword,
    clearToken
  }
}, {
  persist: true
}) 