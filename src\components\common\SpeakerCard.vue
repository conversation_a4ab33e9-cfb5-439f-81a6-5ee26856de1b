<template>
  <div class="speaker-card">
    <div class="speaker-image image-container" :class="{ 'image-placeholder': !imageLoaded }">
      <img 
        :src="speaker.avatar" 
        :alt="speaker.name"
        loading="lazy"
        @load="onImageLoad"
      >
    </div>
    <div class="speaker-info">
      <h3>{{ speaker.name }}</h3>
      <p class="speaker-title">{{ speaker.title }}</p>
      <p class="speaker-company">{{ speaker.company }}</p>
      <p v-if="showBio" class="speaker-bio">{{ speaker.bio }}</p>
      <div v-if="showSocial && speaker.social" class="speaker-social">
        <a v-if="speaker.social.linkedin" :href="speaker.social.linkedin" target="_blank" rel="noopener noreferrer">
          <i class="fab fa-linkedin"></i>
        </a>
        <a v-if="speaker.social.twitter" :href="speaker.social.twitter" target="_blank" rel="noopener noreferrer">
          <i class="fab fa-twitter"></i>
        </a>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'

export default {
  name: 'SpeakerCard',
  props: {
    speaker: {
      type: Object,
      required: true
    },
    showBio: {
      type: Boolean,
      default: false
    },
    showSocial: {
      type: Boolean,
      default: false
    }
  },
  setup() {
    const imageLoaded = ref(false)
    
    const onImageLoad = () => {
      imageLoaded.value = true
    }
    
    return {
      imageLoaded,
      onImageLoad
    }
  }
}
</script>

<style scoped>
.speaker-card {
  background: #fff;
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: transform 0.3s ease;
}

.speaker-card:hover {
  transform: translateY(-10px);
}

.speaker-image {
  position: relative;
  overflow: hidden;
}

.speaker-image::before {
  content: "";
  display: block;
  padding-top: 100%; /* 1:1 宽高比 */
}

.speaker-image img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.speaker-card:hover .speaker-image img {
  transform: scale(1.1);
}

.speaker-info {
  padding: 1.5rem;
}

.speaker-info h3 {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
  color: var(--text-color);
}

.speaker-title {
  font-size: 0.9rem;
  color: var(--primary-color);
  margin-bottom: 0.25rem;
}

.speaker-company {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-bottom: 1rem;
}

.speaker-bio {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-bottom: 1rem;
  line-height: 1.5;
}

.speaker-social {
  display: flex;
  gap: 0.75rem;
}

.speaker-social a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #f5f5f5;
  color: var(--text-color);
  transition: var(--transition);
}

.speaker-social a:hover {
  background-color: var(--primary-color);
  color: white;
}
</style>
