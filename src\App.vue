<template>
  <div class="app">
    <AppHeader />
    <main class="main-content">
      <router-view v-slot="{ Component }">
        <transition name="fade" mode="out-in">
          <component :is="Component" />
        </transition>
      </router-view>
    </main>
    <AppFooter />
  </div>
</template>

<script>
import { defineComponent } from 'vue'
import AppHeader from './components/layout/Header.vue'
import AppFooter from './components/layout/Footer.vue'

export default defineComponent({
  name: 'App',
  components: {
    AppHeader,
    AppFooter
  }
})
</script>

<style>
@import './styles/main.css';
@import './styles/animations.css';
@import './styles/responsive.css';
@import './styles/components.css';
@import '@fortawesome/fontawesome-free/css/all.min.css';

.app {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.main-content {
  flex: 1;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.el-table th.el-table__cell,.el-table tr,.el-collapse-item__header,.el-collapse-item__content,.el-textarea__inner,.el-input__inner,.el-select__wrapper{
  background: #fffdfa !important;
}

</style>