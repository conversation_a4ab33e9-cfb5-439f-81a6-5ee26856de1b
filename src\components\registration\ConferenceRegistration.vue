<template>
  <div class="conference-registration">
    <div class="form-section">
 
      
      <el-form
        ref="formRef"
        :model="modelValue"
        :rules="rules"
        label-width="0"
      >
       

        <div class="form-group">
          <!-- <h4 class="section-title">分论坛选择</h4> -->
          <el-form-item label="" prop="forums">
            <el-checkbox-group v-model="modelValue.forums" class="forum-checkbox-group">
              <el-checkbox 
                v-for="forum in forumOptions" 
                :key="forum.value" 
                :label="forum.value"
                class="forum-checkbox-item"
              >
                <div class="forum-checkbox-content">
                  <span class="forum-name">{{ forum.name }}</span>
                  <span class="forum-price">¥{{ forum.price }}</span>
                </div>
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </div>

        <div class="validation-message" v-if="!isValid">
          <el-alert
            title="请至少选择一个主论坛或分论坛"
            type="warning"
            :closable="false"
            show-icon
          />
        </div>

        <div class="total-amount" v-if="isValid">
          <span class="amount-label">总计金额：</span>
          <span class="amount-value">¥{{ calculateAmount() }}</span>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'

export default {
  name: 'ConferenceRegistration',
  props: {
    modelValue: {
      type: Object,
      required: true
    },
    companions: {
      type: Array,
      default: () => []
    }
  },
  emits: ['update:modelValue', 'validate'],
  setup(props, { emit }) {
    const formRef = ref(null)
    
    const ticketOptions = [
      {
        value: '1',
        name: '国际CMC大会',
        price: '¥1,200',
        description: '会期1.5天',
        features: [
          '各国CMC交流管理咨询经验，探讨管理咨询新趋势',
          '探讨合作机会，推动企业提升管理水平',
          '颁发国际管理咨询案例奖（君士坦丁奖）'
        ]
      },
      {
        value: '2',
        name: 'ICMCI年会',
        price: '¥800',
        description: '会期1.5天',
        features: [
          '总结过去一年工作各成员国组织交流经验、战略执行、会员发展业务项目进展及财务决算',
          '落实新一年工作任务，重点项目推动审议财务预算，增补执委会、相关专业委员会委员和理事'
        ]
      }
    ]
    
    const forumOptions = [
      { 
        value: '3',
        name: '各国CMC交流管理咨询经验，探讨管理咨询新趋势',
        price: 500
      },
      { 
        value: '4', 
        name: '探讨合作机会，推动企业提升管理水平',
        price: 500
      },
      { 
        value: '5', 
        name: '颁发国际管理咨询案例奖（君士坦丁奖）',
        price: 500
      },
      { 
        value: '6', 
        name: '总结过去一年工作,主要包括各成员国组织交流经验、战略执行、会员发展、业务项目进展及财务决算',
        price: 300
      },
      { 
        value: '7', 
        name: '审议财务预算，增补执委会、相关专业委员会委员和理事',
        price: 300
      }
    ]

    // 验证规则
    const rules = {
      meetingType: [
        { 
          validator: (rule, value, callback) => {
            if (value.length === 0 && props.modelValue.forums.length === 0) {
              callback(new Error('请至少选择一个主论坛或分论坛'))
            } else {
              callback()
            }
          },
          trigger: 'change'
        }
      ]
    }

    // 计算是否有效
    const isValid = computed(() => {
      return props.modelValue.meetingType.length > 0 || props.modelValue.forums.length > 0
    })

    // 切换主论坛选择
    const toggleMeetingType = (value) => {
      const newValue = { ...props.modelValue }
      const index = newValue.meetingType.indexOf(value)
      if (index === -1) {
        newValue.meetingType.push(value)
      } else {
        newValue.meetingType.splice(index, 1)
      }
      emit('update:modelValue', newValue)
      validateForm()
    }

    // 验证表单
    const validateForm = async () => {
      if (formRef.value) {
        try {
          await formRef.value.validate()
          emit('validate', isValid.value)
        } catch (error) {
          emit('validate', false)
        }
      }
    }

    // 监听 modelValue 变化
    watch(() => props.modelValue, () => {
      validateForm()
    }, { deep: true })

    // 计算总金额
    const calculateAmount = () => {
      let amount = 0
      
      // 计算主论坛费用
      if (props.modelValue.meetingType.includes('1')) {
        amount += 1200
      }
      if (props.modelValue.meetingType.includes('2')) {
        amount += 800
      }
      
      // 计算分论坛费用
      props.modelValue.forums.forEach(forumValue => {
        const forum = forumOptions.find(f => f.value === forumValue)
        if (forum) {
          amount += forum.price
        }
      })
      console.log(props)
      // alert(props.companions)
      // 计算同行人费用
      const companionCount = props.companions.length
      return amount * (1 + companionCount)
    }

    // 获取选中的论坛编码列表
    const getSelectedForums = () => {
      return [
        ...props.modelValue.meetingType,
        ...props.modelValue.forums
      ]
    }

    return {
      formRef,
      rules,
      isValid,
      ticketOptions,
      forumOptions,
      toggleMeetingType,
      calculateAmount,
      getSelectedForums,
      validateForm
    }
  }
}
</script>

<style scoped>
.conference-registration {
  margin-bottom: 40px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 20px;
  padding-left: 10px;
  /* border-left: 4px solid var(--primary-color); */
}

.ticket-options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.ticket-option {
  border: 2px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: 20px;
  cursor: pointer;
  transition: var(--transition);
  background-color: white;
}

.ticket-option.active {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 136, 229, 0.2);
}

.ticket-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.ticket-header h4 {
  font-size: 18px;
  margin: 0;
}

.ticket-price {
  font-size: 18px;
  font-weight: 700;
  color: var(--primary-color);
}

.ticket-desc {
  color: var(--text-light);
  margin-bottom: 15px;
  font-size: 14px;
}

.ticket-features ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.ticket-features li {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
}

.ticket-features i {
  color: var(--success-color);
  margin-top: 4px;
}

.forum-checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.forum-checkbox-item {
  margin-right: 0 !important;
  padding: 15px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  transition: var(--transition);
}

.forum-checkbox-item:hover {
  background-color: var(--bg-hover);
}

.forum-checkbox-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-left: 8px;
}

.forum-name {
  flex: 1;
  margin-right: 20px;
}

.forum-price {
  color: var(--primary-color);
  font-weight: 600;
  white-space: nowrap;
}

.validation-message {
  margin: 20px 0;
}

.total-amount {
  margin-top: 20px;
  text-align: right;
  padding: 15px;
  background-color: var(--bg-light);
  border-radius: var(--radius-sm);
}

.amount-label {
  font-size: 16px;
  color: var(--text-light);
}

.amount-value {
  font-size: 20px;
  font-weight: 700;
  color: var(--primary-color);
  margin-left: 10px;
}

.form-group {
  margin-bottom: 30px;
}

@media (max-width: 768px) {
  .ticket-options {
    grid-template-columns: 1fr;
  }
}
</style> 