<template>
  <div class="forgot-password-page">
    <!-- 页面头部 -->
    <section class="page-header">
      <div class="container">
        <h1>reset password</h1>
        <p>Reset your password through email verification</p>
      </div>
    </section>
    <div class="conference-overview-container">
    <!-- 重置密码表单 -->
    <section class="reset-section">
      <div class="container">
        <div class="auth-container">
          <div class="auth-form">
            <h2>reset password</h2>
            <p class="auth-desc">Please enter your account and email, and we will send a verification code to your email</p>
            
            <el-form
              :model="form"
              :rules="rules"
              ref="resetFormRef"
              label-width="140px"
              class="reset-form"
            >
              <el-form-item label="account number" prop="account">
                <el-input
                  v-model="form.account"
                  placeholder="Please enter your account"
                />
              </el-form-item>

              <el-form-item label="password" prop="password">
                <el-input
                  v-model="form.password"
                  type="password"
                  placeholder="Please enter a new password"
                  show-password
                />
              </el-form-item>

              <el-form-item label="email" prop="email">
                <el-input
                  v-model="form.email"
                  placeholder="Please enter your email address"
                />
              </el-form-item>

              <el-form-item label="Verification code" prop="emailCode">
                <div class="verification-code">
                  <el-input
                    v-model="form.emailCode"
                    placeholder="Please enter the verification code"
                    style="width: 60%"
                  />
                  <el-button
                    type="primary"
                    :disabled="countdown > 0"
                    @click="handleSendCode"
                    style="width: 35%; margin-left: 5%"
                  >
                    {{ countdown > 0 ? `${countdown}again ` : "code" }}
                  </el-button>
                </div>
              </el-form-item>
              
              <el-form-item>
                <el-button
                  type="primary"
                  @click="handleReset"
                  :loading="isLoading"
                  style="width: 100%"
                >
                  reset password
                </el-button>
              </el-form-item>
            </el-form>
            
            <div class="auth-footer">
              <p>Remember the password? <router-link :to="getLocalePath('/login')">Return to log in</router-link></p>
            </div>
          </div>
        </div>
      </div>
    </section>
    </div>
  </div>
</template>

<script>
import { ref, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { sendResetEmailCode, resetPassword } from '../../api/auth'
import { sm2 } from 'sm-crypto'
import { useLanguage } from '@/hooks/useLanguage'

export default {
  name: 'ForgotPasswordPage',
  setup() {
    const router = useRouter()
    const { getLocalePath } = useLanguage()
    const resetFormRef = ref(null)
    const isLoading = ref(false)
    const countdown = ref(0)
    const timer = ref(null)
    
    // SM2 公钥
    const publicKey = '042136fe42541b34a589a158382b5a2f65593c181af2b388f18745818d5a74350387e8bdd8d44828f4b94180cc4b5cd8143da5324ee9ba86a0fd7d75572a1f4e11'
    
    const form = ref({
      account: "",
      email: "",
      emailCode: "",
      password: ""
    })

    const rules = {
      account: [
        { required: true, message: 'Please enter your account', trigger: 'blur' },
        { min: 4, max: 20, message: 'Length should be 4 to 20 characters', trigger: 'blur' }
      ],
      password: [
        { required: true, message: 'Please enter a new password', trigger: 'blur' },
        { min: 8, max: 20, message: 'Length should be 8 to 20 characters', trigger: 'blur' }
      ],
      email: [
        { required: true, message: 'Please enter your email', trigger: 'blur' },
        { type: 'email', message: 'Please enter a valid email address', trigger: 'blur' }
      ],
      emailCode: [
        { required: true, message: 'Please enter the verification code', trigger: 'blur' },
        { len: 6, message: 'Verification code should be 6 characters', trigger: 'blur' }
      ]
    }

    // 发送验证码
    const handleSendCode = async () => {
      if (!form.value.email) {
        ElMessage.warning('Please enter your email first')
        return
      }
      
      if (countdown.value > 0) return
      
      try {
        const response = await sendResetEmailCode(form.value.email)
        
        if (response.code === 1000) {
          ElMessage.success(response.msg || 'Verification code has been sent')
          countdown.value = 60
          timer.value = setInterval(() => {
            countdown.value--
            if (countdown.value <= 0) {
              clearInterval(timer.value)
            }
          }, 1000)
        } else {
          ElMessage.error(response.msg || 'Sending failed, please try again later')
        }
      } catch (error) {
        console.error('发送验证码失败:', error)
        ElMessage.error(error.response?.data?.message || 'Sending failed, please try again later')
      }
    }

    // reset password
    const handleReset = async () => {
      if (!resetFormRef.value) return

      try {
        // 验证表单
        await resetFormRef.value.validate()
        isLoading.value = true
        
        // 对密码进行加密
        const encryptedPassword = "04" + sm2.doEncrypt(form.value.password, publicKey)
        
        // 准备请求参数
        const params = {
          account: form.value.account,
          email: form.value.email,
          emailCode: form.value.emailCode,
          password: encryptedPassword
        }
        
        // 调用重置密码API
        const response = await resetPassword(params)
        
        if (response.code === 1000) {
          ElMessage.success('Password reset successful, please log in with a new password')
          router.push(getLocalePath('/login'))
        } else {
          ElMessage.error(response.msg || 'Reset failed, please try again later')
        }
      } catch (error) {
        console.error('重置密码失败:', error)
        ElMessage.error(error.response?.data?.message || 'Reset failed, please try again later')
      } finally {
        isLoading.value = false
      }
    }

    // 组件卸载时清除定时器
    onUnmounted(() => {
      if (timer.value) {
        clearInterval(timer.value)
      }
    })
    
    return {
      form,
      rules,
      resetFormRef,
      isLoading,
      countdown,
      handleReset,
      getLocalePath,
      handleSendCode
    }
  }
}
</script>

<style scoped>
@import url('@/assets/css/forgotPassword.css');
</style> 