.conference-overview-container{
    background: url('@/assets/images/home-bg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    min-height: 100vh;
    padding: 60px;
  }
  .container {
    max-width: 1200px;
    margin: 0 auto;
    /* padding: 0 20px; */
  }
  .travel-page {
    min-height: 100vh;
    background-color: #f5f7fa;
    overflow-x: hidden; /* 防止水平滚动条 */
    position: relative; /* 确保动画元素定位正确 */
  }
  
  .page-header {
    background: linear-gradient(rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.3)), url('@/assets/images/dahui.png');
    background-size: cover;
    background-position: center;
  
    height: 200px;
    text-align: center;
    margin-top: 80px;
  }
  
  .travel-section {
    position: relative;
    width: 100%;
    box-sizing: border-box;
    overflow: hidden; /* 防止内容溢出 */
  }
  
  .travel-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    box-sizing: border-box;
    position: relative;
  }
  
  .search-section {
    margin-bottom: 40px;
  }
  
  .search-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
  
  .search-form {
    padding: 20px;
  }
  
  .attractions-section,
  .tours-section {
    margin-bottom: 40px;
    position: relative;
    width: 100%;
    box-sizing: border-box;
  }
  
  h2 {
    font-size: 24px;
    color: var(--primary-color);
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--primary-color);
  }
  
  .spot-card {
    margin-bottom: 40px;
    transition: all 0.3s ease;
    cursor: pointer;
    transform: translateZ(0);
    will-change: transform;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  }
  
  .spot-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 30px rgba(0,0,0,0.15);
  }
  
  .spot-content {
    display: flex;
    height: 340px;
  }
  
  .spot-image-wrapper {
    /* flex: 0 0 49%; */
    width: 52%;
    overflow: hidden;
  }
  
  .spot-image {
    /* width: 100%; */
    height: 100%;
    object-fit: contain;
    transition: transform 0.5s ease;
  }
  
  .spot-card:hover .spot-image {
    transform: scale(1.03);
  }
  
  .spot-info {
    flex: 1;
    padding: 20px;
    background: #fff;
    display: flex;
    flex-direction: column;
  }
  
  .spot-info h3 {
    margin: 0 0 12px;
    font-size: 28px;
    color: #2c3e50;
    font-weight: 600;
    line-height: 1.4;
  }
  
  .spot-description {
    color: #5a6a7e;
    font-size: 16px;
    line-height: 1.5;
    margin-bottom: 10px;
    flex-grow: 1;
  }
  
  .spot-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    
    /* gap: 15px; */
    margin-top: 8px;
  }
  
  .spot-footer .el-button {
    padding: 12px 32px;
    font-size: 16px;
    border-radius: 8px;
    transition: all 0.3s ease;
  }
  
  .spot-footer .el-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  }
  
  .attractions-section {
    margin: 60px 0;
    padding: 0 20px;
  }
  
  .attractions-section h2 {
    font-size: 32px;
    color: #2c3e50;
    margin-bottom: 40px;
    padding-bottom: 15px;
    border-bottom: 3px solid var(--primary-color);
    text-align: center;
    font-weight: 600;
  }
  
  .tour-tags {
    margin: 10px 0;
  }
  
  .tour-tags .el-tag {
    margin-right: 8px;
    transition: all 0.3s ease;
  }
  
  .tour-tags .el-tag:hover {
    transform: scale(1.1);
  }
  
  .tour-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 15px;
  }
  
  :deep(.el-timeline-item__content) {
    h3 {
      margin: 0 0 10px;
      color: #333;
    }
    p {
      color: #666;
      margin-bottom: 10px;
    }
  }
  
  .el-button.animate__pulse {
    animation-duration: 2s;
  }
  
  /* 确保动画元素不会导致页面溢出 */
  .animate__animated {
    will-change: transform, opacity;
    backface-visibility: hidden;
    transform: translateZ(0);
    -webkit-font-smoothing: antialiased;
  }
  
  /* 确保动画元素在容器内正确定位 */
  .el-row {
    margin: 0 !important;
    width: 100%;
    box-sizing: border-box;
  }
  
  .el-col {
    box-sizing: border-box;
  }
  
  @media (max-width: 768px) {
    .travel-container {
      padding: 10px;
      width: 100%;
      box-sizing: border-box;
    }
  
    .el-col {
      width: 100% !important;
    }
    
    .spot-card {
      margin-bottom: 30px;
    }
    
    .spot-content {
      flex-direction: column;
      min-height: auto;
    }
    
    .spot-image-wrapper {
      flex: 0 0 240px;
    }
    
    .spot-info {
      padding: 25px;
    }
    
    .spot-info h3 {
      font-size: 22px;
      margin-bottom: 15px;
    }
    
    .spot-description {
      font-size: 15px;
      margin-bottom: 20px;
    }
    
    .spot-footer {
      flex-direction: column;
      gap: 10px;
    }
    
    .spot-footer .el-button {
      width: 100%;
      padding: 12px 20px;
    }
  
    .search-form {
      :deep(.el-form-item) {
        margin-right: 0;
        width: 100%;
      }
    }
  }
  
  /* 添加动画相关样式 */
  .tours-section {
    opacity: 1;
    transition: opacity 0.3s ease;
  }
  
  .tours-section h2,
  .el-timeline-item,
  .tour-tags .el-tag,
  .tour-footer .el-button {
    opacity: 0;
  }
  
  .tours-section h2.animate__animated,
  .el-timeline-item.animate__animated,
  .tour-tags .el-tag.animate__animated,
  .tour-footer .el-button.animate__animated {
    opacity: 1;
  }
  
  /* 确保动画元素不会影响布局 */
  .animate__animated {
    animation-duration: 1s;
    animation-fill-mode: both;
  }
  
  .el-button.animate__pulse {
    animation-duration: 2s;
  }
  
  /* 优化动画重置效果 */
  .tours-section h2,
  .el-timeline-item,
  .tour-tags .el-tag,
  .tour-footer .el-button {
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  .tours-section h2.animate__animated,
  .el-timeline-item.animate__animated,
  .tour-tags .el-tag.animate__animated,
  .tour-footer .el-button.animate__animated {
    opacity: 1;
  }
  
  /* 确保动画可以重复播放 */
  .animate__animated {
    animation-duration: 1s;
    animation-fill-mode: both;
    animation-iteration-count: 1; /* 每次只播放一次 */
  }
  
  /* 按钮的脉冲动画保持无限循环 */
  .el-button.animate__pulse {
    animation-duration: 2s;
    animation-iteration-count: infinite;
  }
  .page-header h1 {
    font-size: 42px;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    animation: fadeInDown 1s ease-out;
  }
  
  .page-header p {
    font-size: 18px;
    opacity: 0.9;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    animation: fadeInUp 1s ease-out;
  }