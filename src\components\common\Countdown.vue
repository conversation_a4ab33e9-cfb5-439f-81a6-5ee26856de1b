<template>
  <div class="countdown-container">
    <div class="countdown-item">  
      <span class="count">{{ days }}</span>
      <span class="label">{{lang=="EN"?"day":"天"}}</span>
    </div>
    <div class="countdown-item">
      <span class="count">{{ hours }}</span>
      <span class="label">{{lang=="EN"?"hour":"时"}}</span>
    </div>
    <div class="countdown-item">
      <span class="count">{{ minutes }}</span>
      <span class="label">{{lang=="EN"?"minute":"分"}}</span>           
    </div>
    <div class="countdown-item">
      <span class="count">{{ seconds }}</span>
      <span class="label">{{lang=="EN"?"second":"秒"}}</span>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onBeforeUnmount } from 'vue'

export default {
  name: 'Countdown',
  props: {
    targetDate: {
      type: String,
      required: true
    },
    lang:{
      type: String,
      default:"CN"
    }
  },
  setup(props) {
    const days = ref(0)
    const hours = ref(0)
    const minutes = ref(0)
    const seconds = ref(0)
    let interval = null
    
    const calculateTimeLeft = () => {
      const target = new Date(props.targetDate).getTime()
      const now = new Date().getTime()
      const difference = target - now
      
      if (difference > 0) {
        days.value = Math.floor(difference / (1000 * 60 * 60 * 24))
        hours.value = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
        minutes.value = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60))
        seconds.value = Math.floor((difference % (1000 * 60)) / 1000)
      } else {
        days.value = 0
        hours.value = 0
        minutes.value = 0
        seconds.value = 0
      }
    }
    
    onMounted(() => {
      calculateTimeLeft()
      interval = setInterval(calculateTimeLeft, 1000)
    })
    
    onBeforeUnmount(() => {
      clearInterval(interval)
    })
    
    return {
      days,
      hours,
      minutes,
      seconds,
      lang:props.lang
    }
  }
}
</script>

<style scoped>
/* 样式已在全局CSS中定义 */
</style>
