<template>
  <div class="combined-registration-page">
    <!-- 页面头部 -->
    <section class="page-header">
      <div class="container">
        <h1 class="animate__animated animate__fadeInDown">报名参会</h1>
      <p class="animate__animated animate__fadeInUp">2025年10月21-24日 · 上海浦东香格里拉大酒店</p>
      </div>
    </section>
    <div class="conference-overview-container">
    <!-- 报名表单 -->
    <section class="registration-form-section">
      <div class="container">
        <div class="form-container">
          <h2>参会报名</h2>
          <p class="form-desc">
            请填写以下信息完成报名，带 <span class="required">*</span> 为必填项
          </p>

          <!-- 报名人表单 -->
          <el-collapse v-model="activeFormsMain">
            <el-collapse-item name="main">
              <template #title>
                <div class="form-header">
                  <span>【 报名人信息 】 {{ mainForm.firstName }}&nbsp;{{ mainForm.lastName }}</span>
                </div>
              </template>
              <el-form ref="mainFormRef" :model="mainForm" :rules="mainRules" class="registration-form"
                label-position="top">
                <!-- 报名人表单内容 -->
                <div class="form-section">
                  <h3>基本信息</h3>
                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="姓" prop="firstName" required>
                        <el-input v-model="mainForm.firstName" placeholder="请输入姓" />
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="名" prop="lastName" required>
                        <el-input v-model="mainForm.lastName" placeholder="请输入名" />
                      </el-form-item>
                    </div>
                  </div>

                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="性别" prop="sex" required>
                        <el-radio-group v-model="mainForm.sex">
                          <el-radio label="sex_001">男</el-radio>
                          <el-radio label="sex_002">女</el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="国籍" prop="country" required>
                        <el-select v-model="mainForm.country" placeholder="请选择国籍">
                          <el-option v-for="item in countryOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                        </el-select>
                      </el-form-item>
                    </div>
                  </div>

                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="组织/机构" prop="organization" required>
                        <el-input v-model="mainForm.organization" placeholder="请输入组织/机构名称"  />
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="职位" prop="position" required>
                        <el-input v-model="mainForm.position" placeholder="请输入职位"  />
                      </el-form-item>
                    </div>
                  </div>

                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="联系电话" prop="telephone" required>
                        <el-input v-model="mainForm.telephone" placeholder="请输入联系电话"  />
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="通讯地址" prop="telAddr" required>
                        <el-input v-model="mainForm.telAddr" placeholder="请输入通讯地址"  />
                      </el-form-item>
                    </div>
                  </div>

                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="邮箱" prop="email" required>
                        <el-input v-model="mainForm.email" placeholder="请输入邮箱" />
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="护照号码" prop="passportNum" required>
                        <el-input v-model="mainForm.passportNum" placeholder="请输入护照号码"  />
                      </el-form-item>
                    </div>
                  </div>

                  <div class="form-group">
                    <el-form-item label="组织介绍" prop="orgIntroduce" required>
                      <el-input v-model="mainForm.orgIntroduce" type="textarea" :rows="4" placeholder="请输入组织介绍" />
                      <p class="word-count" v-if="mainForm.orgIntroduce">
                        当前字数：{{ countWords(mainForm.orgIntroduce) }}
                      </p>
                    </el-form-item>
                  </div>

                  <div class="form-group" style="margin: 20px 0">
                    <el-form-item label="个人介绍" prop="personalIntroduce" required>
                      <el-input v-model="mainForm.personalIntroduce" type="textarea" :rows="4" placeholder="请输入个人介绍" />
                      <p class="word-count" v-if="mainForm.personalIntroduce">
                        当前字数：{{ countWords(mainForm.personalIntroduce) }}
                      </p>
                    </el-form-item>
                  </div>

                  <div class="form-group">
                    <ImageUpload label="护照照片" prop="passportImageList" v-model="mainForm.passportImageList"
                      :required="true" />
                  </div>
                  <div class="form-group" style="margin: 20px 0">
                    <el-form-item label="参加活动" prop="meetingType" required>
                      <el-checkbox-group v-model="mainForm.meetingType" @change="handleCheckedChange" class="meeting-type-group">
                        <el-checkbox 
                          v-for="item in participationList" 
                          :key="item.value" 
                          :label="item.value"
                          :disabled="isOptionDisabled(item.value)"
                          class="meeting-type-checkbox"
                        >
                          <div class="meeting-type-content">
                            <div class="meeting-type-info">
                              <span class="meeting-date">{{item.date}}</span>
                              <span class="meeting-label">{{item.label}}</span>
                            </div>
                            <span class="meeting-price">{{item.price}}元</span>
                          </div>
                        </el-checkbox>
                      </el-checkbox-group>
                    </el-form-item>
                  </div>
                  <div class="form-row" style="margin: 20px 0">
                    <div class="form-group">
                      <el-form-item label="是否首次来华" prop="firstToCn" required>
                        <el-radio-group v-model="mainForm.firstToCn">
                          <el-radio label="first_to_cn_001">是</el-radio>
                          <el-radio label="first_to_cn_002">否</el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item prop="invitePassportFlag" required>
                        <template #label>
                          是否需要签证邀请函(只涵盖会议期间和路上往返共五天)
                          <!-- <el-tooltip content="只涵盖会议期间和路上往返共五天" placement="top">
                            <el-icon style="margin-left: 2px; cursor: pointer">
                              <QuestionFilled style="padding-top: 2px" />
                            </el-icon>
                          </el-tooltip> -->
                        </template>
                        <el-radio-group v-model="mainForm.invitePassportFlag">
                          <el-radio label="invite_passport_flag_001">是</el-radio>
                          <el-radio label="invite_passport_flag_002">否</el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </div>
                  </div>
                </div>

                <!-- 报名人签证信息部分 -->
                <div class="form-section" v-if="
                  mainForm.invitePassportFlag === 'invite_passport_flag_001'
                ">
                  <h3>签证信息</h3>

                  <div class="form-row">
                    <!-- <div class="form-group">
                      <el-form-item
                        label="性别"
                        prop="visaInfo.visaSex"
                        required
                      >
                        <el-radio-group v-model="mainForm.visaInfo.visaSex">
                          <el-radio label="sex_001">男</el-radio>
                          <el-radio label="sex_002">女</el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </div> -->
                    <div class="form-group">
                      <el-form-item label="出生日期" prop="visaInfo.visaBirth" required>
                        <el-date-picker v-model="mainForm.visaInfo.visaBirth" type="date" placeholder="请选择出生日期"
                          value-format="YYYY-MM-DD" />
                      </el-form-item>
                    </div>

                    <div class="form-group">
                      <el-form-item label="入境日期" prop="visaInfo.visaEnterDate" required>
                        <el-date-picker v-model="mainForm.visaInfo.visaEnterDate" type="date" placeholder="请选择入境日期"
                          value-format="YYYY-MM-DD" :disabled-date="disableOtherDates"/>
                      </el-form-item>
                    </div>
                  </div>

                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="离境日期" prop="visaInfo.visaLeaveDate" required>
                        <el-date-picker v-model="mainForm.visaInfo.visaLeaveDate" type="date" placeholder="请选择离境日期"
                          value-format="YYYY-MM-DD" :disabled-date="disableOtherDates"/>
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="紧急联系人" prop="visaInfo.visaUrgentContacter" required>
                        <el-input v-model="mainForm.visaInfo.visaUrgentContacter" placeholder="请输入紧急联系人" />
                      </el-form-item>
                    </div>
                  </div>

                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="申请护照地点" prop="visaInfo.visaApplyPassportSite" required>
                        <!-- <el-select
                          v-model="mainForm.visaInfo.visaApplyPassportSite"
                          placeholder="请选择申请护照地点"
                        >
                          <el-option
                            v-for="item in passportSiteOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select> -->
                        <el-input v-model="mainForm.visaInfo.visaApplyPassportSite" placeholder="请选择申请护照地点" />
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="访问地点" prop="visaInfo.visaVisitSite" required>
                        <!-- <el-select
                          v-model="mainForm.visaInfo.visaVisitSite"
                          placeholder="请选择访问地点"
                        >
                          <el-option
                            v-for="item in visitSiteOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select> -->
                        <el-input v-model="mainForm.visaInfo.visaVisitSite" placeholder="请输入请选择访问地点" />
                      </el-form-item>
                    </div>
                  </div>
                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="邀请函接收邮箱" prop="visaInfo.visaEmail" required>
                        <el-input v-model="mainForm.visaInfo.visaEmail" placeholder="请输入邀请函接收邮箱" />
                      </el-form-item>
                    </div>
                  </div>
                </div>
              </el-form>
            </el-collapse-item>
          </el-collapse>
          <!-- 同行人表单 -->

        
          <el-collapse v-model="activeForms" @change="handleCollapseChange">
            <el-collapse-item v-for="(companion, index) in companions" :key="index" :name="'companion-' + index">
              <template #title>
                <div class="form-header">
                  <span>【 同行人信息 】{{ companion.firstName }}&nbsp;{{ companion.lastName }}</span>
                  <el-button type="danger" size="small" @click.stop="removeCompanion(companion, index)">
                    删除
                  </el-button>
                </div>
              </template>
              <el-form :ref="(el) => (companionFormRefs[index] = el)" :model="companion" :rules="companionRules"
                class="companion-form" label-position="top">
                <div class="form-section">
                  <h3>基本信息</h3>
                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="姓" prop="firstName" required>
                        <el-input v-model="companion.firstName" placeholder="请输入姓" />
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="名" prop="lastName" required>
                        <el-input v-model="companion.lastName" placeholder="请输入名" />
                      </el-form-item>
                    </div>
                  </div>

                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="性别" prop="sex" required>
                        <el-radio-group v-model="companion.sex">
                          <el-radio label="sex_001">男</el-radio>
                          <el-radio label="sex_002">女</el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="国籍" prop="country" required>
                        <el-select v-model="companion.country" placeholder="请选择国籍">
                          <el-option v-for="item in countryOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                        </el-select>
                      </el-form-item>
                    </div>
                  </div>

                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="组织/机构" prop="organization" required>
                        <el-input v-model="companion.organization" placeholder="请输入组织/机构名称"  />
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="职位" prop="position" required>
                        <el-input v-model="companion.position" placeholder="请输入职位" />
                      </el-form-item>
                    </div>
                  </div>

                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="联系电话" prop="telephone" required>
                        <el-input v-model="companion.telephone" placeholder="请输入联系电话"/>
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="通讯地址" prop="telAddr" required>
                        <el-input v-model="companion.telAddr" placeholder="请输入通讯地址"  />
                      </el-form-item>
                    </div>
                  </div>

                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="邮箱" prop="email" required>
                        <el-input v-model="companion.email" placeholder="请输入邮箱" />
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="护照号码" prop="passportNum" required>
                        <el-input v-model="companion.passportNum" placeholder="请输入护照号码" />
                      </el-form-item>
                    </div>
                  </div>

                  <div class="form-group">
                    <el-form-item label="组织介绍" prop="orgIntroduce" required>
                      <el-input v-model="companion.orgIntroduce" type="textarea" :rows="4" placeholder="请输入组织介绍" />
                      <p class="word-count" v-if="companion.orgIntroduce">
                        当前字数：{{ countWords(companion.orgIntroduce) }}
                      </p>
                    </el-form-item>
                  </div>

                  <div class="form-group" style="margin: 20px 0">
                    <el-form-item label="个人介绍" prop="personalIntroduce" required>
                      <el-input v-model="companion.personalIntroduce" type="textarea" :rows="4" placeholder="请输入个人介绍" />
                      <p class="word-count" v-if="companion.personalIntroduce">
                        当前字数：{{ countWords(companion.personalIntroduce) }}
                      </p>
                    </el-form-item>
                  </div>

                  <div class="form-group">
                    <ImageUpload label="护照照片" prop="passportImageList" v-model="companion.passportImageList"
                      :required="true" />
                  </div>
                  <div class="form-group" style="margin: 20px 0">
                    <el-form-item label="参加活动" prop="meetingType" required>
                      <!-- <el-checkbox :indeterminate="companion.isIndeterminate" v-model="companion.checkAll" @change="(val) => handleCompanionCheckAllChange(val, index)">全选 (优惠价8000元）</el-checkbox> -->
                      <el-checkbox-group v-model="companion.meetingType" @change="(val) => handleCompanionCheckedChange(val, index)" class="meeting-type-group">
                       <!-- <el-checkbox 
                          v-for="item in participationList" 
                          :key="item.value" 
                          :label="item.value"
                          class="meeting-type-checkbox"
                        >
                          <div class="meeting-type-content">
                            <div class="meeting-type-info">
                              <span class="meeting-date">{{item.date}}</span>
                              <span class="meeting-label">{{item.label}}</span>
                            </div>
                            <span class="meeting-price">{{item.price}}</span>
                          </div>
                        </el-checkbox>
                        -->
                      </el-checkbox-group>
                      <el-checkbox-group v-model="companion.meetingType" @change="(val) => handleCompanionCheckedChange(val, index)" class="meeting-type-group">
                        <el-checkbox 
                          v-for="item in participationList" 
                          :key="item.value" 
                          :label="item.value"
                          :disabled="isCompanionOptionDisabled(item.value, index)"
                          class="meeting-type-checkbox"
                        >
                          <div class="meeting-type-content">
                            <div class="meeting-type-info">
                              <span class="meeting-date">{{item.date}}</span>
                              <span class="meeting-label">{{item.label}}</span>
                            </div>
                            <span class="meeting-price">{{item.price}}元</span>
                          </div>
                        </el-checkbox>
                      </el-checkbox-group>
                    </el-form-item>
                  </div>
                  <div class="form-row" style="margin: 20px 0">
                    <div class="form-group">
                      <el-form-item label="是否首次来华" prop="firstToCn" required>
                        <el-radio-group v-model="companion.firstToCn">
                          <el-radio label="first_to_cn_001">是</el-radio>
                          <el-radio label="first_to_cn_002">否</el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </div>
              
                    <div class="form-group">
                      <el-form-item prop="invitePassportFlag" required>
                        <template #label>
                          是否需要签证邀请函(只涵盖会议期间和路上往返共五天)
                          <!-- <el-tooltip content="只涵盖会议期间和路上往返共五天" placement="top">
                            <el-icon style="margin-left: 2px; cursor: pointer">
                              <QuestionFilled style="padding-top: 2px" />
                            </el-icon>
                          </el-tooltip> -->
                        </template>
                        <el-radio-group v-model="companion.invitePassportFlag">
                          <el-radio label="invite_passport_flag_001">是</el-radio>
                          <el-radio label="invite_passport_flag_002">否</el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </div>
                  </div>
                </div>

                <!-- 同行人签证信息部分 -->
                <div class="form-section" v-if="
                  companion.invitePassportFlag === 'invite_passport_flag_001'
                ">
                  <h3>签证信息</h3>
                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="出生日期" prop="visaInfo.visaBirth" required>
                        <el-date-picker v-model="companion.visaInfo.visaBirth" type="date" placeholder="请选择出生日期"
                          value-format="YYYY-MM-DD" />
                      </el-form-item>
                    </div>
                    <!-- </div>

                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item
                        label="国籍"
                        prop="visaInfo.visaCountry"
                        required
                      >
                        <el-select
                          v-model="companion.visaInfo.visaCountry"
                          placeholder="请选择国籍"
                        >
                          <el-option
                            v-for="item in countryOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </div> -->
                    <div class="form-group">
                      <el-form-item label="入境日期" prop="visaInfo.visaEnterDate" required>
                        <el-date-picker v-model="companion.visaInfo.visaEnterDate" type="date" placeholder="请选择入境日期"
                          value-format="YYYY-MM-DD" :disabled-date="disableOtherDates" :default-value="new Date('2025-10-01')"/>
                      </el-form-item>
                    </div>
                  </div>

                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="离境日期" prop="visaInfo.visaLeaveDate" required>
                        <el-date-picker v-model="companion.visaInfo.visaLeaveDate" type="date" placeholder="请选择离境日期"
                          value-format="YYYY-MM-DD" :disabled-date="disableOtherDates" :default-value="new Date('2025-10-01')"/>
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="紧急联系人" prop="visaInfo.visaUrgentContacter" required>
                        <el-input v-model="companion.visaInfo.visaUrgentContacter" placeholder="请输入紧急联系人" />
                      </el-form-item>
                    </div>
                  </div>

                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="申请护照地点" prop="visaInfo.visaApplyPassportSite" required>
                        <el-input v-model="companion.visaInfo.visaApplyPassportSite" placeholder="请选择申请护照地点" />
                      </el-form-item>
                    </div>
                    <div class="form-group">
                      <el-form-item label="访问地点" prop="visaInfo.visaVisitSite" required>
                        <el-input v-model="companion.visaInfo.visaVisitSite" placeholder="请输入请选择访问地点" />
                      </el-form-item>
                    </div>
                  </div>
                  <div class="form-row">
                    <div class="form-group">
                      <el-form-item label="邀请函接收邮箱" prop="visaInfo.visaEmail" required>
                        <el-input v-model="companion.visaInfo.visaEmail" placeholder="请输入邀请函接收邮箱" />
                      </el-form-item>
                    </div>
                  </div>
                </div>
              </el-form>
            </el-collapse-item>
          </el-collapse>
          <div style="
              display: flex;
              justify-content: flex-end;
              margin: 12px 42px 12px 0;
            ">
            <el-button type="primary" @click="addCompanion" size="small" :disabled="companions.length >= 5">
              添加同行人
            </el-button>
          </div>
          <div class="total-amount" >
            <span class="amount-label">总计金额：</span>
            <span class="amount-value">¥{{ calculateAmount() }}</span>
          </div>
          <!-- 参会信息 -->
          <!-- <div class="form-section" style="margin-top: 10px;">
            <p>参会信息</p>
            <ConferenceRegistration v-model="mainForm" :companions="companions" />
          </div> -->

          <!-- 操作按钮 -->
          <div class="form-actions">
            <el-button type="primary" @click="submitAll('save')" :loading="isSubmitting">
              保存
            </el-button>
            <el-button type="success" @click="submitAll('pay')" :loading="isSubmitting">
              支付
            </el-button>
            <!-- <el-button @click="goBack">返回</el-button> -->
          </div>
        </div>
      </div>
    </section>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, nextTick } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
// import ConferenceRegistration from "@/components/registration/ConferenceRegistration.vue";
import ImageUpload from "@/components/common/ImageUpload.vue";
import {
  submitRegistration,
  updateRegistration,
  submitCompanion,
  updatePeerInfo,
  deletePeerInfo,
  passportInfo
} from "@/api/registration";
import { getHotelInfo } from '@/api/agenda'
import { getDictCode } from "@/api/dictCode";
import { useUserStore } from "@/stores/user";
import { useRegistrationStore } from "@/stores/registration";
import dayjs from "dayjs";
import { QuestionFilled } from "@element-plus/icons-vue";
import { useLanguage } from '@/hooks/useLanguage'
// 返回 'chinese' 或 'english'
export default {
  name: "CombinedRegistration",
  components: {
    // ConferenceRegistration,
    ImageUpload,
    QuestionFilled,
  },
  setup() {
    const router = useRouter();
    const userStore = useUserStore();
    const registrationStore = useRegistrationStore();
    const isSubmitting = ref(false);
    const activeFormsMain = ref(["main"]);
    const activeForms = ref([]);
    const mainFormRef = ref(null);
    const companionFormRefs = ref([]);
    const isEdit = ref(false);
    const { currentLanguage, getLocalePath } = useLanguage()
    // console.log(currentLanguage.value)
    
    // <el-checkbox label="1">欢迎晚宴(1000元)</el-checkbox>
    //                       <el-checkbox label="2">国际CMC大会(2000元)</el-checkbox>
    //                       <el-checkbox label="3">ICMCI年会(3000元)</el-checkbox>
    //                       <el-checkbox label="4">颁奖(4000元)</el-checkbox>
    // // 报名人表单数据
    const isIndeterminate = ref(false);
    const checkAll = ref(false);
    const participationList = ref([
      {
        date:'10月21日',
        label:'欢迎酒会',
        price:'400',
        value:'1'
      },
      {
        date:'10月22日至23日',
        label:'会议现场',
        price:'3600',
        value:'2'
      },
      {
        date:'10月22日',
        label:'君士坦丁奖及晚宴',
        price:'980',
        value:'3'
      },
      {
        date:'10月23日至24日',
        label:'年会现场',
        price:'1960',
        value:'4'
      },
      {
        date:'10月23日至24日',
        label:'年会在线',
        price:'800',
        value:'5'
      },
      {
        date:'优惠价格',
        label:'参加欢迎酒会、会议现场、君士坦丁奖及晚宴',
        price:'4500',
        value:'6'
      },
      // {
      //   date:'优惠价格',
      //   label:'参加年会现场或在线',
      //   price:'200',
      //   value:'7'
      // }
    ])
    const mainForm = ref({
      // 基本信息

      userId: userStore.userInfo.id,
      // firstName: "fengbao",
      // lastName: "wei",
      // sex: "sex_001",
      // country: "country_code_1",
      // organization: "organization_001",
      // position: "position_001",
      // telephone: "***********",
      // telAddr: "telAddr_001",
      // email: "<EMAIL>",
      // passportNum: "**********",
      // firstToCn: "first_to_cn_001",
      // invitePassportFlag: "invite_passport_flag_002",
      // orgIntroduce: "p er so n al Intr o  d  u c e_0 0 1",
      // personalIntroduce: "p er so n al Intr o  d  u c e_0 0 1",
      // passportImageList: ['/group1/M00/00/40/wKhxXmgkQvyAejGGAAfDs8JmB5I805.jpg'],

      firstName: "",
      lastName: "",
      sex: "",
      country: "",
      organization: "",
      position: "",
      telephone: "",
      telAddr: "",
      email: "",
      passportNum: "",
      firstToCn: "",
      invitePassportFlag: "",
      orgIntroduce: "",
      personalIntroduce: "",
      passportImageList: [],
      meetingType: '',
      // 签证信息
      visaInfo: {
        visaFirstName: "",
        visaLastName: "",
        visaSex: "",
        visaBirth: "",
        visaCountry: "",
        visaEnterDate: "",
        visaLeaveDate: "",
        visaUrgentContacter: "",
        visaEmail: "",
        visaOrganization: "",
        visaPosition: "",
        visaApplyPassportSite: "",
        visaVisitSite: "",
        visaOrgIntroduce: "",
        visaPersonalIntroduce: "",
        personType: "",
      },
      // 参会信息
      meetingType: [],
      forums: [],
    });

    // 同行人列表
    const companions = ref([]);

    // 国籍选项
    const countryOptions = ref([]);
    // 护照申请地点选项
    const passportSiteOptions = [
      { value: "passport_site_001", label: "北京" },
      { value: "passport_site_002", label: "上海" },
      { value: "passport_site_003", label: "广州" },
      // ... 其他地点选项
    ];

    // 访问地点选项
    const visitSiteOptions = [
      { value: "visit_site_001", label: "北京" },
      { value: "visit_site_002", label: "上海" },
      { value: "visit_site_003", label: "广州" },
      // ... 其他地点选项
    ];

    // 论坛选项
    const forumOptions = [
      {
        value: "3",
        name: "各国CMC交流管理咨询经验，探讨管理咨询新趋势",
        price: 500,
      },
      {
        value: "4",
        name: "探讨合作机会，推动企业提升管理水平",
        price: 500,
      },
      {
        value: "5",
        name: "颁发国际管理咨询案例奖（君士坦丁奖）",
        price: 500,
      },
      {
        value: "6",
        name: "总结过去一年工作,主要包括各成员国组织交流经验、战略执行、会员发展、业务项目进展及财务决算",
        price: 300,
      },
      {
        value: "7",
        name: "审议财务预算，增补执委会、相关专业委员会委员和理事",
        price: 300,
      },
    ];

    // 报名人表单验证规则
    const mainRules = {
      firstName: [
        { required: true, message: "请输入姓", trigger: "blur" },
        { min: 1, max: 64, message: "姓长度不能超过64个字符", trigger: "blur" },
        { 
          validator: async (rule, value, callback) => {
            if (!value || !mainForm.value.lastName || !mainForm.value.passportNum) {
              callback();
              return;
            }
            try {
              // 检查是否与任何同行人重复
              const isDuplicate = companions.value.some(companion => 
                companion.firstName === value && 
                companion.lastName === mainForm.value.lastName && 
                companion.passportNum === mainForm.value.passportNum
              );
              
              if (isDuplicate) {
                callback(new Error('该护照信息与同行人重复'));
                return;
              }

              const res = await passportInfo({
                firstName: value,
                lastName: mainForm.value.lastName,
                passportNum: mainForm.value.passportNum,
                signUpInfoId: mainForm.value.id
              });
              if (res.data.data) {
                callback(new Error('该用户已报名'));
              } else {
                callback();
                // 清除关联字段的验证状态
                mainFormRef.value?.clearValidate(['lastName', 'passportNum']);
              }
            } catch (error) {
              callback();
            }
          },
          trigger: 'blur'
        }
      ],
      lastName: [
        { required: true, message: "请输入名", trigger: "blur" },
        { min: 1, max: 64, message: "名长度不能超过64个字符", trigger: "blur" },
        { 
          validator: async (rule, value, callback) => {
            if (!value || !mainForm.value.firstName || !mainForm.value.passportNum) {
              callback();
              return;
            }
            try {
              // 检查是否与任何同行人重复
              const isDuplicate = companions.value.some(companion => 
                companion.firstName === mainForm.value.firstName && 
                companion.lastName === value && 
                companion.passportNum === mainForm.value.passportNum
              );
              
              if (isDuplicate) {
                callback(new Error('该护照信息与同行人重复'));
                return;
              }

              const res = await passportInfo({
                firstName: mainForm.value.firstName,
                lastName: value,
                passportNum: mainForm.value.passportNum,
                signUpInfoId: mainForm.value.id
              });
              if (res.data.data) {
                callback(new Error('该用户已报名'));
              } else {
                callback();
                // 清除关联字段的验证状态
                mainFormRef.value?.clearValidate(['firstName', 'passportNum']);
              }
            } catch (error) {
              callback();
            }
          },
          trigger: 'blur'
        }
      ],
      passportNum: [
        { required: true, message: "请输入护照号码", trigger: "blur" },
        { min: 1, max: 16, message: "护照号码长度不能超过16个字符", trigger: "blur" },
        { 
          validator: async (rule, value, callback) => {
            if (!value || !mainForm.value.firstName || !mainForm.value.lastName) {
              callback();
              return;
            }
            try {
              // 检查是否与任何同行人重复
              const isDuplicate = companions.value.some(companion => 
                companion.firstName === mainForm.value.firstName && 
                companion.lastName === mainForm.value.lastName && 
                companion.passportNum === value
              );
              
              if (isDuplicate) {
                callback(new Error('该护照信息与同行人重复'));
                return;
              }

              const res = await passportInfo({
                firstName: mainForm.value.firstName,
                lastName: mainForm.value.lastName,
                passportNum: value,
                signUpInfoId: mainForm.value.id
              });
              if (res.data.data) {
                callback(new Error('该用户已报名'));
              } else {
                callback();
                // 清除关联字段的验证状态
                mainFormRef.value?.clearValidate(['firstName', 'lastName']);
              }
            } catch (error) {
              callback();
            }
          },
          trigger: 'blur'
        }
      ],
      telAddr: [{ required: true, message: "请输入通讯地址", trigger: "blur" },
      { min: 1, max: 255, message: "通讯地址长度不能超过255个字符", trigger: "blur" }
      ],
      sex: [{ required: true, message: "请选择性别", trigger: "change" }],
      country: [{ required: true, message: "请选择国籍", trigger: "change" }],
      organization: [
        { required: true, message: "请输入组织/机构", trigger: "blur" },
        { min: 1, max: 128, message: "组织/机构名称长度不能超过128个字符", trigger: "blur" }
      ],
      position: [
        { required: true, message: "请输入职位", trigger: "blur" },
        { min: 1, max: 64, message: "职位长度不能超过64个字符", trigger: "blur" }
      ],
      telephone: [
        { required: true, message: "请输入联系电话", trigger: "blur" },
        { min: 1, max: 32, message: "联系电话长度不能超过32个字符", trigger: "blur" }
      ],
      email: [
        { required: true, message: "请输入邮箱", trigger: "blur" },
        { type: "email", message: "请输入有效的邮箱地址", trigger: "blur" },
      ],
      firstToCn: [
        { required: true, message: "请选择是否首次来华", trigger: "change" },
      ],
      invitePassportFlag: [
        {
          required: true,
          message: "请选择是否需要签证邀请函",
          trigger: "change",
        },
      ],
      meetingType: [
        { required: true, message: "请选择参会类型", trigger: "change" },
      ],
      orgIntroduce: [
        { required: true, message: "请输入组织介绍", trigger: "blur" },
        {
          validator: (rule, value, callback) => {
            if (!value) {
              callback();
              return;
            }
            const wordCount = countWords(value);
            if (wordCount < 30) {
              callback(new Error("组织介绍至少需要30个字符"));
            } else if (wordCount > 150) {
              callback(new Error("组织介绍最多不能超过150个字符"));
            } else {
              callback();
            }
          },
          trigger: "blur",
        },
      ],
      personalIntroduce: [
        { required: true, message: "请输入个人介绍", trigger: "blur" },
        {
          validator: (rule, value, callback) => {
            if (!value) {
              callback();
              return;
            }
            const wordCount = countWords(value);
            if (wordCount < 30) {
              callback(new Error("个人介绍至少需要30个字符"));
            } else if (wordCount > 150) {
              callback(new Error("个人介绍最多不能超过150个字符"));
            } else {
              callback();
            }
          },
          trigger: "blur",
        },
      ],
      passportImageList: [
        { required: true, message: "请上传护照照片", trigger: "change" },
        {
          validator: (rule, value, callback) => {
            if (!value) {
              callback(new Error("请上传护照照片"));
            } else {
              callback();
            }
          },
          trigger: "change",
        },
      ],
      'visaInfo.visaBirth': [
        { required: true, message: "请选择出生日期", trigger: "change" },
      ],
      'visaInfo.visaEnterDate': [
        { required: true, message: "请选择入境日期", trigger: "change" },
      ],
      'visaInfo.visaLeaveDate': [
        { required: true, message: "请选择离境日期", trigger: "change" },
      ],
      'visaInfo.visaUrgentContacter': [
        { required: true, message: "请输入紧急联系人", trigger: "blur" },
      ],
      'visaInfo.visaEmail': [
        { required: true, message: "请输入邀请函接收邮箱", trigger: "blur" },
        { type: "email", message: "请输入有效的邮箱地址", trigger: "blur" },
      ],
      'visaInfo.visaApplyPassportSite': [
        { required: true, message: "请输入申请护照地点", trigger: "blur" },
      ],
      'visaInfo.visaVisitSite': [
        { required: true, message: "请输入访问地点", trigger: "blur" },
      ],
    };

    // 同行人表单验证规则
    const companionRules = {
      firstName: [
        { required: true, message: "请输入姓", trigger: "blur" },
        { 
          validator: async (rule, value, callback) => {
            const companion = companions.value.find(c => c.firstName === value);
            if (!value || !companion?.lastName || !companion?.passportNum) {
              callback();
              return;
            }
            try {
              // 检查是否与报名人重复
              if (mainForm.value.firstName === value && 
                  mainForm.value.lastName === companion.lastName && 
                  mainForm.value.passportNum === companion.passportNum) {
                callback(new Error('该护照信息与报名人重复'));
                return;
              }

              // 检查是否与其他同行人重复
              const isDuplicate = companions.value.some(c => 
                c !== companion && 
                c.firstName === value && 
                c.lastName === companion.lastName && 
                c.passportNum === companion.passportNum
              );
              
              if (isDuplicate) {
                callback(new Error('该护照信息与其他同行人重复'));
                return;
              }

              const res = await passportInfo({
                firstName: value,
                lastName: companion.lastName,
                passportNum: companion.passportNum,
                peerInfoId: companion.id
              });
              if (res.data.data) {
                callback(new Error('该用户已报名'));
              } else {
                callback();
                // 获取当前同行人的索引
                const index = companions.value.findIndex(c => c.firstName === value);
                if (index !== -1 && companionFormRefs.value[index]) {
                  companionFormRefs.value[index].clearValidate(['lastName', 'passportNum']);
                }
              }
            } catch (error) {
              callback();
            }
          },
          trigger: 'blur'
        }
      ],
      lastName: [
        { required: true, message: "请输入名", trigger: "blur" },
        { 
          validator: async (rule, value, callback) => {
            const companion = companions.value.find(c => c.lastName === value);
            if (!value || !companion?.firstName || !companion?.passportNum) {
              callback();
              return;
            }
            try {
              // 检查是否与报名人重复
              if (mainForm.value.firstName === companion.firstName && 
                  mainForm.value.lastName === value && 
                  mainForm.value.passportNum === companion.passportNum) {
                callback(new Error('该护照信息与报名人重复'));
                return;
              }

              // 检查是否与其他同行人重复
              const isDuplicate = companions.value.some(c => 
                c !== companion && 
                c.firstName === companion.firstName && 
                c.lastName === value && 
                c.passportNum === companion.passportNum
              );
              
              if (isDuplicate) {
                callback(new Error('该护照信息与其他同行人重复'));
                return;
              }

              const res = await passportInfo({
                firstName: companion.firstName,
                lastName: value,
                passportNum: companion.passportNum,
                peerInfoId: companion.id
              });
              if (res.data.data) {
                callback(new Error('该用户已报名'));
              } else {
                callback();
                // 获取当前同行人的索引
                const index = companions.value.findIndex(c => c.lastName === value);
                if (index !== -1 && companionFormRefs.value[index]) {
                  companionFormRefs.value[index].clearValidate(['firstName', 'passportNum']);
                }
              }
            } catch (error) {
              callback();
            }
          },
          trigger: 'blur'
        }
      ],
      passportNum: [
        { required: true, message: "请输入护照号码", trigger: "blur" },
        { min: 1, max: 16, message: "护照号码长度不能超过16个字符", trigger: "blur" },
        { 
          validator: async (rule, value, callback) => {
            const companion = companions.value.find(c => c.passportNum === value);
            if (!value || !companion?.firstName || !companion?.lastName) {
              callback();
              return;
            }
            try {
              // 检查是否与报名人重复
              if (mainForm.value.firstName === companion.firstName && 
                  mainForm.value.lastName === companion.lastName && 
                  mainForm.value.passportNum === value) {
                callback(new Error('该护照信息与报名人重复'));
                return;
              }

              // 检查是否与其他同行人重复
              const isDuplicate = companions.value.some(c => 
                c !== companion && 
                c.firstName === companion.firstName && 
                c.lastName === companion.lastName && 
                c.passportNum === value
              );
              
              if (isDuplicate) {
                callback(new Error('该护照信息与其他同行人重复'));
                return;
              }

              const res = await passportInfo({
                firstName: companion.firstName,
                lastName: companion.lastName,
                passportNum: value,
                peerInfoId: companion.id
              });
              if (res.data.data) {
                callback(new Error('该用户已报名'));
              } else {
                callback();
                // 获取当前同行人的索引
                const index = companions.value.findIndex(c => c.passportNum === value);
                if (index !== -1 && companionFormRefs.value[index]) {
                  companionFormRefs.value[index].clearValidate(['firstName', 'lastName']);
                }
              }
            } catch (error) {
              callback();
            }
          },
          trigger: 'blur'
        }
      ],
      sex: [{ required: true, message: "请选择性别", trigger: "change" }],
      country: [{ required: true, message: "请选择国籍", trigger: "change" }],
      organization: [
        { required: true, message: "请输入组织/机构", trigger: "blur" },
        { min: 1, max: 128, message: "组织/机构名称长度不能超过128个字符", trigger: "blur" }
      ],
      position: [
        { required: true, message: "请输入职位", trigger: "blur" },
        { min: 1, max: 64, message: "职位长度不能超过64个字符", trigger: "blur" }
      ],
      telephone: [
        { required: true, message: "请输入联系电话", trigger: "blur" },
        { min: 1, max: 32, message: "联系电话长度不能超过32个字符", trigger: "blur" }
      ],
      telAddr: [
        { required: true, message: "请输入通讯地址", trigger: "blur" },
        { min: 1, max: 255, message: "通讯地址长度不能超过255个字符", trigger: "blur" }
      ],
      email: [
        { required: true, message: "请输入邮箱", trigger: "blur" },
        { type: "email", message: "请输入有效的邮箱地址", trigger: "blur" },
      ],
      firstToCn: [
        { required: true, message: "请选择是否首次来华", trigger: "change" },
      ],
      invitePassportFlag: [
        {
          required: true,
          message: "请选择是否需要签证邀请函",
          trigger: "change",
        },
      ],
      orgIntroduce: [
        { required: true, message: "请输入组织介绍", trigger: "blur" },
        {
          validator: (rule, value, callback) => {
            if (!value) {
              callback();
              return;
            }
            const wordCount = countWords(value);
           if (wordCount < 30) {
              callback(new Error("组织介绍至少需要30个字符"));
            } else if (wordCount > 150) {
              callback(new Error("组织介绍最多不能超过150个字符"));
            } else {
              callback();
            }
          },
          trigger: "blur",
        },
      ],
      personalIntroduce: [
        { required: true, message: "请输入个人介绍", trigger: "blur" },
        {
          validator: (rule, value, callback) => {
            if (!value) {
              callback();
              return;
            }
            const wordCount = countWords(value);
            if (wordCount < 30) {
              callback(new Error("个人介绍至少需要30个字符"));
            } else if (wordCount > 150) {
              callback(new Error("个人介绍最多不能超过150个字符"));
            } else {
              callback();
            }
          },
          trigger: "blur",
        },
      ],
      passportImageList: [
        { required: true, message: "请上传护照照片", trigger: "change" },
        {
          validator: (rule, value, callback) => {
            if (!value) {
              callback(new Error("请上传护照照片"));
            } else {
              callback();
            }
          },
          trigger: "change",
        },
      ],
      meetingType: [
        { required: true, message: "请选择参会类型", trigger: "change" },
        { 
          validator: (rule, value, callback) => {
            if (!value || value.length === 0) {
              callback(new Error('请至少选择一个参会类型'));
            } else {
              callback();
            }
          },
          trigger: 'change'
        }
      ],
      'visaInfo.visaBirth': [
        { required: true, message: "请选择出生日期", trigger: "change" },
      ],
      'visaInfo.visaEnterDate': [
        { required: true, message: "请选择入境日期", trigger: "change" },
      ],
      'visaInfo.visaLeaveDate': [
        { required: true, message: "请选择离境日期", trigger: "change" },
      ],
      'visaInfo.visaUrgentContacter': [
        { required: true, message: "请输入紧急联系人", trigger: "blur" },
      ],
      'visaInfo.visaEmail': [
        { required: true, message: "请输入邀请函接收邮箱", trigger: "blur" },
        { type: "email", message: "请输入有效的邮箱地址", trigger: "blur" },
      ],
      'visaInfo.visaApplyPassportSite': [
        { required: true, message: "请输入申请护照地点", trigger: "blur" },
      ],
      'visaInfo.visaVisitSite': [
        { required: true, message: "请输入访问地点", trigger: "blur" },
      ],
    };

    // 签证信息验证规则
    const visaRules = {
      visaBirth: [
        { required: true, message: "请选择出生日期", trigger: "change" },
      ],
      visaEnterDate: [
        { required: true, message: "请选择入境日期", trigger: "change" },
      ],
      visaLeaveDate: [
        { required: true, message: "请选择离境日期", trigger: "change" },
      ],
      visaUrgentContacter: [
        { required: true, message: "请输入紧急联系人", trigger: "blur" },
      ],
      visaEmail: [
        { required: true, message: "请输入邀请函接收邮箱", trigger: "blur" },
        { type: "email", message: "请输入有效的邮箱地址", trigger: "blur" },
      ],
      visaApplyPassportSite: [
        { required: true, message: "请输入申请护照地点", trigger: "blur" },
      ],
      visaVisitSite: [
        { required: true, message: "请输入访问地点", trigger: "blur" },
      ],
    };

    // 加载数据
    const loadData = async () => {
      try {
        const savedData = sessionStorage.getItem("registrationFormData");
        if (savedData) {
          const data = JSON.parse(savedData);
            console.log(1,data)
          // 处理报名人数据

          const types = data.meetingType.split(',')
          mainForm.value = {
            ...data,
            meetingType: types,
            visaInfo: {
              ...mainForm.value.visaInfo,
              ...(data.visaInfo || {}),
            },
          };
          
          // 设置报名人的全选状态
          checkAll.value = types.length === 4;
          isIndeterminate.value = types.length > 0 && types.length < 4;

          isEdit.value = true;

          // 加载同行人数据
          const companionData = data?.peerInfoList || [];

          if (companionData.length > 0) {
            companions.value = companionData.map((item) => {
              const types = item.meetingType.split(',')
              return {
                ...item,
                visaInfo: item.meetingVisaInfo || {},
                meetingType: types,
                checkAll: types.length === 4,
                isIndeterminate: types.length > 0 && types.length < 4
              }
            });
          } else {
            companions.value = [];
          }
          sessionStorage.removeItem("registrationFormData");
        }
        // 重置表单验证状态
        nextTick(() => {
          if (mainFormRef.value) {
            mainFormRef.value.clearValidate();
          }
          companionFormRefs.value.forEach((formRef) => {
            if (formRef) {
              formRef.clearValidate();
            }
          });
        });
      } catch (error) {
        console.log(error);
      }
    };
    const handleCheckAllChange = (val) => {
      mainForm.value.meetingType = val ? participationList.value.map(item => item.value) : [];
      isIndeterminate.value = false;
    };

    const handleCheckedChange = (value) => {
      // 选中全包，只能选它
      if (value.includes('6')) {
        mainForm.value.meetingType = ['6'];
        return;
      }
      // 选中第二个优惠价，只能选 4、5、7
      if (value.includes('7')) {
        mainForm.value.meetingType = value.filter(v => ['4', '5', '7'].includes(v));
        return;
      }
      // 其它情况，正常处理
      const checkedCount = value.length;
      checkAll.value = checkedCount === participationList.value.length;
      isIndeterminate.value = checkedCount > 0 && checkedCount < participationList.value.length;
    };

    onMounted(() => {
      loadData();
      if(userStore.userInfo.account){
        getHotelInfo(userStore.userInfo.account).then(res=>{
          console.log(res)
          hotelMoney.value = res.data.data;
        })
      }
      getDictCode("country_code").then((res) => {
        countryOptions.value = res.data.data.map((item) => ({
          value: item.dictionaryCode,
          label: item.dictionaryValue,
        }));
      });
    });

    // 添加同行人
    const addCompanion = () => {
      if (companions.value.length >= 5) {
        ElMessage.warning("最多只能添加5位同行人");
        return;
      }

      const newCompanion = {
        // firstName: "fengbao",
        // lastName: "wei",
        // sex: "sex_001",
        // country: "country_code_1",
        // organization: "organization_001",
        // position: "position_001",
        // telephone: "***********",
        // telAddr: "telAddr_001",
        // email: "<EMAIL>",
        // passportNum: "**********",
        // firstToCn: "first_to_cn_001",
        // invitePassportFlag: "invite_passport_flag_002",
        // orgIntroduce: "p er so n al Intr o  d  u c e_0 0 1",
        // personalIntroduce: "p er so n al Intr o  d  u c e_0 0 1",
        // passportImageList: ['/group1/M00/00/40/wKhxXmgkQvyAejGGAAfDs8JmB5I805.jpg '],



        firstName: "",
        lastName: "",
        sex: "",
        country: "",
        organization: "",
        position: "",
        telephone: "",
        telAddr: "",
        email: "",
        passportNum: "",
        firstToCn: "",
        invitePassportFlag: "",
        orgIntroduce: "",
        personalIntroduce: "",
        passportImageList: [],

        meetingType: [],
        checkAll: false,
        isIndeterminate: false,
        visaInfo: {
          visaFirstName: "",
          visaLastName: "",
          visaSex: "",
          visaBirth: "",
          visaCountry: "",
          visaEnterDate: "",
          visaLeaveDate: "",
          visaUrgentContacter: "",
          visaEmail: "",
          visaOrganization: "",
          visaPosition: "",
          visaApplyPassportSite: "",
          visaVisitSite: "",
          visaOrgIntroduce: "",
          visaPersonalIntroduce: "",
          personType: "person_type_002",
        },
      };

      companions.value.push(newCompanion);
      // activeForms.value = ["companion-" + (companions.value.length - 1)];
    };

    // 删除同行人
    const removeCompanion = (item, index) => {
      // 如果有id，调用删除接口
      if (item.id) {
        deletePeerInfo(item.id)
          .then(() => {
            doRemove();
          })
          .catch(() => { });
      } else {
        doRemove();
      }

      function doRemove() {
        ElMessageBox.confirm("确定要删除该同行人吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            companions.value.splice(index, 1);
            if (activeForms.value[0] === "companion-" + index) {
              // activeForms.value = ["main"];
            }
            ElMessage.success("删除成功");
          })
          .catch(() => { });
      }
    };

    // 格式化日期
    const formatDate = (date) => {
      if (!date) return null;
      return dayjs(date).format("YYYY-MM-DD");
    };

    // 处理折叠面板变化
    const handleCollapseChange = (activeNames) => {
      // 处理折叠面板的展开/收起
      activeForms.value = activeNames;
      // console.log(activeForms.value)
    };
    // 字数统计函数
    const countWords = (text) => {
      if (!text) return 0;
      return text.trim().length
      // return text.trim().split(/\s+/).length;
    };

    // 处理同行人护照照片上传成功
    const handleCompanionVisaImageSuccess = (response, index) => {
      if (response.code === 1000) {
        companions.value[index].passportImageList = response.data;
        nextTick(() => {
          if (companionFormRefs.value[index]) {
            companionFormRefs.value[index].clearValidate(["passportImageList"]);
            companionFormRefs.value[index].validateField("passportImageList");
          }
        });
      } else {
        ElMessage.error("护照照片上传失败");
      }
    };

    // 计算金额
    const calculateAmount = () => {
      let amount = 0;
      // 计算报名人的参会费用
      mainForm.value.meetingType.forEach(type => {
        const selectedItem = participationList.value.find(item => item.value === type);
        if (selectedItem) {
          amount += Number(selectedItem.price);
        }
      });

      // 计算所有同行人的参会费用
      companions.value.forEach(companion => {
        companion.meetingType.forEach(type => {
          const selectedItem = participationList.value.find(item => item.value === type);
          if (selectedItem) {
            amount += Number(selectedItem.price);
          }
        });
      });

      return amount;
    };

    // 同行人全选处理
    const handleCompanionCheckAllChange = (val, index) => {
      companions.value[index].meetingType = val ? participationList.value.map(item => item.value) : [];
      companions.value[index].isIndeterminate = false;
    };

    // 同行人复选框组变化处理
    const handleCompanionCheckedChange = (value, index) => {
      // 选中全包，只能选它
      if (value.includes('6')) {
        companions.value[index].meetingType = ['6'];
        return;
      }
      // 选中第二个优惠价，只能选 4、5、7
      if (value.includes('7')) {
        companions.value[index].meetingType = value.filter(v => ['4', '5', '7'].includes(v));
        return;
      }
      // 其它情况，正常处理
      const checkedCount = value.length;
      companions.value[index].checkAll = checkedCount === participationList.value.length;
      companions.value[index].isIndeterminate = checkedCount > 0 && checkedCount < participationList.value.length;
    };

    // 提交所有信息
    const submitAll = async (type) => {
      try {
        isSubmitting.value = true;

        // 验证报名人表单
        await mainFormRef.value.validate();

        // 验证同行人表单
        if (companions.value.length > 0) {
          for (let i = 0; i < companions.value.length; i++) {
            await companionFormRefs.value[i].validate();
          }
        }
          const enterDate = new Date(this.mainForm.visaInfo.visaEnterDate);
          const leaveDate = new Date(this.mainForm.visaInfo.visaLeaveDate);
            if (leaveDate <= enterDate) {
          this.$message.error('离境日期必须晚于入境日期');
          return false;
        }
        // 计算总金额
        let totalMoney = calculateAmount();

        // 准备报名人数据
        const mainFormData = {
          ...mainForm.value,
          status: "meeting_status_001",
          orderAmtPayable: totalMoney,
          orderPaidAmt: 0,
          passportImageList: mainForm.value.passportImageList,
          passportImage: "",
          orderUnit:'1', //orderUnit  1 人民币  2 欧元  3美元
          meetingType: mainForm.value.meetingType.sort().join(","),
          visaInfo:
            mainForm.value.invitePassportFlag === "invite_passport_flag_001"
              ? {
                ...mainForm.value.visaInfo,
                visaBirth: formatDate(mainForm.value.visaInfo.visaBirth),
                visaEnterDate: formatDate(
                  mainForm.value.visaInfo.visaEnterDate
                ),
              }
              : null,
        };

        // 提交报名人信息
        let response;
        if (isEdit.value) {
          mainFormData.id = mainForm.value.id;
          response = await updateRegistration(mainFormData);
        } else {
          response = await submitRegistration(mainFormData);
        }

        if (response.code !== 1000) {
          ElMessage.error(response.message || "提交失败");
          return;
        }

        // 新增
        if (!isEdit.value) {
          registrationStore.setRegistrationId(response.data.data.id);
        }

        // 如果有同行人，提交同行人信息
        if (companions.value.length > 0) {
          const companionsData = companions.value.map((companion) => {
            if (companion.id) {
              delete companion.id;
            }
            if (companion.meetingVisaInfo) {
              delete companion.meetingVisaInfo.id;
            }
            if (companion.visaInfo) {
              delete companion.visaInfo.id;
            }
            return {
              ...companion,
              signUpInfoId: !isEdit.value
                ? response.data.data.id
                : mainFormData.id,
              meetingType: companion.meetingType.sort().join(','),
              visaInfo:
                companion.invitePassportFlag === "invite_passport_flag_001"
                  ? {
                    ...companion.visaInfo,
                    visaBirth: formatDate(companion.visaInfo.visaBirth),
                    visaEnterDate: formatDate(
                      companion.visaInfo.visaEnterDate
                    ),
                    visaLeaveDate: formatDate(
                      companion.visaInfo.visaLeaveDate
                    ),
                  }
                  : null,
            };
          });
          //   updatePeerInfo
          const companionResponse = await submitCompanion(companionsData);
          if (companionResponse.code !== 1000) {
            ElMessage.error("同行人信息提交失败");
            return;
          }
        }
        if (type === "save") {
          router.push({
            path: getLocalePath("dashboard"),
            query: { tab: "registrations" },
          });
        } else {
          router.push(getLocalePath("payment"));
        }
        ElMessage.success("提交成功");
      } catch (error) {
        console.error("提交失败", error);
        // 获取第一个错误表单项
        let firstErrorField = null;
        let errorFormType = '';
        let errorFormIndex = -1;
        
        // 检查主表单
        if (mainFormRef.value?.$el) {
          firstErrorField = mainFormRef.value.$el.querySelector('.is-error');
          if (firstErrorField) {
            errorFormType = '报名人';
          }
        }
        
        // 如果主表单没有错误，检查同行人表单
        if (!firstErrorField && companions.value.length > 0) {
          for (let i = 0; i < companionFormRefs.value.length; i++) {
            const formRef = companionFormRefs.value[i];
            if (formRef?.$el) {
              firstErrorField = formRef.$el.querySelector('.is-error');
              if (firstErrorField) {
                errorFormType = '同行人';
                errorFormIndex = i + 1;
                break;
              }
            }
          }
        }
        
        // 如果找到错误表单项，滚动到该位置
        if (firstErrorField) {
          firstErrorField.scrollIntoView({ 
            behavior: 'smooth',
            block: 'center'
          });
          
          // 展开包含错误表单项的折叠面板
          if (errorFormType === '报名人') {
            activeFormsMain.value = ['main'];
          } else if (errorFormType === '同行人') {
            activeForms.value = [`companion-${errorFormIndex - 1}`];
          }
          
          // 显示具体的错误提示
          const errorMessage = errorFormType === '报名人' 
            ? '请完善报名人信息'
            : `请完善第${errorFormIndex}位同行人信息`;
          ElMessage.error(errorMessage);
        } else {
          // 保留原有的通用错误提示
          ElMessage.error("请检查表单填写是否正确");
        }
      } finally {
        isSubmitting.value = false;
      }
    };
    const   disableOtherDates =(date) =>{
    // 获取日期的月份（注意：JavaScript的月份是从0开始的，10表示11月，所以9才是10月）
    const month = date.getMonth();
    // 获取日期的日
    const day = date.getDate();
    
    // 只允许选择10月(9)的19日至25日
    return month !== 9 || day < 19 || day > 25;
  }
    const goBack = () => {
      router.back();
    };

    // 报名人选项禁用逻辑
    const isOptionDisabled = (value) => {
      const selected = mainForm.value.meetingType;
      // 选中全包
      if (selected.includes('6')) {
        return value !== '6';
      }
      // 选中第二个优惠价
      if (selected.includes('7')) {
        // 只允许 4、5、7 选，其他禁用
        return !['4', '5', '7'].includes(value);
      }
      return false;
    };

    // 同行人选项禁用逻辑
    const isCompanionOptionDisabled = (value, index) => {
      const selected = companions.value[index].meetingType;
      // 选中全包
      if (selected.includes('6')) {
        return value !== '6';
      }
      // 选中第二个优惠价
      if (selected.includes('7')) {
        // 只允许 4、5、7 选，其他禁用
        return !['4', '5', '7'].includes(value);
      }
      return false;
    };

    return {
      mainForm,
      handleCheckAllChange,
      handleCheckedChange,
      handleCompanionCheckAllChange,
      handleCompanionCheckedChange,
      mainFormRef,
      calculateAmount,
      companions,
      companionFormRefs,
      activeForms,
      activeFormsMain,
      isSubmitting,
      addCompanion,
      removeCompanion,
      submitAll,
      goBack,
      formatDate,
      countryOptions,
      passportSiteOptions,
      visitSiteOptions,
      forumOptions,
      mainRules,
      companionRules,
      visaRules,
      handleCollapseChange,
      countWords,
      participationList,
      handleCompanionVisaImageSuccess,
      isIndeterminate,
      checkAll,
      disableOtherDates,
      isOptionDisabled,
      isCompanionOptionDisabled
    };
  },
};
</script>

<style scoped>
.conference-overview-container{
  background: url('@/assets/images/home-bg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  min-height: 100vh;
  
}
.total-amount {
  margin-top: 20px;
  text-align: right;
  padding: 15px;
  background-color: var(--bg-light);
  border-radius: var(--radius-sm);
}

.amount-label {
  font-size: 16px;
  color: var(--text-light);
}

.amount-value {
  font-size: 20px;
  font-weight: 700;
  color: var(--primary-color);
  margin-left: 10px;
}
.combined-registration-page {
  padding-bottom: 80px;
}

.page-header {
  background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('@/assets/images/changcheng.png');
  background-size: cover;
  background-position: center;
  color: var(--text-white);
  padding: 120px 0 60px;
  text-align: center;
  position: relative;
}

.page-header h1 {
  font-size: 42px;
  margin-bottom: 15px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  animation: fadeInDown 1s ease-out;
}

.page-header p {
  font-size: 18px;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  animation: fadeInUp 1s ease-out;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.registration-form-section {
  padding: 80px 0;
}

.form-container {
  max-width: 860px;
  margin: 0 auto;
  background-color: var(--bg-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: 40px;
}

.form-container h2 {
  text-align: center;
  margin-bottom: 10px;
}

.form-desc {
  text-align: center;
  color: var(--text-light);
  margin-bottom: 30px;
}

.required {
  color: var(--error-color);
}

.form-section {
  margin-bottom: 40px;
  padding-bottom: 30px;
  border-bottom: 1px solid var(--border-color);
}

.form-section:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.form-section h3 {
  font-size: 20px;
  margin-bottom: 20px;
  color: var(--primary-color);
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 90%;
  /* padding-right: 20px; */
  font-size: 16px;
  font-weight: 500;
}

.form-header :deep(.el-button) {
  flex-shrink: 0;
  margin-left: 10px;
}

.form-header > span {

  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 10px;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 40px;
}

:deep(.el-collapse-item__header) {
  font-size: 16px;
  font-weight: 500;
}

:deep(.el-collapse-item__content) {
  padding: 20px;
}

:deep(.el-form-item) {
  margin-bottom: 0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--text-color);
}

:deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--border-color) inset;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px var(--primary-color) inset;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px var(--primary-color) inset;
}

:deep(.el-radio-group) {
  display: flex;
  gap: 20px;
}

:deep(.el-radio) {
  margin-right: 0;
}

:deep(.el-textarea__inner) {
  min-height: 100px;
  resize: vertical;
}

:deep(.el-date-editor) {
  width: 100%;
}

.form-row .form-group {
  margin-bottom: 20px;
}

@media (max-width: 768px) {
  .form-container {
    padding: 30px 20px;
  }

  .form-row {
    flex-direction: column;
    gap: 0;
  }

  .form-actions {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }
}

.meeting-type-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
}

.meeting-type-checkbox {
  width: 100%;
  margin-right: 0 !important;
  padding: 16px 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.meeting-type-checkbox:hover {
  border-color: var(--primary-color);
  background-color: rgba(25, 118, 210, 0.04);
}

.meeting-type-checkbox.is-checked {
  border-color: var(--primary-color);
  background-color: rgba(25, 118, 210, 0.08);
}

.meeting-type-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.meeting-type-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.meeting-date {
  color: var(--primary-color);
  font-weight: 500;
  font-size: 16px;
  width: 90px;
}

.meeting-label {
  font-size: 16px;
  color: #333;
}

.meeting-price {
  font-size: 18px;
  font-weight: 600;
  color: var(--primary-color);
}

:deep(.el-checkbox__input) {
  margin-right: 12px;
}
:deep(.el-checkbox__label) {
  width: 100%;
}

:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

:deep(.el-checkbox__inner) {
  width: 20px;
  height: 20px;
  border-radius: 4px;
}

:deep(.el-checkbox__inner::after) {
  height: 10px;
  width: 5px;
  left: 6px;
  top: 2px;
}
.meeting-date {
    font-size: 14px;
    min-width: 120px;
  }
@media (max-width: 768px) {
  .meeting-type-checkbox {
    padding: 12px 16px;
  }
  
  .meeting-date {
    font-size: 14px;
    min-width: 130px;
  }
  
  .meeting-label {
    font-size: 14px;
  }
  
  .meeting-price {
    font-size: 16px;
  }
}
</style>

