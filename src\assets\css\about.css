.conference-overview-container{
  background: url('@/assets/images/home-bg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  min-height: 100vh;
  
}
/* 添加基础样式确保不会出现横向滚动 */
.about-page {
  width: 100%;
  overflow-x: hidden;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  width: 100%;
  box-sizing: border-box;
}

/* 修改网格布局，确保响应式布局不会溢出 */
.intro-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 50px;
  align-items: center;
  width: 100%;
}

.theme-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
  width: 100%;
}

.highlights-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
  margin-top: 50px;
  width: 100%;
}

.speakers-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  padding: 10px 0;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.forum-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  width: 100%;
}

.media-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* 确保图片不会导致溢出 */
img {
  max-width: 100%;
  height: auto;
}

/* 修改响应式布局断点 */
@media (max-width: 1200px) {
  .container {
    padding: 0 15px;
  }
  
  .theme-grid,
  .highlights-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 992px) {
  .intro-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .speakers-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .media-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .theme-grid,
  .highlights-grid,
  .speakers-grid,
  .media-grid {
    grid-template-columns: 1fr;
  }
  
  .hotel-card {
    flex-direction: column;
  }
  
  .hotel-image {
    width: 100%;
    height: 200px;
  }
}

/* 确保所有元素都使用border-box盒模型 */
* {
  box-sizing: border-box;
}

.more-speakers{
  text-align: right;
  cursor: pointer;
  max-width: 1200px;
  margin: 0 auto;
  font-size: 14px;
  color: var(--primary-color);
}
.bg{
  /* background-color: var(--bg-light); */
}
.page-header.about-header {
  background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('@/assets/images/dahui.png');
  background-size: cover;
  background-position: center;
  color: var(--text-white);
  text-align: center;
}

.page-header h1 {
  font-size: 36px;
  margin-bottom: 10px;
}

.breadcrumbs {
  font-size: 16px;
  opacity: 0.8;
}

.breadcrumbs a {
  color: var(--text-white);
  text-decoration: none;
}

.breadcrumbs a:hover {
  text-decoration: underline;
}

.about-intro {
  padding: 60px 0;
  margin-top: 0;
}

.intro-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 50px;
  align-items: center;
}

.intro-content h2 {
  font-size: 32px;
  margin-bottom: 20px;
  color: var(--primary-color);
}

.intro-content .lead {
  font-size: 20px;
  margin-bottom: 20px;
  color: var(--text-light);
}

.intro-content p {
  margin-bottom: 30px;
}

.key-info {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
}

.info-item i {
  font-size: 24px;
  color: var(--primary-color);
  margin-top: 5px;
}

.info-content h4 {
  font-size: 18px;
  margin-bottom: 5px;
}

.intro-image img {
  width: 100%;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
}

.about-theme {
  padding: 60px 0;
  margin-top: 0;
}

.theme-desc {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 50px;
  color: var(--text-light);
  font-size: 22px;
}

.theme-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
}

.theme-item {
  
  border-radius: var(--radius-md);
  padding: 30px;
  /* box-shadow: var(--shadow-md); */
  text-align: center;
  transition: var(--transition);
}

.theme-item:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-lg);
}

.theme-icon {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background-color: rgba(30, 136, 229, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
}

.theme-icon i {
  font-size: 30px;
  color: var(--primary-color);
}

.theme-item h3 {
  font-size: 20px;
  margin-bottom: 15px;
}

.theme-item p {
  color: var(--text-light);
}

.about-organization {
  padding: 60px 0;
  margin-top: 0;
}

/* 会议亮点样式 */
.about-highlights {
  padding: 60px 0;
  margin-top: 0;
}

.highlights-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
  margin-top: 50px;
}

.highlight-card {
  background-color: var(--bg-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  padding: 30px;
  text-align: center;
  transition: var(--transition);
}

.highlight-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-lg);
}

.highlight-icon {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background-color: rgba(30, 136, 229, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
}

.highlight-icon i {
  font-size: 30px;
  color: var(--primary-color);
}

.highlight-card h3 {
  font-size: 20px;
  margin-bottom: 15px;
  color: var(--primary-color);
}

.highlight-card p {
  color: var(--text-light);
  line-height: 1.6;
}

/* 组织机构样式 */
.organization-section {
  padding: 60px 0;
  margin-top: 0;
}

.section-title {
  font-size: 32px;
  text-align: center;
  margin-bottom: 40px;
  color: var(--primary-color);
  position: relative;
  padding-bottom: 15px;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background-color: var(--primary-color);
}

.org-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
}

.org-card {
  background-color: var(--bg-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  padding: 30px;
}

.org-card h3 {
  font-size: 20px;
  margin-bottom: 20px;
  color: var(--primary-color);
  text-align: center;
  position: relative;
  padding-bottom: 10px;
}

.org-card h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 2px;
  background-color: var(--primary-color);
}

.org-card ul {
  list-style: none;
  padding: 0;
}

.org-card li {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  gap: 15px;
}

.org-card li:last-child {
  margin-bottom: 0;
}

.org-card img {
  width: 80px;
  height: auto;
  border-radius: var(--radius-sm);
  transition: var(--transition);
}

.org-card span {
  flex: 1;
  font-size: 16px;
  color: var(--text-color);
}

.about-history {
  padding: 60px 0;
  margin-top: 0;
}

.history-timeline {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
}

.history-timeline::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 120px;
  width: 2px;
  background-color: var(--border-color);
}

.timeline-item {
  display: flex;
  margin-bottom: 50px;
  position: relative;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-year {
  width: 100px;
  font-size: 24px;
  font-weight: 700;
  color: var(--primary-color);
  text-align: right;
  padding-right: 30px;
  flex-shrink: 0;
}

.timeline-content {
  position: relative;
  background-color: var(--bg-color);
  border-radius: var(--radius-md);
  padding: 30px;
  box-shadow: var(--shadow-md);
  margin-left: 40px;
}

.timeline-content::before {
  content: '';
  position: absolute;
  left: -40px;
  top: 20px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: var(--primary-color);
  border: 4px solid var(--bg-color);
  box-shadow: var(--shadow-sm);
}

.timeline-content h3 {
  font-size: 20px;
  margin-bottom: 10px;
}

.timeline-content p {
  color: var(--text-light);
  margin-bottom: 5px;
}

@media (max-width: 992px) {
  .theme-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .org-grid {
    grid-template-columns: 1fr;
  }

  .highlights-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .intro-grid {
    grid-template-columns: 1fr;
  }

  .key-info {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .theme-grid {
    grid-template-columns: 1fr;
  }

  .history-timeline::before {
    left: 20px;
  }

  .timeline-year {
    width: 60px;
    font-size: 18px;
    padding-right: 20px;
  }

  .timeline-content {
    margin-left: 20px;
  }

  .timeline-content::before {
    left: -30px;
    width: 16px;
    height: 16px;
  }
}

@media (max-width: 768px) {
  .contact-grid {
    grid-template-columns: 1fr;
  }

  .highlights-grid {
    grid-template-columns: 1fr;
  }
}

.speakers-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  padding: 10px 0;
  max-width: 1200px;
  margin: 0 auto;
}

.speaker-card {
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.speaker-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.15);
}

.speaker-image {
  position: relative;
  width: 100%;
  overflow: hidden;
  height: 380px;
}

.speaker-image img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.speaker-card:hover .speaker-image img {
  transform: scale(1.1);
}

.image-container.image-placeholder {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

.speaker-info {
  padding: 18px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.speaker-info h3 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.speaker-title {
  font-size: 16px;
  color: var(--primary-color);
  margin-bottom: 12px;
  font-weight: 500;
}

.speaker-bio {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  flex-grow: 1;
}

.speaker-social {
  display: flex;
  gap: 12px;
}

.speaker-social a {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  transition: all 0.3s ease;
}

.speaker-social a:hover {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}
.footer-image-item{
  margin:0 8px;
}
@media (max-width: 1024px) {
  .speakers-grid {
    grid-template-columns: repeat(2, 1fr);
    padding: 30px 20px;
  }
}

@media (max-width: 768px) {
  .speakers-grid {
    grid-template-columns: 1fr;
    padding: 20px;
  }

  .speaker-info h3 {
    font-size: 20px;
  }

  .speaker-title {
    font-size: 14px;
  }

  .speaker-bio {
    font-size: 13px;
  }
}


.forum-page {
  min-height: 100vh;
  
}

/* .page-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, #1565c0 100%);
  color: var(--text-white);
  padding: 120px 0 60px;
  text-align: center;
} */

.page-header h1 {
  font-size: 36px;
  margin-bottom: 10px;
}

.forum-section {
  padding: 80px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.forum-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.forum-card {
  background: var(--bg-color);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: transform 0.3s ease;
}

.forum-card:hover {
  transform: translateY(-5px);
}

.forum-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.forum-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.forum-content {
  padding: 20px;
}

.forum-content h2 {
  font-size: 20px;
  margin-bottom: 15px;
  color: var(--primary-color);
}

.forum-content p {
  color: var(--text-light);
  margin-bottom: 20px;
  line-height: 1.6;
}

.forum-details {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--text-light);
}

.detail-item i {
  color: var(--primary-color);
}

@media (max-width: 768px) {
  .forum-grid {
    grid-template-columns: 1fr;
  }
  
  .page-header {
    padding: 40px 0 40px;
  }
  
  .page-header h1 {
    font-size: 28px;
  }
}

.about-hotel {
  padding: 60px 0;
  margin-top: 0;
}
.hotel-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
}
.hotel-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.08);
  overflow: hidden;
  display: flex;
  flex-direction: row;
}
.hotel-image {
  width: 240px;
  height: 100%;
  flex-shrink: 0;
  overflow: hidden;
}
.hotel-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.hotel-info {
  padding: 24px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.hotel-info h3 {
  font-size: 22px;
  margin-bottom: 10px;
  color: var(--primary-color);
}
.hotel-address {
  font-size: 15px;
  color: #888;
  margin-bottom: 10px;
}
.hotel-desc {
  font-size: 15px;
  color: #555;
  margin-bottom: 12px;
}
.hotel-link {
  color: var(--primary-color);
  text-decoration: underline;
  font-size: 15px;
  margin-top: 8px;
}
@media (max-width: 900px) {
  .hotel-grid {
    grid-template-columns: 1fr;
  }
  .hotel-card {
    flex-direction: column;
  }
  .hotel-image {
    width: 100%;
    height: 180px;
  }
}

.media-support {
  padding: 60px 0;
  margin-top: 0;
}

.media-desc {
  text-align: center;
  color: #666;
  margin-bottom: 40px;
  font-size: 16px;
}

.media-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.media-card {
  background: #fff;
  border-radius: 12px;
  padding: 30px;
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid #eee;
}

.media-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

.media-icon {
  width: 60px;
  height: 60px;
  margin: 0 auto 20px;
  background: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.media-icon i {
  font-size: 24px;
  color: #fff;
}

.media-info h3 {
  font-size: 20px;
  color: #333;
  margin-bottom: 10px;
}

.media-info p {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
}

.media-tags {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.media-tags span {
  padding: 4px 12px;
  background: rgba(30, 136, 229, 0.1);
  color: var(--primary-color);
  border-radius: 20px;
  font-size: 12px;
}

@media (max-width: 1024px) {
  .media-grid {
    grid-template-columns: repeat(2, 1fr);
    padding: 0 20px;
  }
}

@media (max-width: 640px) {
  .media-grid {
    grid-template-columns: 1fr;
  }
}

/* 修改动画相关样式 */
.animate__animated {
  animation-duration: 1s;
  animation-fill-mode: both;
}

.animate__animated.animate__fadeInDown {
  animation-name: fadeInDown;
}

.animate__animated.animate__fadeInUp {
  animation-name: fadeInUp;
}

.animate__animated.animate__fadeInLeft {
  animation-name: fadeInLeft;
}

.animate__animated.animate__fadeInRight {
  animation-name: fadeInRight;
}

.animate__animated.animate__fadeIn {
  animation-name: fadeIn;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translate3d(0, -100%, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 100%, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translate3d(-100%, 0, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translate3d(100%, 0, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 确保元素在动画前是隐藏的 */
.section-title:not(.animate__animated),
.intro-content:not(.animate__animated),
.intro-image:not(.animate__animated),
.theme-item:not(.animate__animated),
.highlight-card:not(.animate__animated),
.speaker-card:not(.animate__animated),
.forum-card:not(.animate__animated),
.media-card:not(.animate__animated) {
  opacity: 0;
}

/* 确保元素在动画后保持可见 */
.animate__animated {
  opacity: 1 !important;
}

/* 为锚点目标添加负margin */
[id] {
  scroll-margin-top: 80px;
}
.reservation-btn {
  padding: 10px 20px;
  font-size: 15px;
  border-radius: 6px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.reservation-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.reservation-btn i {
  margin-right: 8px;
}

/* 弹窗样式 */
.reservation-dialog {
  border-radius: 12px;
  overflow: hidden;
}

.reservation-dialog .el-dialog__header {
  background-color: #f5f7fa;
  padding: 18px 20px;
  border-bottom: 1px solid #eee;
}

.reservation-dialog .el-dialog__title {
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

/* 内容区域样式 */
.dialog-content {
  padding: 25px 20px;
}

/* 酒店头部信息 */
.hotel-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px dashed #e0e0e0;
}

.hotel-logo {
  width: 50px;
  height: 50px;
  background-color: #37c2ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  color: white;
  font-size: 24px;
  box-shadow: 0 3px 10px rgba(55, 194, 255, 0.3);
}

.hotel-name {
  margin: 0 0 5px 0;
  color: #2c3e50;
  font-size: 17px;
  font-weight: 600;
}

.conference-info {
  margin: 0;
  color: #7f8c8d;
  font-size: 13px;
  font-style: italic;
}

/* 预订信息内容 */
.reservation-message {
  color: #34495e;
  line-height: 1.8;
}

.greeting {
  font-weight: 500;
  margin-bottom: 12px;
}

.intro {
  margin-bottom: 18px;
  text-align: justify;
}

/* 需求列表样式 */
.requirements {
  list-style: none;
  padding-left: 0;
  margin: 20px 0;
}

.requirement-item {
  margin-bottom: 15px;
  padding-left: 35px;
  position: relative;
  transition: all 0.2s ease;
}

.requirement-item:hover {
  transform: translateX(5px);
}

.item-number {
  position: absolute;
  left: 0;
  top: 0;
  width: 26px;
  height: 26px;
  background-color: #37c2ff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
}

.item-text {
  color: #4a5568;
}

/* 装饰线 */
.decorative-line {
  height: 1px;
  background: linear-gradient(90deg, transparent, #37c2ff, transparent);
  margin: 20px 0 10px;
}

/* 底部按钮样式 */
.dialog-footer {
  padding: 15px 20px;
  background-color: #f9fafc;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.cancel-btn {
  border-radius: 6px;
  transition: all 0.2s ease;
}

.submit-btn {
  border-radius: 6px;
  padding: 8px 20px;
  transition: all 0.2s ease;
  background-color: #37c2ff;
  border-color: #37c2ff;
}

.submit-btn:hover {
  background-color: #29b6f6;
  border-color: #29b6f6;
  transform: translateY(-2px);
}