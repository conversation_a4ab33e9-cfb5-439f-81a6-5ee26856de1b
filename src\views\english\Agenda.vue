<template>
  <div class="agenda-page">
    <!-- 页面头部 -->
    <section class="page-header">
      <div class="container">
        <!-- <h1 class="animate__animated animate__fadeInDown">Conference Agenda</h1>
        <p class="animate__animated animate__fadeInUp">October 21-24, 2025 · Pudong Shangri-La, Shanghai</p> -->
      </div>
    </section>
    <div class="conference-overview-container">
    <!-- 日程预览 -->
    <div class="conference-tabs" v-intersection-observer="handleConferenceTabsIntersection">
      <div v-for="conference in ['CMC', 'ICMCI']" :key="conference" class="conference-tab" :class="{
        'active': activeConference === conference,
        'animate__animated animate__fadeIn': conferenceTabsVisible
      }" :style="{ animationDelay: `${conference === 'CMC' ? 0 : 0.15}s` }"
        @click="handleConferenceClick(conference)">
        {{ conference === 'CMC' ? 'International CMC Conference' : 'Annual meeting of ICMCI' }}
      </div>
    </div>

    <!-- 日程标签页 -->
    <div class="agenda-tabs" v-intersection-observer="handleTabsIntersection">
      <div v-for="(day, index) in dayTabs" :key="day.id" class="agenda-tab" :class="{
        'active': activeDay === day.id,
        'animate__animated animate__fadeIn': tabsVisible
      }" :style="{ animationDelay: `${index * 0.15}s` }" @click="handleTabClick(day.id)">
        {{ day.label }}
      </div>
    </div>

    <!-- 日程内容 -->
    <div class="agenda-content" v-intersection-observer="handleContentIntersection">
      <!-- 第一天日程 -->
      <div class="agenda-day" :class="{ 'active': activeDay === 1 && activeConference === 'CMC' }">
        <div v-for="(item, index) in day1Items" :key="index" class="agenda-item"
          :class="{ 'animate__animated animate__fadeInUp': contentVisible && activeDay === 1 }"
          :style="{ animationDelay: `${index * 0.15}s` }">
          <div class="agenda-time">{{ item.time }}</div>
          <div class="agenda-details">
            <h3 class="agenda-title">{{ item.title }}</h3>
            <!-- <p class="agenda-description">{{ item.description }}</p>   -->
            <div v-if="item.location" class="session-info">
              <div class="session-location">
                <i class="fas fa-map-marker-alt"></i> {{ item.location }}
              </div>
            </div>
            <div v-if="item.speakers" class="speakers-grid">
              <div v-for="(speaker, speakerIndex) in item.speakers" :key="speaker.name" class="speaker-card"
                :class="{ 'animate__animated animate__fadeInRight': contentVisible && activeDay === 1 }"
                :style="{ animationDelay: `${index * 0.15 + speakerIndex * 0.1}s` }">
                <div class="speaker-image image-container">
                  <img :src="speaker.avatar" :alt="speaker.name" class="speaker-image" loading="lazy">
                </div>
                <div class="speaker-info">
                  <h4 class="speaker-name">{{ speaker.name }}</h4>
                  <div class="speaker-title">{{ speaker.title }}</div>
                  <div class="speaker-company">{{ speaker.company }}</div>
                  <p class="speaker-bio">{{ speaker.bio }}</p>
                  <div class="speaker-social">
                    <a v-if="speaker.linkedin" :href="speaker.linkedin"><i class="fab fa-linkedin"></i></a>
                    <a v-if="speaker.twitter" :href="speaker.twitter"><i class="fab fa-twitter"></i></a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 第二天日程 -->
      <div class="agenda-day" :class="{ 'active': activeDay === 2 && activeConference === 'CMC' }">
        <div v-for="(item, index) in day2Items" :key="index" class="agenda-item"
          :class="{ 'animate__animated animate__fadeInUp': contentVisible && activeDay === 2 }"
          :style="{ animationDelay: `${index * 0.15}s` }">
          <div class="agenda-time">{{ item.time }}</div>
          <div class="agenda-details">
            <h3 class="agenda-title">{{ item.title }}</h3>
            <!-- <p class="agenda-description">{{ item.description }}</p>   -->
            <div v-if="item.host" class="session-info">
              <div class="session-host">
                <i class="fas fa-user"></i> Host: {{ item.host }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 第三天日程 -->
      <div class="agenda-day" :class="{ 'active': activeDay === 3 && activeConference === 'CMC' }">
        <div v-for="(item, index) in day3Items" :key="index" class="agenda-item"
          :class="{ 'animate__animated animate__fadeInUp': contentVisible && activeDay === 3 }"
          :style="{ animationDelay: `${index * 0.15}s` }">
          <div class="agenda-time">{{ item.time }}</div>
          <div class="agenda-details">
            <h3 class="agenda-title">{{ item.title }}</h3>
            <!-- <p class="agenda-description">{{ item.description }}</p>   -->
          </div>
        </div>
      </div>
      <!-- 第三天日程的下午 -->
      <div class="agenda-day" :class="{ 'active': activeDay === 3 && activeConference === 'ICMCI' }">

        <div v-for="(item, index) in day3ItemsAfter" :key="index" class="agenda-item"
          :class="{ 'animate__animated animate__fadeInUp': contentVisible && activeDay === 3 }"
          :style="{ animationDelay: `${index * 0.15}s` }">
          <div class="agenda-time">{{ item.time }}</div>
          <div class="agenda-details">
            <h3 class="agenda-title">{{ item.title }}</h3>
            <!-- <p class="agenda-description">{{ item.description }}</p>   -->
          </div>
        </div>
      </div>
      <!-- 第四天日程 -->
      <div class="agenda-day" :class="{ 'active': activeDay === 4 && activeConference != 'CMC' }">
        <div v-for="(item, index) in day4Items" :key="index" class="agenda-item"
          :class="{ 'animate__animated animate__fadeInUp': contentVisible && activeDay === 4 }"
          :style="{ animationDelay: `${index * 0.15}s` }">
          <div class="agenda-time">{{ item.time }}</div>
          <div class="agenda-details">
            <h3 class="agenda-title">{{ item.title }}</h3>
            <!-- <p class="agenda-description">{{ item.description }}</p>   -->
          </div>
        </div>
      </div>
    </div>

    <!-- 快速报名 -->
    <section class="registration-cta" v-intersection-observer="handleCtaIntersection">
      <div class="container">
        <div class="cta-card" :class="{ 'animate__animated animate__fadeInUp': ctaVisible }">
          <div class="cta-content">
            <h2>Register Now</h2>
            <p>Seize the opportunity to connect with global management consulting leaders and explore cutting-edge
              industry trends and innovative practices.</p>
            <ul class="benefits-list">
              <li v-for="(benefit, index) in benefits" :key="index"
                :class="{ 'animate__animated animate__fadeInLeft': ctaVisible }"
                :style="{ animationDelay: `${0.3 + index * 0.1}s` }">
                <i class="fas fa-check"></i> {{ benefit }}
              </li>
            </ul>
          </div>
          <div class="cta-actions">
            <button class="btn btn-primary animate__animated animate__pulse animate__infinite"
              @click="$router.push(getLocalePath('registration'))">
              Register Now
            </button>
          </div>
        </div>
      </div>
    </section>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useLanguage } from '@/hooks/useLanguage'
// import { getAgenda } from '@/api/agenda.js'
import 'animate.css'

export default {
  name: 'AgendaPage',
  directives: {
    intersectionObserver: {
      mounted(el, binding) {
        let hasAnimated = false

        const observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting && !hasAnimated) {
              // 添加自定义属性以触发 CSS 过渡
              el.setAttribute('v-intersection-observer-visible', 'true')
              binding.value(true)
              hasAnimated = true
            }
          })
        }, {
          threshold: 0.1,
          rootMargin: '0px'
        })

        observer.observe(el)
      }
    }
  },
  setup() {
    const agenda = ref({ days: [] })
    const activeDay = ref(1)
    const { getLocalePath } = useLanguage()

    // 修改动画状态控制
    const tabsVisible = ref(false)
    const contentVisible = ref(false)
    const ctaVisible = ref(false)
    const conferenceTabsVisible = ref(false)

    const handleConferenceTabsIntersection = (isVisible) => {
      if (isVisible) {
        conferenceTabsVisible.value = true
      }
    }

    const handleTabsIntersection = (isVisible) => {
      if (isVisible) {
        tabsVisible.value = true
      }
    }

    const handleContentIntersection = (isVisible) => {
      if (isVisible) {
        contentVisible.value = true
      }
    }

    const handleCtaIntersection = (isVisible) => {
      if (isVisible) {
        ctaVisible.value = true
      }
    }

    // 添加报名福利列表
    const benefits = [
      'Face-to-face networking with industry leaders',
      'Participate in professional forums and workshops',
      'Gain latest industry insights',
      'Build global professional networks'
    ]

    const addToCalendar = () => {
      const activeAgendaDay = agenda.value.days.find(day => day.id === activeDay.value)

      if (!activeAgendaDay) return

      let icsContent = 'BEGIN:VCALENDAR\n'
      icsContent += 'VERSION:2.0\n'
      icsContent += 'PRODID:-//ICMCI2025//CN\n'

      activeAgendaDay.sessions.forEach(session => {
        const sessionDate = activeAgendaDay.date
        const startTime = session.startTime
        const endTime = session.endTime

        const startDateTime = new Date(`${sessionDate}T${startTime}:00`)
        const endDateTime = new Date(`${sessionDate}T${endTime}:00`)

        icsContent += 'BEGIN:VEVENT\n'
        icsContent += `DTSTART:${formatDate(startDateTime)}\n`
        icsContent += `DTEND:${formatDate(endDateTime)}\n`
        icsContent += `SUMMARY:${session.title}\n`
        icsContent += `DESCRIPTION:${session.description}\n`
        icsContent += `LOCATION:${session.location}\n`
        icsContent += 'END:VEVENT\n'
      })

      icsContent += 'END:VCALENDAR'

      const blob = new Blob([icsContent], { type: 'text/calendar;charset=utf-8' })
      const link = document.createElement('a')
      link.href = window.URL.createObjectURL(blob)
      link.download = 'ICMCI2025-Conference-Agenda.ics'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }

    const shareToWeibo = () => {
      const title = '2025 ICMCI Shanghai International Management Consulting Conference Agenda'
      const url = window.location.href
      window.open(`http://service.weibo.com/share/share.php?url=${encodeURIComponent(url)}&title=${encodeURIComponent(title)}`)
    }

    const shareByEmail = () => {
      const subject = '2025 ICMCI Shanghai International Management Consulting Conference Agenda'
      const body = 'View detailed conference agenda: ' + window.location.href
      window.location.href = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`
    }

    // 日期格式化函数
    const formatDate = (date) => {
      return date.getFullYear() +
        ('0' + (date.getMonth() + 1)).slice(-2) +
        ('0' + date.getDate()).slice(-2) +
        'T' +
        ('0' + date.getHours()).slice(-2) +
        ('0' + date.getMinutes()).slice(-2) +
        '00'
    }

    const handleTabClick = (day) => {
      activeDay.value = day
    }

    // Day 1 (Tuesday, October 21) - Check in Day
    const day1Items = [
      {
        time: 'All day',
        title: 'Check in all day',
        description: 'Registration and conference material collection'
      },
      {
        time: '18:30-20:00',
        title: 'Reception dinner',
        description: 'Welcome reception and networking event'
      }
    ]

    // Day 2 (Wednesday, October 22) - CMC Conference Day 1
    const day2Items = [
      // {
      //   time: '09:00-09:30',
      //   title: 'Opening ceremony',
      //   description: 'International Conference of Certified Management Consultants opening ceremony',
      //   host: 'Li Bing, Vice President of China Enterprise Confederation'
      // },
      {
        time: '09:30-12:00',
        title: 'Keynote presentations',
        description: 'Industry leaders deliver keynote speeches on management consulting trends'
      },
      {
        time: '12:00-13:00',
        title: 'Lunch',
        description: 'Buffet lunch and networking'
      },
      {
        time: '14:00-15:30',
        title: 'Sub-forums on Industrial Applications of Artificial Intelligence, etc.',
        description: 'Parallel sessions discussing AI applications across industries and development prospects'
      },
      {
        time: '15:30-15:50',
        title: 'Tea break',
        description: 'Refreshment break and networking time'
      },
      {
        time: '15:50-17:30',
        title: 'Sub-forums on Management Consulting Practices, etc.',
        description: 'Sharing best practices and experiences in management consulting'
      },
      {
        time: '18:00-20:30',
        title: 'Ceremony of the Constantinus International Award 2025',
        description: 'Constantinus International Management Consulting Award ceremony'
      }
    ]
    const day3Items = [
      {
        time: '09:00-10:00',
        title: 'International Management Consulting Chairman Forum',
        description: 'Round table discussion among chairmen of management consulting institutes worldwide'
      },
      {
        time: '10:00-10:20',
        title: 'TEA BREAK',
        description: 'Announcement and recognition of outstanding management consulting organizations and practices'
      },
      {
        time: '10:20-11:40',
        title: 'keynote presentation',
        description: 'Refreshment break and networking time'
      },
      // {
      //   time: '10:40-11:20',
      //   title: 'Keynote presentations',
      //   description: 'Closing keynote speeches by distinguished guests'
      // },
      {
        time: '11:40-12:00',
        title: 'Closing ceremony',
        description: 'International Conference of Certified Management Consultants closing ceremony'
      },
      {
        time: '12:00-13:00',
        title: 'Lunch',
        description: 'Buffet lunch and networking'
      },

    ]
    const day3ItemsAfter = [

      {
        time: '14:00-14:10',
        title: 'Opening ceremony',
        description: 'Annual meeting of International Council of Management Consulting Institutes (ICMCI) opening'
      },
      {
        time: '14:10-15:30',
        title: 'The committees of ICMCI work promotion meeting',
        description: 'Committee work progress reports and discussions'
      },
      {
        time: '15:30-16:00',
        title: 'Tea break',
        description: 'Refreshment break and networking time'
      },
      {
        time: '16:00-17:30',
        title: 'Round table discussion by groups (four groups by region)',
        description: 'Regional group discussions in four groups'
      }
    ]

    // Day 4 (Friday, October 24) - ICMCI Annual Meeting Continues
    const day4Items = [
      {
        time: '09:00-10:00',
        title: 'Each group reports the discussion',
        description: 'Regional groups report round table discussion results'
      },
      {
        time: '10:00-10:30',
        title: 'Tea break',
        description: 'Refreshment break and networking time'
      },
      {
        time: '10:30-11:30',
        title: 'Representatives of IMCs introduce latest developments',
        description: 'Representatives from Institute of Management Consultants worldwide present latest developments'
      },
      {
        time: '11:30-11:45',
        title: 'Summary of the meeting',
        description: 'Conference summary and closing remarks'
      },
      {
        time: '12:00-13:00',
        title: 'Lunch',
        description: 'Buffet lunch and networking'
      },
      {
        time: '14:00-15:30',
        title: 'Introduce the annual report, financial report, audit report and other reports',
        description: 'Presentation of ICMCI annual reports, financial statements and audit results'
      },
      {
        time: '15:30-16:00',
        title: 'Tea break',
        description: 'Refreshment break and networking time'
      },
      {
        time: '16:00-17:00',
        title: 'Voting and reporting',
        description: 'Voting on important matters and results reporting'
      },
      {
        time: '17:00-17:30',
        title: 'Closing ceremony',
        description: 'ICMCI Annual Meeting closing ceremony'
      },
      {
        time: '18:00-19:00',
        title: 'Dinner',
        description: 'Closing dinner'
      }
    ]
    const dayTabs = ref([
      { id: 1, label: 'Tuesday, 21st October' },
      { id: 2, label: 'Wednesday, 22nd October' },
      { id: 3, label: 'Thursday, 23rd October' },
      { id: 4, label: 'Friday, 24th October' }
    ])
    const activeConference = ref('CMC')
    const handleConferenceClick = (conference) => {

      activeConference.value = conference
      // 切换会议类型时，默认选择第一个可用日期
      if (conference === 'CMC') {
        dayTabs.value = [
          { id: 1, label: 'Tuesday, 21st October' },
          { id: 2, label: 'Wednesday, 22nd October' },
          { id: 3, label: 'Thursday, 23rd October' }
        ]
        activeDay.value = 1;
      } else {
        activeDay.value = 3
        dayTabs.value = [
          { id: 3, label: 'Thursday, 23rd October' },
          { id: 4, label: 'Friday, 24th October' },
        ]
      }
    }
    onMounted(() => {
      // loadAgenda()
      handleConferenceClick('CMC')
    })

    return {
      dayTabs,
      activeConference,
      agenda,
      activeDay,
      addToCalendar,
      shareToWeibo,
      shareByEmail,
      handleTabClick,
      day1Items,
      day2Items,
      day3Items,
      day3ItemsAfter,
      day4Items,
      getLocalePath,
      tabsVisible,
      contentVisible,
      ctaVisible,
      conferenceTabsVisible,
      handleConferenceTabsIntersection,
      handleTabsIntersection,
      handleContentIntersection,
      handleCtaIntersection,
      benefits,
      handleConferenceClick
    }
  }
}
</script>

<style scoped>
@import url('@/assets/css/agenda.css');
</style>
