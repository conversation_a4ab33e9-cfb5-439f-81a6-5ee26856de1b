<template>
  <div class="forum-item" @click="navigateToForum">
    <div class="forum-number">{{ forum.number }}</div>
    <div class="forum-content">
      <h4>{{ forum.title }}</h4>
      <p>{{ forum.subtitle }}</p>
      <div class="forum-meta">
        <div class="meta-item">
          <i class="fas fa-calendar"></i>
          <span>{{ forum.date }}</span>
        </div>
        <div class="meta-item">
          <i class="fas fa-map-marker-alt"></i>
          <span>{{ forum.location }}</span>
        </div>
      </div>
      <router-link :to="'/forum/' + forum.id" class="forum-link">了解详情</router-link>
    </div>
  </div>
</template>

<script>
import { useRouter } from 'vue-router'

export default {
  name: 'ForumCard',
  props: {
    forum: {
      type: Object,
      required: true
    }
  },
  setup(props) {
    const router = useRouter()
    
    const navigateToForum = () => {
      router.push('/forum/' + props.forum.id)
    }
    
    return {
      navigateToForum
    }
  }
}
</script>

<style scoped>
.forum-item {
  background-color: var(--bg-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  padding: 2rem;
  position: relative;
  overflow: hidden;
  transition: var(--transition);
  cursor: pointer;
}

.forum-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.forum-number {
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 3rem;
  font-weight: 700;
  color: rgba(0, 0, 0, 0.05);
  line-height: 1;
}

.forum-content h4 {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
  color: var(--primary-color);
}

.forum-content p {
  color: var(--text-light);
  margin-bottom: 1rem;
}

.forum-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: var(--text-light);
}

.forum-link {
  display: inline-block;
  color: var(--primary-color);
  font-weight: 500;
  position: relative;
}

.forum-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--primary-color);
  transition: width 0.3s ease;
}

.forum-link:hover::after {
  width: 100%;
}

@media (max-width: 768px) {
  .forum-item {
    padding: 1.5rem;
  }
  
  .forum-item h4 {
    font-size: 1.1rem;
  }
}
</style>
