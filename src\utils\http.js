import axios from 'axios'
import { useUserStore } from '@/stores/user'
import router from '@/router' 
import { ElMessage } from "element-plus";

// 添加标记，用于控制是否已经显示过消息
let isShowingMessage = false;

// 创建axios实例
const http = axios.create({
  baseURL: '/oa/cec-api', // 使用Vite配置的代理
  timeout: 20000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json'
  }
})

// 获取当前语言路径
const getCurrentLangPath = () => {
  const currentPath = router.currentRoute.value.fullPath
  return currentPath.startsWith('/english') ? '/english' : '/chinese'
}

// 请求拦截器
http.interceptors.request.use(
  config => {
    // 从Pinia store获取token
    const userStore = useUserStore()
    const token = userStore.token;
    if (token) {
      config.headers.token = token
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
http.interceptors.response.use(
  response => {
    // 密码3008过期，3009token不能为空，3010token无效
    if(response.data.code === 3008 || response.data.code === 3009 || response.data.code === 3010){
      // 只在没有显示过消息时显示
      if (!isShowingMessage) {
        isShowingMessage = true;
        ElMessage.warning(response.data.msg || '登录已过期，请重新登录')
      }

      // 清除 Pinia store 中的用户信息
      const userStore = useUserStore()
      userStore.clearToken()
      
      // 获取当前语言路径
      const langPath = getCurrentLangPath()
      // debugger
      // 确保路由实例已经准备好，并且当前不在登录页面
  
        // 使用 replace 而不是 push，防止用户返回到需要认证的页面
        router.replace({
          path: `${langPath}/login`,
          query: { redirect: router.currentRoute.value.fullPath }
        })
      
      
      // 返回一个被拒绝的 Promise，这样调用方可以知道请求失败了
      return Promise.reject(new Error(response.data.msg))
    }
    return response.data
  },
  error => {
    // 处理401未授权错误
    if (error.response && error.response.status === 401) {
      // 只在没有显示过消息时显示
      if (!isShowingMessage) {
        isShowingMessage = true;
        ElMessage.warning('登录已过期，请重新登录')
      }

      // 清除Pinia中的token
      const userStore = useUserStore()
      userStore.clearToken()
      
      // 获取当前语言路径
      const langPath = getCurrentLangPath()
      
      // 确保路由实例已经准备好，并且当前不在登录页面
      if (!router.currentRoute.value.path.includes('/login')) {
        router.replace({
          path: `${langPath}/login`,
          query: { redirect: router.currentRoute.value.fullPath }
        })
      }
    }
    
    return Promise.reject(error)
  }
)

// 封装GET请求
export const get = (url, params = {}) => {
  return http.get(url, { params })
}

// 封装POST请求
export const post = (url, data = {}) => {
  return http.post(url, data)
}

// 封装PUT请求
export const put = (url, data = {}) => {
  return http.put(url, data)
}

// 封装DELETE请求
export const del = (url, params = {}) => {
  return http.delete(url, { params })
}

export default http