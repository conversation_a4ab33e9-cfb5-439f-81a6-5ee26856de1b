/* 响应式导航菜单 */
.mobile-menu {
  position: fixed;
  top: var(--header-height);
  left: 0;
  width: 100%;
  height: calc(100vh - var(--header-height));
  background-color: var(--bg-color);
  z-index: 999;
  padding: 20px;
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  overflow-y: auto;
}

.mobile-menu.active {
  transform: translateX(0);
}

.mobile-nav ul {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.mobile-nav a {
  display: block;
  padding: 12px 15px;
  border-radius: var(--radius-sm);
  color: var(--text-color);
  font-weight: 500;
  transition: var(--transition);
}

.mobile-nav a:hover,
.mobile-nav a.active {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.mobile-user-actions {
  margin-top: 30px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: space-between;
  width: 30px;
  height: 21px;
  cursor: pointer;
}

.mobile-menu-toggle span {
  display: block;
  height: 3px;
  width: 100%;
  background-color: var(--text-color);
  border-radius: 3px;
  transition: var(--transition);
}

/* 响应式表格 */
.responsive-table {
  width: 100%;
  overflow-x: auto;
}

/* 响应式媒体查询 */
@media (max-width: 992px) {
  .main-nav {
    display: none;
  }
  
  .mobile-menu-toggle {
    display: flex;
  }
  
  .dashboard-container {
    grid-template-columns: 1fr;
  }
  
  .dashboard-sidebar {
    margin-bottom: 30px;
  }
}

@media (max-width: 768px) {
  .main-title {
    font-size: 32px;
  }
  
  .subtitle {
    font-size: 18px;
  }
  
  .cta-buttons {
    flex-direction: column;
    gap: 15px;
  }
  
  .countdown-container {
    gap: 20px;
  }
  
  .count {
    font-size: 28px;
  }
  
  .footer-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 576px) {
  .main-title {
    font-size: 28px;
  }
  
  .subtitle {
    font-size: 16px;
  }
  
  .count {
    font-size: 24px;
  }
  
  .label {
    font-size: 12px;
  }
  
  .form-actions {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }
  
  .form-actions .btn {
    width: 100%;
  }
}
