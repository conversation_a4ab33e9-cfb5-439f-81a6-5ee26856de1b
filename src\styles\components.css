/* 组件样式 */

/* 导航栏样式 */
.main-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: var(--header-height);
  background-color: var(--bg-color);
  box-shadow: var(--shadow-sm);
  z-index: 1000;
  transition: var(--transition);
}

.main-header .container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
}

.logo {
  height: 50px;
}

.logo img {
  height: 100%;
}

.main-nav ul {
  display: flex;
  gap: 30px;
  margin: 0;
  padding: 0;
  list-style: none;
}

.main-nav a {
  color: var(--text-color);
  text-decoration: none;
  font-size: 16px;
  font-weight: 500;
  padding: 8px 0;
  position: relative;
  transition: all 0.3s ease;
}

.main-nav a::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--primary-color);
  transition: width 0.3s ease;
}

.main-nav a:hover::after,
.main-nav a.active::after {
  width: 100%;
}

.main-nav a:hover,
.main-nav a.active {
  color: var(--primary-color);
}

.user-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

/* 页脚样式 */
.main-footer {
  background-color: var(--bg-dark);
  color: var(--text-white);
  padding: 80px 0 30px;
}

.footer-grid {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 15px;
  margin-bottom: 50px;
}

.footer-about {
  max-width: 400px;
}

.footer-logo {
  height: 120px;
  margin-bottom: 20px;
}

.footer-about p {
  margin-bottom: 20px;
  opacity: 0.8;
}

.footer-links h3,
.footer-contact h3 {
  margin-bottom: 20px;
  font-size: 18px;
  position: relative;
  padding-bottom: 10px;
}

.footer-about h3::after,
.footer-links h3::after,
.footer-contact h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 2px;
  background-color: var(--primary-color);
}

.footer-about p {
  margin-bottom: 20px;
  opacity: 0.8;
}

.social-links {
  display: flex;
  gap: 15px;
}

.social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
  transition: var(--transition);
}

.social-links a:hover {
  background-color: var(--primary-color);
  transform: translateY(-3px);
}

.footer-links ul {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.social-links {
  display: flex;
  gap: 15px;
}

.social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
  transition: var(--transition);
}

.social-icon:hover {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.footer-links a {
  color: var(--text-white);
  opacity: 0.8;
  transition: var(--transition);
}

.footer-links a:hover {
  opacity: 1;
  color: var(--primary-color);
  padding-left: 5px;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 10px;
  opacity: 0.8;
}

.footer-bottom {
  text-align: center;
  padding-top: 30px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  opacity: 0.6;
  font-size: 14px;
}

/* 英雄区域样式 */
.hero-section {
  position: relative;
  height: 100vh;
  min-height: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-white);
  overflow: hidden;
  margin-top: var(--header-height);
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* z-index: -1; */
}

.hero-background::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* background: linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.7)); */
}

.hero-background img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-content {
  text-align: center;
  max-width: 800px;
  padding: 0 20px;
  z-index: 1;
  margin-top:100px;
}

.main-title {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 10px;
  animation: fadeInDown 0.8s ease forwards;
}

.subtitle {
  font-size: 24px;
  margin-bottom: 30px;
  animation: fadeInUp 0.8s ease forwards;
  animation-delay: 0.2s;
  opacity: 0;
}

.event-details {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-bottom: 30px;
  animation: fadeInUp 0.8s ease forwards;
  animation-delay: 0.4s;
  opacity: 0;
}

.event-details div {
  display: flex;
  align-items: center;
  gap: 10px;
}

.countdown-container {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-top: 40px;
  animation: fadeInUp 0.8s ease forwards;
  animation-delay: 0.8s;
  opacity: 0;
}

.countdown-item {
  background-color: rgba(255, 255, 255, 0.8);
  /* backdrop-filter: blur(10px); */
  padding: 15px 20px;
  border-radius: var(--radius-md);
  min-width: 100px;
  text-align: center;
}

.count {
  font-size: 36px;
  font-weight: 700;
  display: block;
  color: var(--primary-color);
}

.label {
  font-size: 14px;
  opacity: 0.9;
  color: var(--primary-color);
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  animation: fadeInUp 0.8s ease forwards;
  animation-delay: 0.6s;
  opacity: 0;
}

/* 卡片样式 */
.card {
  background-color: var(--bg-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: var(--transition);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.card-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.card:hover .card-image img {
  transform: scale(1.1);
}

.card-content {
  padding: 20px;
}

.card-title {
  font-size: 20px;
  margin-bottom: 10px;
}

.card-text {
  color: var(--text-light);
  margin-bottom: 15px;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: var(--bg-light);
}

/* 图片网格布局 */
.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  margin: 30px 0;
}

/* 图片卡片样式 */
.image-card {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.image-card:hover {
  transform: translateY(-10px);
}

.image-container {
  position: relative;
  overflow: hidden;
}



.image-container img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.image-card:hover .image-container img {
  transform: scale(1.1);
}

