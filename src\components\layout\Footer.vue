<template>
  <footer class="main-footer">
    <div class="container">
      <div class="footer-grid">
        <div class="footer-about">
          <div style="display: flex;">


            <a href="https://www.cmc-global.org/" target="_blank"><img src="../../assets/images/logo.png"
                alt="ICMCI 2025" class="footer-logo"/></a>
          </div>
          <div style="display: flex;margin-left: 40px;" >


            <a href="https://www.cec1979.org.cn/englishVersion/" target="_blank"> <img
                src="../../assets/images/logo-big.png" class="footer-logo"/></a>

          </div>

        </div>

        <div class="footer-links">
          <h3>{{ currentLanguage === 'chinese' ? '快速链接' : 'Quick Links' }}</h3>
          <div class="footer-links-list">
            <ul>
              <li><router-link :to="getLocalePath('home')">{{ currentLanguage === 'chinese' ? '首页' :
                  'Home'}}</router-link></li>
              <li><router-link :to="getLocalePath('about')">{{ currentLanguage === 'chinese' ? '关于会议' :
                  'About'}}</router-link></li>
              <li><router-link :to="getLocalePath('agenda')">{{ currentLanguage === 'chinese' ? '会议日程' :
                  'Agenda'}}</router-link></li>
              <li><router-link :to="getLocalePath('participants')">{{ currentLanguage === 'chinese' ? '重要嘉宾' :
                  'VIPs'}}</router-link></li>

            </ul>
            <ul>
              <li><router-link :to="getLocalePath('registration')">{{ currentLanguage === 'chinese' ? '报名参会' :
                  'Register'}}</router-link></li>
              <li><router-link :to="getLocalePath('Travel')">{{ currentLanguage === 'chinese' ? '旅游' : 
              'Partner  Program'}}</router-link></li>
              <!-- <li><router-link :to="getLocalePath('Visits')">{{currentLanguage === 'chinese' ? '参观考察' : 'Visit'}}</router-link></li> -->
              <li><router-link :to="getLocalePath('AskQuestion')">{{ currentLanguage === 'chinese' ? '常见问题' :
                  'FAQ'}}</router-link></li>
            </ul>
          </div>
        </div>

        <div class="footer-contact" style="width: 130%;">
          <h3>{{ currentLanguage === 'chinese' ? '联系我们' : 'Contact' }}</h3>
          <div class="contact-info">
            <div class="contact-item">
              <i class="fas fa-envelope"></i>
              <span><EMAIL></span>
            </div>
            <div class="contact-item">
              <i class="fas fa-phone"></i>
              <span>010-68701159</span>
            </div>
            <div class="contact-item">
              <i class="fas fa-map-marker-alt"></i>
              <span>{{ currentLanguage === 'chinese' ? '北京市紫竹院南路17号' : 'No.17， Zizhuyuan Nanlu，Beijing，China' }}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="footer-bottom">
        <div>
          <p>&copy; 2025 ICMCI {{ currentLanguage === 'chinese' ? '上海国际管理咨询大会 保留所有权利' : 'Shanghai International Management Consulting Conference. All rights reserved'}}</p>
        </div>
        <div> 
          <span>ICP license: 13027772</span>
          <!-- <el-divider direction="vertical"></el-divider> -->
      </div>
      </div>
    </div>
  </footer>
</template>

<script>
import { useLanguage } from '@/hooks/useLanguage.js'
export default {
  name: 'AppFooter',
  setup() {
    const { getLocalePath, currentLanguage } = useLanguage()

    return {
      getLocalePath,
      currentLanguage
    }
  }

}
</script>

<style scoped>
/* 样式已在全局CSS中定义 */
.footer-logo {}

.footer-about {
  display: flex;

}

.footer-about img {
  margin-right: 20px;
}

.footer-links-list {
  display: flex;
  gap: 20px;
}

.footer-links-list ul {
  /* margin-right:20px; */

}
</style>
