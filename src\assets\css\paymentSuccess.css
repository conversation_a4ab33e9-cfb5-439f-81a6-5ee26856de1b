.conference-overview-container{
    background: url('@/assets/images/home-bg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    min-height: 100vh;
    
  }
.success-section {
    padding: 120px 0 80px;
    /* background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%); */
  }
  
  .success-container {
    max-width: 800px;
    margin: 0 auto;
    background-color: var(--bg-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: 50px;
    text-align: center;
  }
  
  .success-icon {
    font-size: 80px;
    color: var(--success-color);
    margin-bottom: 20px;
  }
  
  .success-container h1 {
    font-size: 32px;
    margin-bottom: 15px;
    color: var(--success-color);
  }
  
  .success-message {
    font-size: 18px;
    color: var(--text-light);
    margin-bottom: 30px;
  }
  
  .order-info {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 40px;
    padding: 20px;
    background-color: var(--bg-light);
    border-radius: var(--radius-md);
  }
  
  .info-label {
    font-size: 14px;
    color: var(--text-light);
    margin-bottom: 5px;
  }
  
  .info-value {
    font-size: 16px;
    font-weight: 500;
  }
  
  .next-steps {
    margin-bottom: 40px;
  }
  
  .next-steps h2 {
    font-size: 24px;
    margin-bottom: 30px;
  }
  
  .steps-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }
  
  .step-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    text-align: left;
  }
  
  .step-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: rgba(30, 136, 229, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: var(--primary-color);
    flex-shrink: 0;
  }
  
  .step-content h3 {
    font-size: 18px;
    margin-bottom: 5px;
  }
  
  .step-content p {
    color: var(--text-light);
    font-size: 14px;
  }
  
  .action-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
  }
  
  .share-section {
    padding: 80px 0;
  }
  
  .share-container {
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
  }
  
  .share-container h2 {
    font-size: 24px;
    margin-bottom: 10px;
  }
  
  .share-container p {
    color: var(--text-light);
    margin-bottom: 30px;
  }
  
  .share-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 30px;
  }
  
  .share-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding: 15px;
    border-radius: var(--radius-md);
    border: none;
    cursor: pointer;
    transition: var(--transition);
    color: var(--text-white);
    width: 80px;
  }
  
  .share-button.wechat {
    background-color: #07C160;
  }
  
  .share-button.weibo {
    background-color: #E6162D;
  }
  
  .share-button.linkedin {
    background-color: #0077B5;
  }
  
  .share-button.email {
    background-color: #F6A623;
  }
  
  .share-button:hover {
    transform: translateY(-5px);
  }
  
  .share-button i {
    font-size: 24px;
  }
  
  .share-link {
    display: flex;
    max-width: 500px;
    margin: 0 auto;
  }
  
  .share-link input {
    flex: 1;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-right: none;
    border-radius: var(--radius-sm) 0 0 var(--radius-sm);
    background-color: var(--bg-light);
  }
  
  .copy-button {
    padding: 12px 20px;
    background-color: var(--primary-color);
    color: var(--text-white);
    border: none;
    border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .copy-button:hover {
    background-color: #1565c0;
  }
  
  @media (max-width: 768px) {
    .success-container {
      padding: 30px 20px;
    }
    
    .order-info {
      flex-direction: column;
      gap: 15px;
    }
    
    .steps-grid {
      grid-template-columns: 1fr;
    }
    
    .action-buttons {
      flex-direction: column;
      gap: 15px;
    }
    
    .share-buttons {
      flex-wrap: wrap;
    }
  }