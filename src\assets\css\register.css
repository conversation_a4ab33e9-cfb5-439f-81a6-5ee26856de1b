.conference-overview-container{
    background: url('@/assets/images/home-bg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    min-height: 100vh;
    
  }
  .page-header {
    background: linear-gradient(rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.3)), url('@/assets/images/dahui.png');
    background-size: cover;
    background-position: center;
  
    height: 200px;
    text-align: center;
    margin-top: 80px;
  }
  
  .page-header h1 {
    font-size: 36px;
    margin-bottom: 10px;
  }
  
  .register-section {
    padding: 80px 0;
    /* background-color: var(--bg-light); */
  }
  
  .auth-container {
    display: grid;
    grid-template-columns: 2fr ;
    max-width: 1000px;
    margin: 0 auto;
    background-color: var(--bg-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
  }
  
  .auth-form {
    padding: 20px;
  }
  
  .auth-form h2 {
    font-size: 24px;
    margin-bottom: 10px;
    color: var(--primary-color);
  }
  
  .auth-desc {
    color: var(--text-light);
    margin-bottom: 30px;
  }
  
  .password-input {
    position: relative;
  }
  
  .password-toggle {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
  }
  
  .password-strength {
    margin-top: 10px;
  }
  
  .strength-bar {
    height: 5px;
    background-color: #e0e0e0;
    border-radius: 5px;
    overflow: hidden;
    margin-bottom: 5px;
  }
  
  .strength-level {
    height: 100%;
    transition: width 0.3s ease;
  }
  
  .strength-text {
    font-size: 12px;
    text-align: right;
  }
  
  .strength-weak {
    background-color: #f44336;
    color: #f44336;
  }
  
  .strength-medium {
    background-color: #ff9800;
    color: #ff9800;
  }
  
  .strength-good {
    background-color: #2196f3;
    color: #2196f3;
  }
  
  .strength-strong {
    background-color: #4caf50;
    color: #4caf50;
  }
  
  .password-match {
    margin-top: 10px;
    font-size: 12px;
  }
  
  .match-success {
    color: #4caf50;
  }
  
  .match-error {
    color: #f44336;
  }
  
  .checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    cursor: pointer;
  }
  
  .checkbox-label span {
    font-size: 14px;
  }
  
  .checkbox-label a {
    color: var(--primary-color);
  }
  
  .btn-block {
    width: 100%;
  }
  
  .auth-divider {
    position: relative;
    text-align: center;
    margin: 30px 0;
  }
  
  .auth-divider::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background-color: var(--border-color);
  }
  
  .auth-divider span {
    position: relative;
    background-color: var(--bg-color);
    padding: 0 15px;
    color: var(--text-light);
  }
  
  .social-login {
    margin-bottom: 30px;
  }
  
  .social-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
  }
  
  .auth-footer {
    text-align: center;
    color: var(--text-light);
  }
  
  .auth-footer a {
    color: var(--primary-color);
    font-weight: 500;
  }
  
  .auth-info {
    background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('@/assets/images/dahui.png');
    background-size: cover;
    background-position: center;
    color: var(--text-white);
    text-align: center;
  }
  
  .info-content h2 {
    font-size: 24px;
    margin-bottom: 30px;
  }
  
  .benefits-list {
    list-style: none;
    padding: 0;
    margin: 0 0 40px;
  }
  
  .benefits-list li {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 20px;
  }
  
  .benefits-list i {
    color: #4caf50;
    font-size: 20px;
    margin-top: 2px;
  }
  
  .testimonial {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    padding: 20px;
  }
  
  .testimonial-content {
    margin-bottom: 20px;
    font-style: italic;
  }
  
  .testimonial-author {
    display: flex;
    align-items: center;
    gap: 15px;
  }
  
  .author-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
  }
  
  .author-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .author-name {
    font-weight: 500;
  }
  
  .author-title {
    font-size: 14px;
    opacity: 0.8;
  }
  
  .modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  
  .modal-content {
    background-color: var(--bg-color);
    border-radius: var(--radius-md);
    width: 90%;
    max-width: 700px;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
  }
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
  }
  
  .modal-header h3 {
    margin: 0;
  }
  
  .modal-close {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: var(--text-light);
  }
  
  .modal-body {
    padding: 40px;
    overflow-y: auto;
  }
  
  .modal-body h4 {
    margin-top: 20px;
    margin-bottom: 10px;
  }
  
  .modal-body p {
    margin-bottom: 15px;
  }
  
  .modal-body ul {
    padding-left: 20px;
    margin-bottom: 15px;
  }
  
  .modal-footer {
    padding: 20px;
    border-top: 1px solid var(--border-color);
    text-align: right;
  }
  
  @media (max-width: 992px) {
    .auth-container {
      grid-template-columns: 1fr;
    }
  
    .auth-info {
      display: none;
    }
  }
  
  @media (max-width: 576px) {
    .auth-form {
      padding: 30px 20px;
    }
  }
  
  .verification-code {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }