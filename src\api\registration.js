import http from '@/utils/http'

// 提交报名信息
export function submitRegistration(data) {
  return http.post('/meeting/signUpInfo/insertSignUpInfo',data)

}

// 提交同行人信息

export function getRegistrationInfo(id) {
  return http.get('/meeting/signUpInfo/getInfoByUserId?userId='+id)
}
export function generateOrderNum() {
  return http.get('/meeting/signUpInfo/generateOrderNum')
}
export function updateRegistration(data) {
  return http.post('/meeting/signUpInfo/updateSignUpInfo',data)
}
export function updatePeerInfo(data) {
  return http.post('/meeting/peerInfo/updatePeerInfo',data)
}

export function submitCompanion(data) {
  return http.post('/meeting/peerInfo/insertPeerInfo',data)
}

// 获取同行人信息
export function getCompanionInfo(signUpInfoId) {
  return http.get(`/meeting/peerInfo/getById?id=${signUpInfoId}`)
}

// 删除报名
export function deleteSignUpInfo(signUpInfoId) {
  return http.get(`/meeting/signUpInfo/deleteSignUpInfo?id=${signUpInfoId}`)
}
// 取消报名
export function cancelSignInfo(signUpInfoId) {
  return http.get(`/meeting/signUpInfo/cancelSignInfo?id=${signUpInfoId}`)
}
export function deletePeerInfo(id) {
  return http.get(`/meeting/peerInfo/deletePeerInfo?id=${id}`)
}

export function passportInfo(data) {
  return http.post('/meeting/signUpInfo/repeatVerify',data)
}
