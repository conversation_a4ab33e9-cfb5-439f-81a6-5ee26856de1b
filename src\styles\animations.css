/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeInDown {
  from {
    transform: translateY(-30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes zoomIn {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.1);
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes shimmer {
  to {
    background-position: -200% 0;
  }
}

/* 动画类 */
.animate-fade-in {
  animation: fadeIn 0.5s ease forwards;
}

.animate-fade-in-up {
  animation: fadeInUp 0.5s ease forwards;
}

.animate-fade-in-down {
  animation: fadeInDown 0.5s ease forwards;
}

.animate-zoom-in {
  animation: zoomIn 0.3s ease forwards;
}

.animate-slide-in-down {
  animation: slideInDown 0.5s ease forwards;
}

.animate-slide-in-up {
  animation: slideInUp 0.5s ease forwards;
}

/* 图片加载占位符 */
.image-placeholder {
  background: linear-gradient(110deg, #ececec 8%, #f5f5f5 18%, #ececec 33%);
  background-size: 200% 100%;
  animation: shimmer 1.5s linear infinite;
}
