<template>
  <div class="home-page">
    <!-- 英雄区域 -->
    <div class="conference-overview-container">
    <section class="hero-section">
      <div class="hero-background">
        <img src="@/assets/images/banner.jpg" alt="ICMCI 2025 Shanghai" class="img">
       
      </div>
     
      <div class="hero-content"> 
        <!-- <h1 class="main-title">CMC 2025 中国·上海</h1>
        <p class="subtitle">国际注册管理咨询师大会</p> -->
        <div class="event-details">
          <div>
            <i class="fas fa-calendar"></i>
            <span>2025年10月21-24日</span>
          </div>
          <div>
            <i class="fas fa-map-marker-alt"></i>
            <span>上海香格里拉大酒店</span>
          </div>
        </div>
        <div class="cta-buttons">
          <router-link :to="getLocalePath('registration')" class="btn btn-primary">
            立即报名
          </router-link>
          <router-link :to="getLocalePath('agenda')" class="btn btn-outline">
            查看议程
          </router-link>
        </div>
        <Countdown targetDate="2025-10-23T09:00:00" />
      </div>
    </section>

    <!-- 会议概览 -->
    <section class="conference-overview" v-intersection-observer="handleOverviewIntersection">
      <div class="container">
        <h2 class="section-title" :class="{ 'animate__animated animate__fadeInDown': overviewVisible }">会议概览</h2>
        <div class="overview-grid">
          <div 
            class="overview-card"
            :class="{ 'animate__animated animate__fadeInLeft': overviewVisible }"
            :style="{ animationDelay: '0.2s' }"
          >
            <div class="card-icon">
              <img src="https://picsum.photos/60/60?random=20" alt="国际CMC大会">
            </div>
            <h3>国际CMC大会</h3>
            <p>全球管理咨询行业的顶级盛会</p>
            <!-- <router-link to="/about#cmc" class="read-more">了解更多</router-link> -->
          </div>
          <div 
            class="overview-card"
            :class="{ 'animate__animated animate__fadeInRight': overviewVisible }"
            :style="{ animationDelay: '0.4s' }"
          >
            <div class="card-icon">
              <img src="https://picsum.photos/60/60?random=21" alt="ICMCI年会">
            </div>
            <h3>ICMCI年会</h3>
            <p>国际管理咨询师协会联合会年度会议</p>
            <!-- <router-link to="/about#icmci" class="read-more">了解更多</router-link> -->
          </div>
        </div>
      </div>
    </section>

    <!-- 主题介绍 -->
    <section class="theme-section" v-intersection-observer="handleThemeIntersection">
      <div class="container">
        <h2 class="section-title" :class="{ 'animate__animated animate__fadeInDown': themeVisible }">会议主题</h2>
        <div class="theme-content">
          <h3 :class="{ 'animate__animated animate__fadeInLeft': themeVisible }">数智引领未来，咨询助推管理创新与可持续发展</h3>
          <p :class="{ 'animate__animated animate__fadeInUp': themeVisible }" :style="{ animationDelay: '0.3s' }">数字化不仅对产业对企业对社会都会产生深远的影响，人工智能（AI）无疑成为了最具变革性力量的领域之一。企业如何应对新一轮挑战？管理咨询如何迅速融入新浪潮，创新管理理念，研发新工具，助力企业实现高质量可持续发展。</p>
          
          <div class="forums-grid">
            <div 
              v-for="(forum, index) in 1" 
              :key="index"
              class="forum-item"
              :class="{ 'animate__animated animate__fadeInUp': themeVisible }"
              :style="{ animationDelay: `${0.4 + index * 0.1}s` }"
            >
              <div class="forum-content">
                <span class="forum-number">{{ index + 1 }}</span>
                <h4>各国CMC交流管理咨询经验，探讨管理咨询新趋势</h4>
                <!-- <p>对企业管理的影响及挑战</p> -->
                <!-- <div class="forum-price">
                  <span class="price-label">分论坛价格</span>
                  <span class="price-value">¥1,200</span>
                </div> -->
              </div>
              <!-- <a href="forum-detail.html?id=ai-industry" class="forum-link">
                <span>查看详情</span>
                <i class="fas fa-arrow-right"></i>
              </a> -->
            </div>
            <div 
              v-for="(forum, index) in 1" 
              :key="index + 1"
              class="forum-item"
              :class="{ 'animate__animated animate__fadeInUp': themeVisible }"
              :style="{ animationDelay: `${0.4 + index * 0.1}s` }"
            >
              <div class="forum-content">
                <span class="forum-number">{{ index + 2 }}</span>
                <h4>探讨合作机会，推动企业提升管理水平</h4>
                <!-- <p>跨组织跨领域跨学科协同</p> -->
                <!-- <div class="forum-price">
                  <span class="price-label">分论坛价格</span>
                  <span class="price-value">¥1,200</span>
                </div> -->
              </div>
              <!-- <a href="forum-detail.html?id=management-governance" class="forum-link">
                <span>查看详情</span>
                <i class="fas fa-arrow-right"></i>
              </a> -->
            </div>
            <div 
              v-for="(forum, index) in 1" 
              :key="index + 10"
              class="forum-item"
              :class="{ 'animate__animated animate__fadeInUp': themeVisible }"
              :style="{ animationDelay: `${0.4 + index * 0.1}s` }"
            >
              <div class="forum-content">
                <span class="forum-number">{{ index + 3 }}</span>
                <h4>颁发国际管理咨询案例奖（君士坦丁奖）</h4>
                <!-- <p>技术与咨询的深度融合</p> -->
                <!-- <div class="forum-price">
                  <span class="price-label">分论坛价格</span>
                  <span class="price-value">¥1,200</span>
                </div> -->
              </div>
              <!-- <a href="forum-detail.html?id=ai-consulting" class="forum-link">
                <span>查看详情</span>
                <i class="fas fa-arrow-right"></i>
              </a> -->
            </div>
            <div 
              v-for="(forum, index) in 1" 
              :key="index + 15"
              class="forum-item"
              :class="{ 'animate__animated animate__fadeInUp': themeVisible }"
              :style="{ animationDelay: `${0.4 + index * 0.1}s` }"
            >
              <div class="forum-content">
                <span class="forum-number">{{ index + 4 }}</span>
                <h4>总结过去一年工作,主要包括各成员国组织交流经验、战略执行、会员发展、业务项目进展及财务决算；</h4>
                <!-- <p>ESG方向</p> -->
                <!-- <div class="forum-price">
                  <span class="price-label">分论坛价格</span>
                  <span class="price-value">¥1,200</span>
                </div> -->
              </div>
              <!-- <a href="forum-detail.html?id=esg" class="forum-link">
                <span>查看详情</span>
                <i class="fas fa-arrow-right"></i>
              </a> -->
            </div>
            <div 
              v-for="(forum, index) in 1" 
              :key="index + 20"
              class="forum-item"
              :class="{ 'animate__animated animate__fadeInUp': themeVisible }"
              :style="{ animationDelay: `${0.4 + index * 0.1}s` }"
            >
              <div class="forum-content">
                <span class="forum-number">{{ index + 5 }}</span>
                <h4>落实新一年工作任务，重点项目推动，审议财务预算，增补执委会、相关专业委员会委员和理事等</h4>
                <!-- <p>中国管理咨询牵引企业走出去（典型案例分享）</p> -->
                <!-- <div class="forum-price">
                  <span class="price-label">分论坛价格</span>
                  <span class="price-value">¥1,200</span>
                </div> -->
              </div>
              <!-- <a href="forum-detail.html?id=china-practice" class="forum-link">
                <span>查看详情</span>
                <i class="fas fa-arrow-right"></i>
              </a> -->
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 会场展示 -->
    <section class="venue-section" v-intersection-observer="handleVenueIntersection">
      <div class="container">
        <h2 class="section-title" :class="{ 'animate__animated animate__fadeInDown': venueVisible }">会议场地</h2>
        <div 
          class="venue-slider"
          :class="{ 'animate__animated animate__fadeInUp': venueVisible }"
          :style="{ animationDelay: '0.3s' }"
        >
          <div class="slider-container" ref="sliderContainer">
            <div class="slide" v-for="(slide, index) in venueSlides" :key="index" :class="{ 'active': currentSlide === index }">
              <img :src="slide.image" :alt="slide.alt">
            </div>
          </div>
          <div class="slider-controls">
            <button class="prev-btn" @click="prevSlide"><i class="fas fa-chevron-left"></i></button>
            <div class="slider-dots">
              <span
                v-for="(slide, index) in venueSlides"
                :key="index"
                class="dot"
                :class="{ 'active': currentSlide === index }"
                @click="goToSlide(index)"
              ></span>
            </div>
            <button class="next-btn" @click="nextSlide"><i class="fas fa-chevron-right"></i></button>
          </div>
        </div>
        <div 
          class="venue-info"
          :class="{ 'animate__animated animate__fadeInUp': venueVisible }"
          :style="{ animationDelay: '0.5s' }"
        >
          <h3>上海浦东香格里拉大酒店</h3>
          <p>位于陆家嘴金融贸易区的核心地带，毗邻黄浦江畔，拥有绝佳的城市景观。酒店提供一流的会议设施和专业的服务团队，是举办国际会议的理想场所。</p>
          <div class="venue-details">
            <div class="detail-item">
              <i class="fas fa-map-marker-alt"></i>
              <span>上海市浦东新区富城路33号</span>
            </div>
            <div class="detail-item">
              <i class="fas fa-phone"></i>
              <span>+86 21 6882 8888</span>
            </div>
            <div class="detail-item">
              <i class="fas fa-envelope"></i>
              <span><EMAIL></span>
            </div>
          </div>
          <router-link :to="getLocalePath('accommodation')" class="btn btn-secondary">住宿详情</router-link>
        </div>
      </div>
    </section>

    <!-- 快速报名 -->
    <section class="quick-register">
      <div class="container">
        <div class="register-card">
          <h2>立即报名参加 CMC&ICMCI 2025上海</h2>
          <p>把握机会，与全球管理咨询专家共同探讨行业未来</p>
          <router-link :to="getLocalePath('registration')" class="btn btn-primary btn-large">
            <span class="btn-content">
              <i class="fas fa-arrow-right"></i>
              开始报名
              <i class="fas fa-arrow-right"></i>
            </span>
          </router-link>
       </div>
      </div>
    </section>
  </div>
  </div>
</template>

<script>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import Countdown from '../../components/common/Countdown.vue'
import { useLanguage } from '@/hooks/useLanguage'
import 'animate.css'
import shanghai01 from '@/assets/images/shanghai01.jpg'
import shanghai02 from '@/assets/images/shanghai02.jpg'
import shanghai03 from '@/assets/images/shanghai03.jpg'
import shanghai04 from '@/assets/images/shanghai04.jpg'

export default {
  name: 'HomePage',
  components: {
    Countdown
  },
  directives: {
    intersectionObserver: {
      mounted(el, binding) {
        let lastScrollY = window.scrollY
        let hasAnimated = false

        const observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            const currentScrollY = window.scrollY
            const isScrollingDown = currentScrollY > lastScrollY

            if (entry.isIntersecting) {
              // 向下滚动时触发动画
              if (isScrollingDown) {
                binding.value(true)
                hasAnimated = true
              }
            } else if (isScrollingDown) {
              // 向下滚动时保持可见
              binding.value(true)
            } else {
              // 向上滚动时重置状态
              binding.value(false)
              hasAnimated = false
            }

            lastScrollY = currentScrollY
          })
        }, {
          threshold: 0.1,
          rootMargin: '0px'
        })

        observer.observe(el)
      }
    }
  },
  setup() {
    const { getLocalePath } = useLanguage()
    const currentSlide = ref(0)
    let slideInterval = null

    // 添加动画状态控制
    const overviewVisible = ref(false)
    const themeVisible = ref(false)
    const venueVisible = ref(false)
    const registerVisible = ref(false)

    // 处理各个部分的可见性
    const handleOverviewIntersection = (isVisible) => {
      overviewVisible.value = isVisible
    }

    const handleThemeIntersection = (isVisible) => {
      themeVisible.value = isVisible
    }

    const handleVenueIntersection = (isVisible) => {
      venueVisible.value = isVisible
    }

    const handleRegisterIntersection = (isVisible) => {
      registerVisible.value = isVisible
    }

    // 会场轮播图数据
    const venueSlides = [
        {
        image: shanghai01,
        alt: '上海浦东香格里拉大酒店'
      },
      {
        image: shanghai02,
        alt: '上海浦东香格里拉大酒店'
      },
      {
        image: shanghai03,
        alt: '上海浦东香格里拉大酒店'
      },
      {
        image: shanghai04,
        alt: '上海浦东香格里拉大酒店'
      }
    ]

    // 轮播图控制函数
    const nextSlide = () => {
      currentSlide.value = (currentSlide.value + 1) % venueSlides.length
    }

    const prevSlide = () => {
      currentSlide.value = (currentSlide.value - 1 + venueSlides.length) % venueSlides.length
    }

    const goToSlide = (index) => {
      currentSlide.value = index
    }

    // 自动轮播
    const startSlideShow = () => {
      slideInterval = setInterval(() => {
        nextSlide()
      }, 5000)
    }

    onMounted(() => {
      startSlideShow()
    })

    onBeforeUnmount(() => {
      if (slideInterval) {
        clearInterval(slideInterval)
      }
    })

    return {
      venueSlides,
      currentSlide,
      nextSlide,
      prevSlide,
      goToSlide,
      getLocalePath,
      overviewVisible,
      themeVisible,
      venueVisible,
      registerVisible,
      handleOverviewIntersection,
      handleThemeIntersection,
      handleVenueIntersection,
      handleRegisterIntersection
    }
  }
}
</script>

<style scoped>
@import url('@/assets/css/home.css');
</style>
